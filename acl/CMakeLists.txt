include_directories(.)
add_definitions(-DENABLE_DVPP_INTERFACE)
include(${CMAKE_SOURCE_DIR}/cmake/common_src.cmake)
list(APPEND SRC_FILES ${SEC_SRC}
        ${PROJECT_SOURCE_DIR}/common/vega_plate_filter.cpp
        ${PROJECT_SOURCE_DIR}/common/Platform/interface/vega_interface.cpp
        ${PROJECT_SOURCE_DIR}/common/Platform/engine/vega_device_engine.cpp
        ${PROJECT_SOURCE_DIR}/common/Platform/engine/vega_host_engine.cpp
        srv/vega_acl.cpp
        srv/acl_stream_impl.cpp
        srv/video_device_encoder.cpp
        srv/video_device_decoder.cpp
        srv/image_device_decoder.cpp
        srv/image_device_encoder.cpp
        ${PROJECT_SOURCE_DIR}/common/Platform/vega_stream_impl.cpp
        ${PROJECT_SOURCE_DIR}/common/utils/jpeg_exif.cpp
        ${PROJECT_SOURCE_DIR}/common/utils/jpegd_check/JpegdCheck.cpp
        ${PROJECT_SOURCE_DIR}/common/utils/jpegd_check/jdmarker.cpp
        ${PROJECT_SOURCE_DIR}/common/utils/jpegd_check/jpeglib.cpp
        )
if(CANN50)
list(APPEND SRC_FILES 
        srv/aicore_op_engine.cpp
        utils/operator_desc.cpp
        utils/aipp.cpp
)
endif()
        
list(APPEND VEGA_DEPEND_LIBS
        -lascendcl
        )
list(APPEND VEGA_DEPEND_LIBS
        -lacl_dvpp
)

if(CANN50 AND ACL_MEDIA_V2)
        list(APPEND SRC_FILES 
                v2/srv/video_device_decoder.cpp
                v2/srv/video_device_encoder.cpp
                v2/srv/image_device_decoder.cpp
                v2/srv/image_device_encoder.cpp
                v2/srv/vpc_device.cpp
        )
        list(APPEND VEGA_DEPEND_LIBS
                -lacl_dvpp_mpi
        )
endif()

add_library(${PROJECT_NAME} SHARED ${SRC_FILES})

target_link_libraries(${PROJECT_NAME} PUBLIC
        ${VEGA_DEPEND_LIBS}
        ${SEC_LIBS}
        )



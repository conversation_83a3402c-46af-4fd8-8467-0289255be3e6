#ifndef VEGA_ACL_IMAGE_DEVICE_DECODER_V2_H
#define VEGA_ACL_IMAGE_DEVICE_DECODER_V2_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "srv/dvpp_common.h"
#include "rpc/rpc_processor.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "channel_manage.h"
namespace vega
{
    namespace acl
    {
        class AclImageDecoderV2: public AclImageDecoderBase
        {
        public:
            AclImageDecoderV2()
            {
            }
            ~AclImageDecoderV2();
            AclImageDecoderV2 &
            operator=(const AclImageDecoderV2 &) = delete;

            DgError JpegDecode(const MatrixSP &input, MatrixSP &output, MatrixType outputType);
            static DgError createChannel(int& chn);
            static DgError destroyChannel(int chn);

        private:
            DgError CombineJpegDecProcess(const MatrixSP &input, MatrixSP &output, int chn);
        private:
        };

    }
}

#endif

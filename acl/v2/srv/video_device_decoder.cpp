#include "video_device_decoder.h"
#include <stdio.h>
#include <sys/prctl.h>
#include <sys/time.h>
namespace vega {
namespace acl {

static AclChannelManageV2 aclChannelManage(256);
void *AclVDecProcV2::AclReportThread_(void *arg) {
    prctl(PR_SET_NAME, "AclDecReportThread");
    auto aclVenc = static_cast<AclVDecProcV2 *>(arg);
    if (aclVenc != nullptr) {
        return aclVenc->recvStreamThread();
    }
    return nullptr;
}

DgError AclVDecProcV2::GetCheckCBFrame(VegaTmPnt tp_now,HI_FRAME_SP &frameSp, uint64_t frame_id,uint32_t   frame_flag,bool check) {
    std::map<FrameId, HI_FRAME_SP>::iterator it;
    cb_frame_mutex_.lock();
    double dec_du=0;
    if (frame_id > 0) {
        it = cb_frame_.find(frame_id);
        if (it == cb_frame_.end()) {
            LOG(ERROR) << "Can not find the decoder callback function,ch=" << channelId_ << " pts=" << frame_id << ",decResult "
                       << frame_flag;
            cb_frame_mutex_.unlock();
            return DG_ERR_NOT_EXIST;
        }
        frameSp = it->second;
        dec_du=tp_now-it->second.start_time;
        cb_frame_.erase(it);
    }
    if (check) {
        std::vector<HI_FRAME_SP> frameSpV;
        std::stringstream du_log;
        bool print_log=false;
        du_log<<"frame_cb  streamid " << streamid_ <<", size "<<cb_frame_.size();
        if(frame_id>0){
            du_log<<",dec id "<<frame_id<<",du "<<dec_du;
        }

        for (it = cb_frame_.begin(); it != cb_frame_.end();) {
            auto du=tp_now-it->second.start_time;
            du_log<<",frame_id "<<it->first<<",du "<<du;
            if(du>2000){
                print_log=true;
            }
            if (frame_id > it->first) {
                if (frame_id - it->first > 25 * 60) {
                    frameSpV.push_back(it->second);
                    LOG(ERROR) << "streamid " << streamid_ << " ,frame_id " << it->first << ",cur_id " << frame_id << ", videc time out!";
                    cb_frame_.erase(it++);
                } else {
                    it++;
                }
            } else {
                it++;
            }
        }
        cb_frame_mutex_.unlock();
        if(print_log){
            LOG(WARNING)<<""<<du_log.str();
        }
        if (frameSpV.size() > 0)
            for (auto frame : frameSpV) {
                frame.cb(nullptr, DG_ERR_TIME_OUT);
            }
    } else {
        cb_frame_mutex_.unlock();
    }
    return DG_OK;
}
/// @brief  循环接收视频解码结果
/// @param args AclVDecProc指针
/// @return
void *AclVDecProcV2::recvStreamThread(void) {
    auto ret = aclrtSetCurrentContext(context_);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "thread_routine set context error. " << std::hex << ret;
        return nullptr;
    }
    hi_vdec_stream stream;
    hi_video_frame_info frame;
    hi_vdec_supplement_info stSupplement;
    uint64_t fame_id;
    uint64_t get_frame_times=0;
    HI_FRAME_SP frameSp;
    VegaTmPnt tp_now;
    VegaTmPnt tp_pre;
    tp_now.mark();
    tp_pre.mark();
    while (1) {
        std::map<FrameId, HI_FRAME_SP>::iterator it;
        stream.pts=0;
        ret = hi_mpi_vdec_get_frame(channelId_, &frame, &stSupplement, &stream, timeOut_);
        get_frame_times++;
        get_frame_run_log_=",get_frame_times "+std::to_string(get_frame_times);
        fame_id=stream.pts;
        bool check=false;
        if(get_frame_times%25==0){
            tp_now.mark();
            auto du=tp_now-tp_pre;
            if (du > 10 * 1000) {
                tp_pre.mark();
                check=true;
            }
        }
        if (ret != (hi_s32)HI_ERR_VDEC_BUF_EMPTY){
            if(DG_OK!=GetCheckCBFrame(tp_now,frameSp, fame_id,frame.v_frame.frame_flag,check)){
                frame_count_++; 
                continue;
            }
        }
        if (ret == HI_SUCCESS) {
            auto decResult = frame.v_frame.frame_flag;
            if (frameSp.status == SKIP) {
                frameSp.cb(nullptr, DG_OK);
            } else if (frameSp.status == FULL) {
                frameSp.cb(nullptr, DG_ERR_FULL);
            } else {// DECODE
                 bool discard_frame=false;
                if(frameSp.status==STRATEGY) {
                    discard_frame = VideoBuffer::isDiscard(vdecConf_.discardInterval, frame_count_);
                }
                if(discard_frame){
                    frameSp.cb(nullptr, DG_OK);
                }else {
                    if (decResult == 0) {
                        auto width = DVPP_ALIGN_UP(videoWidth_, MIN_VDEC_ALIGN),
                        height = DVPP_ALIGN_UP(videoHeight_, MIN_VDEC_ALIGN),
                        width_stride = DVPP_ALIGN_UP(videoWidth_, 16),
                        height_stride = DVPP_ALIGN_UP(videoHeight_, MIN_VDEC_ALIGN),
                        buffer_size = width_stride * height_stride * 3 / 2;
                        auto dst = std::make_shared<AclImage>();
                        auto err = dst->create(
                            MatrixType::NV12, cv::Size(width, height),frameSp.outMem, 1, width_stride, height_stride);
                        if (err != DG_OK) {
                            LOG(ERROR) << "Failed to create output frame for image";
                        }
                        if (dst->reqMemSize() > (int)buffer_size) {
                            LOG(ERROR) << "Fail because dataSize " << buffer_size << " < reqMemSize " << dst->reqMemSize()
                                   << " !";
                        }
                        if (err != DG_OK)
                            dst = nullptr;
                        frameSp.cb(dst, err);
                    } else if (decResult == 2) {
                        frameSp.cb(nullptr, DG_ERR_VDEC_NOPIC);
                    } else {
                        LOG(ERROR)<<"vdec fail,streamid "<<streamid_<<" ,fame_id "<<fame_id<<",ret "<<decResult;
                        frameSp.cb(nullptr, DG_ERR_VDEC_FAIL);
                    }
                }
            }
            ret = hi_mpi_vdec_release_frame(channelId_, &frame);
            if (ret != HI_SUCCESS) {
                LOG(ERROR) << "free frame " << fame_id << "failed " << std::hex << ret;
            }
            frame_count_++;
        } else if (ret == (hi_s32)HI_ERR_VDEC_BUF_EMPTY) {
            if(check)
                GetCheckCBFrame(tp_now,frameSp, 0,0,check);
            thread_destroy_mutex_.lock();
            if (thread_destroy_) {
                thread_destroy_mutex_.unlock();
                LOG(ERROR) << "Vdec receive thread exit, channel id: " << channelId_;
                pthread_exit(nullptr);
                break;
            }
            thread_destroy_mutex_.unlock();
        }else{
            LOG(ERROR) << "vdec failed ret = " << std::hex << ret;
            frameSp.cb(nullptr, DG_ERR_VDEC_FAIL);
            ret = hi_mpi_vdec_release_frame(channelId_, &frame);
            if (ret != HI_SUCCESS) {
                LOG(ERROR) << "free frame " << fame_id << "failed " << std::hex << ret;
            }
        }
        frameSp.inMem=nullptr;
        frameSp.outMem=nullptr;
    }
    return nullptr;
}

DgError AclVDecProcV2::Init(const aclrtStream &stream, uint32_t channelId) {
    vdecConf_.bSet = false;
    std::string acl_soc_name=aclrtGetSocName();
    if(acl_soc_name.compare("Ascend310P1")==0){
        is_acl_soc_=true;
    }
    frame_count_=0;
    return DG_OK;
}
DgError AclVDecProcV2::Deinit(void) {
    return DG_OK;
}

DgError AclVDecProcV2::vdec_reset_chn(uint32_t chanId)
{
    int32_t ret = HI_SUCCESS;
    send_frame_run_log_="hi_mpi_vdec_stop_recv_stream";
    ret = hi_mpi_vdec_stop_recv_stream(chanId);
    send_frame_run_log_="hi_mpi_vdec_stop_recv_stream out";
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "hi_mpi_vdec_stop_recv_stream Fail, channel " << chanId << " ,ret=" << std::hex << ret;
        return DG_ERR_HW_FAILURE;
    }
    // reset channel
    send_frame_run_log_="hi_mpi_vdec_reset_chn";
    ret = hi_mpi_vdec_reset_chn(channelId_);
    send_frame_run_log_="hi_mpi_vdec_reset_chn out";
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "hi_mpi_vdec_reset_chn Fail, channel " << chanId << " ,ret=" << std::hex << ret;
        return DG_ERR_HW_FAILURE;
    }

    send_frame_run_log_ = "hi_mpi_vdec_start_recv_stream";
    ret         = hi_mpi_vdec_start_recv_stream(channelId_);
    send_frame_run_log_ = "hi_mpi_vdec_start_recv_stream out";
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "hi_mpi_vdec_start_recv_stream Fail, channel " << chanId << " ,ret=" << std::hex << ret;
        return DG_ERR_HW_FAILURE;
    }
    return DG_OK;
}

void AclVDecProcV2::waitVdecEnd() {
    int32_t ret = HI_SUCCESS;
    int32_t waitTimes = 0;
    int32_t sleepTime = 10000;  // 10000us
    hi_vdec_chn_status status{};
    hi_vdec_chn_status pre_status{};
    // Wait channel decode over
    while (1) {

        send_frame_run_log_="hi_mpi_vdec_query_status";
        ret = hi_mpi_vdec_query_status(channelId_, &status);
        send_frame_run_log_="hi_mpi_vdec_query_status out";
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "hi_mpi_vdec_query_status  failed " << std::hex << ret;
            break;
        }
        if ((status.left_stream_bytes == 0) && (status.left_decoded_frames == 0)) {
            break;
        }
        if (status.left_decoded_frames == pre_status.left_decoded_frames) {
            waitTimes += sleepTime;
        } else {
            waitTimes = 0;
        }
        pre_status = status;
        // 10000us
        usleep(sleepTime);

        if (waitTimes >= 5000000) {  // 5000000 us
            if(vdec_reset_chn(channelId_) != DG_OK){
                LOG(ERROR) << "Time out for 5000000 us, but reset channel failed, channel id: " << channelId_ ;
                return ;
            }
            LOG(ERROR) << "Time out for 5000000 us, Reset channel success, channel id: " << channelId_ ;
            break;
        }
    }
    // 1000000us
    // usleep(1000000);
    // Notify get thread exit
}
/// @brief 使用device解码后，通过回调返回给上层
/// @param input  输入某帧 size[lenx1]
/// @param outputType 解码后的类型
/// @param cb 回调函数
/// @param vdecParam 视频信息
/// @param bEos 是否结束
/// @return
DgError AclVDecProcV2::DecodeVideo(MatrixSP input,
                                 MatrixType outputType,
                                 DecVideoCallback cb,
                                 const VideoDecParamSP &vdecParam,
                                 bool bEos) {
    /*             分辨率约束
                    输入码流分辨率：
                    最大分辨率4096*4096，最小分辨率128*128。
                    输出图像分辨率：
                    最大分辨率4096*4096，最小分辨率10*6。
                输入码流格式：
                    H264 bp/mp/hp level5.1 YUV420编码的码流，当前只支持annex-B的裸码流。
                    H265 8/10bit level5.1 YUV420编码的码流，当前只支持annex-B的裸码流。
                输出图片格式：
                    YUV420SP NV12 8bit 宽2对齐 高2对齐
                    宽stride为宽16对齐后的值，最小32，最大16384。
                    高stride为高2对齐后的值，最小6，最大16384。
                    内存大小（单位Byte）≥ 宽stride * 高stride * 3/2 */
    // LOG(ERROR) << "video decode v2";
    send_frame_run_log_="DecodeVideo in 0";
    if (bEos == true)  // end of stream
    {
        send_frame_run_log_="proc eos";
        VdecSendEosFrame();
        waitVdecEnd();
        DeInitVdec();
        frame_id_ = 1;
        send_frame_run_log_="proc out";
        return DG_OK;
    }

    if (nullptr == input || nullptr == vdecParam) {
        LOG(ERROR) << "Invalid input matrix or param";
        send_frame_run_log_="DecodeVideo out 1";
        return DG_ERR_INVALID_PARAM;
    }

    if (!vdecConf_.bSet) {
        // check vdecParam
        if (vdecParam->videoHeight * vdecParam->videoHeight == 0) {
            LOG(ERROR) << "Video's height and width is  essential";
            send_frame_run_log_="DecodeVideo out 2";
            return DG_ERR_INVALID_PARAM;
        }
        videoWidth_ = vdecParam->videoWidth;
        videoHeight_ = vdecParam->videoHeight;
        vdecConf_.fast_mode = vdecParam->fast_mode;
        vdecConf_.discardInterval = vdecParam->discardInterval;
        streamid_ = input->streamId();
        dataSize_ = DVPP_ALIGN_UP(videoWidth_, 16) * DVPP_ALIGN_UP(videoHeight_, MIN_VDEC_ALIGN) * 3 / 2;

        // input stream's type
        switch (input->type()) {
        case MatrixType::H264:
            inputStreamtype_ = HI_PT_H264;
            break;
        case MatrixType::H265:
            inputStreamtype_ = HI_PT_H265;
            break;
        default:
            LOG(ERROR) << "Invalid input video type " << (int)input->type() << " not support !!!";
            send_frame_run_log_="DecodeVideo out 3";
            return DG_ERR_INVALID_PARAM;
        }

        // check output type
        if (outputType != MatrixType::NV12) {
            LOG(ERROR) << "Invalid output video type " << (int)outputType << " not support !!!";
             send_frame_run_log_="DecodeVideo out 4";
            return DG_ERR_INVALID_PARAM;
        }
        outputFormat_ = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;

        //校验大小
        if (videoWidth_ && videoWidth_
            && (videoWidth_ < 128 || videoWidth_ > maxResolution_ || videoHeight_ < 128 || videoHeight_ > maxResolution_)) {
            LOG(ERROR) << "Input video width:" << videoWidth_ << ", height:" << videoHeight_
                   << ", VDEC support resolution form 128x128 to " << maxResolution_ << "x" << maxResolution_ << " .";
            send_frame_run_log_="DecodeVideo out 5";
            return DG_ERR_INVALID_PARAM;
        }

        if (DG_OK != InitVdec(vdecConf_)) {
            LOG(ERROR) << "InitVdec fail.";
            DeInitVdec();
            last_ch_erro_ = DG_ERR_DECODE_FATAL;
            send_frame_run_log_="DecodeVideo out 6";
            return DG_ERR_DECODE_FATAL;
        }
        vdecConf_.bSet = true;
        packet_sn_ = 0;
    }

    DecStatus status = DECODE;
    if (vdecParam->bIgnore == DSICARD_FRAME) {
        status = SKIP;
    }else if (vdecParam->bIgnore == NOCARE_FRAME) {
        status = STRATEGY;
    }

    // 初始化内存池
    if (!bRegAclFramePoolMap) {
        if (dataSize_ != 0) {
            auto &aclFramePoolMap = AclFramePoolMap::getInstance();
            if (DG_OK != aclFramePoolMap.RegStreamId(dataSize_, streamid_)) {
                LOG(ERROR) << "Fail to aclFramePoolMap.RegStreamId! mem_size " << vdecConf_.dataSize << " streamid_ "
                           << streamid_;
            }
            bRegAclFramePoolMap = true;
        }
    }

    // 8.发送码流
    // 8.1 申请输入内存
    auto inMemDev = std::make_shared<AclMemDvpp>();
    if(is_acl_soc_){
        RawStream data = RawStream(input->data(), [=](void *data) {
            });
        if(DG_OK!=inMemDev->create(data, input->memory()->size(), 1)){
            LOG(ERROR)<<"Fail to creat acl memory !";
             send_frame_run_log_="DecodeVideo out 7";
            return DG_ERR_VDEC_FAIL;
        }
    }else {
         send_frame_run_log_="alocat input mem";
        if(videoInDeviceMemHeap_){
            uint32_t size =input->memory()->size();
            unsigned char *ptr=(unsigned char *)videoInDeviceMemHeap_->alloc(size);
            if(ptr){
                RawStream data = RawStream(ptr, [=](void *data) {
                    if(videoInDeviceMemHeap_){
                        videoInDeviceMemHeap_->free(ptr,size);
                    }
                });
                if(DG_OK!=inMemDev->create(data, size, 1)){
                    LOG(ERROR)<<"Fail to creat acl memory !";
                }
            }else {
                LOG(WARNING)<<"Fail to allocate memory of size "<<size<<" from video device memory heap!";
            }
        }else{
            LOG(ERROR)<<"video device memory heap has not been initialized.";
        }
        auto err = inMemDev->fromHost(input->memory());
        send_frame_run_log_="alocat input mem out";
        if (err != DG_OK) {
            LOG(ERROR) << "Failed to copy form host to device. " << err;
             send_frame_run_log_="DecodeVideo out 8";
            return DG_ERR_VDEC_FAIL;
        }
    }

    // // 8.2 申请输出内存
    // // 输出图片数据占用的内存大小与输出图片格式有关
    // // 8.3 构造存放一帧输入码流信息的结构体
    hi_vdec_stream stream;
    stream.addr = inMemDev->blob();
    stream.len = inMemDev->size();
    stream.end_of_frame = HI_TRUE;
    stream.end_of_stream = HI_FALSE;  //是否发完所有码流 
    stream.pts = nextFid();

    // // 8.4 构造存放一帧输出结果信息的结构体
    hi_vdec_pic_info outPicInfo;
    outPicInfo.width = DVPP_ALIGN_UP(videoWidth_, MIN_VDEC_ALIGN);
    outPicInfo.height = DVPP_ALIGN_UP(videoHeight_, MIN_VDEC_ALIGN);
    outPicInfo.width_stride = DVPP_ALIGN_UP(videoWidth_, 16);
    outPicInfo.height_stride = DVPP_ALIGN_UP(videoHeight_, MIN_VDEC_ALIGN);
    outPicInfo.pixel_format = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;
    
    AclMemDvppSP mem=nullptr;
    if(status == DECODE || status== STRATEGY){
        send_frame_run_log_="aclFramePoolMap.PopFrame";
        auto &aclFramePoolMap = AclFramePoolMap::getInstance();  // 内存池用尽时，skip该帧，不能再申请空间!
        std::shared_ptr<AclFrameBuffer> framebuf = aclFramePoolMap.PopFrame(dataSize_);
        if (!framebuf) {
            LOG_EVERY_N(ERROR, 500) << "Fail to pop buffer from frame pool!";
            status = FULL;
        } else {
            stream.need_display = HI_TRUE;
            mem = std::shared_ptr<AclMemDvpp>(framebuf->getMem().get(), [=](void *) {
                auto &aclFramePoolMap1 = AclFramePoolMap::getInstance();
                aclFramePoolMap1.PushFrame(framebuf);
            });
            outPicInfo.vir_addr = (uint64_t)mem->blob();
            outPicInfo.buffer_size = dataSize_;
        }
        send_frame_run_log_="aclFramePoolMap.PopFrame out";
    }

    if (status == SKIP || status == FULL) {
        stream.need_display = HI_FALSE;
        outPicInfo.vir_addr = 0;
        outPicInfo.buffer_size = 0;
    }
    //缓存帧信息
    HI_FRAME_SP frameSp;
    frameSp.cb = cb;
    // 是否需要缓存输入以及输出信息
    frameSp.outMem = mem;
    frameSp.inMem = inMemDev;
    frameSp.status = status;
    frameSp.start_time.mark();
    cb_frame_mutex_.lock();
    cb_frame_[stream.pts] = frameSp;
    cb_frame_mutex_.unlock();

    hi_s32 ret;
    // // 8.5 发送一帧码流，尝试5000次
    int times = 0;

    do {
        send_frame_run_log_="v2 hi_mpi_vdec_send_stream";
        ret = hi_mpi_vdec_send_stream(channelId_, &stream, &outPicInfo, timeOut_);  //-1 阻塞 0 非阻塞 >0 超时
        send_frame_run_log_="v2 hi_mpi_vdec_send_stream out";
        if (ret != HI_SUCCESS) {
            times++;
            if (times >= 30) {
                HI_FRAME_SP frameSp;
                cb_frame_mutex_.lock();
                auto it=cb_frame_.find(stream.pts);
                frameSp=it->second;
                cb_frame_.erase(it);
                cb_frame_mutex_.unlock();
                send_frame_run_log_="vdec_reset_chn";
                if (vdec_reset_chn(channelId_) != DG_OK) {
                    LOG(ERROR) << "hi_mpi_vdec_send_stream failed for 30 times, but reset channel failed, channel id: " << channelId_;
                    send_frame_run_log_="vdec_reset_chn out ";
                    return DG_ERR_HW_FAILURE;
                }
                LOG(ERROR) << "hi_mpi_vdec_send_stream failed for 30 times, ret=" << std::hex << ret << ", but reset channel " << channelId_
                           << "successfully!";
                send_frame_run_log_="vdec_reset_chn out 2";
                return DG_ERR_HW_FAILURE;
            }
        }
    } while (ret != HI_SUCCESS);
    send_frame_run_log_="DecodeVideo out 10";
    return DG_OK;
}

/// @brief  创建通道，开启接收服务线程
/// @param config
/// @return
DgError AclVDecProcV2::InitVdec(const VdecConfig &config) {
    send_frame_run_log_="acl v2  initializing video decoder!";
    auto ret = aclrtGetCurrentContext(&context_);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to get current context." << std::hex << ret;
        return DG_ERR_INIT_FAIL;
    }

    // 5.创建通道 todo:解码协议修改

    //[0, 256)
    hi_vdec_chn_attr chnAttr;
    //图片宽、图片高、对齐参数、图片位宽、图片格式、压缩模式
    hi_pic_buf_attr buf_attr{ maxResolution_,      maxResolution_, 0,
                              HI_DATA_BIT_WIDTH_8, outputFormat_,  HI_COMPRESS_MODE_NONE };
    chnAttr.type = inputStreamtype_;         //输入码流格式
    chnAttr.mode = HI_VDEC_SEND_MODE_FRAME;  //码流发送方式
    chnAttr.pic_width = maxResolution_;      //最大像素
    chnAttr.pic_height = maxResolution_;
    chnAttr.stream_buf_size = chnAttr.pic_width * chnAttr.pic_height * 3 / 2;
    chnAttr.frame_buf_cnt = 0;  //至少解码图像帧存个数= 参考帧+显示帧+1=16+3+1
    chnAttr.frame_buf_size = hi_vdec_get_pic_buf_size(inputStreamtype_, &buf_attr);  //解码图像帧存大小 Byte
    // hi_vdec_get_pic_buf_size 获取解码图像需要的Buffer大小
    chnAttr.video_attr.ref_frame_num = 16;         //参考帧的数目 [0, 16]
    chnAttr.video_attr.temporal_mvp_en = HI_TRUE;  //是否支持时域运动矢量预测。
    chnAttr.video_attr.tmv_buf_size
      = hi_vdec_get_tmv_buf_size(inputStreamtype_,
                                 maxResolution_,
                                 maxResolution_);  //视频解码图像TMV（Temporal Motion Vector）缓存大小，以Byte为单位
    // hi_vdec_get_tmv_buf_size  获取解码图像需要的矢量预测缓冲区的大小，类型 宽 高
    channelId_=0;
    do{
        channelId_ = aclChannelManage.GetChannel(channelId_);
         if(channelId_ >= 0){
            ret = hi_mpi_vdec_create_chn(channelId_, &chnAttr);
            if (ret != HI_SUCCESS) {
                LOG(ERROR) << "Failed to create vdec channel " << channelId_ << ". " << std::hex << ret;
                aclChannelManage.PushChannel(channelId_, DG_ERR_INIT_FAIL);
                channelId_++;
            }else{
                break;
            }
         }
    }while(channelId_ > 0);
    if (channelId_ < 0) {
        LOG(ERROR) << "create video decode channel all failed ";
        return DG_ERR_INIT_FAIL;
    }
    
    // 6.设置通道属性
    hi_vdec_chn_param chnParam;
    ret = hi_mpi_vdec_get_chn_param(channelId_, &chnParam);
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Failed to  get channel param." << std::hex;
        return DG_ERR_INIT_FAIL;
    }

    chnParam.video_param.dec_mode = HI_VIDEO_DEC_MODE_IPB;           // 解码模式
    chnParam.video_param.compress_mode = HI_COMPRESS_MODE_HFBC;      // 解码图像压缩模式。
    chnParam.video_param.video_format = HI_VIDEO_FORMAT_TILE_64x16;  //解码图像数据格式。
    chnParam.display_frame_num = 3;                                  //解码后缓存图像帧数，默认值2
    chnParam.video_param.out_order = vdecConf_.fast_mode ? HI_VIDEO_OUT_ORDER_DEC : HI_VIDEO_OUT_ORDER_DISPLAY;     //解码图像输出顺序  Display sequence
    
    

    ret = hi_mpi_vdec_set_chn_param(channelId_, &chnParam);
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Failed to set channel param." << std::hex;
        return DG_ERR_INIT_FAIL;
    }

    // 7.解码器启动接收码流
    ret = hi_mpi_vdec_start_recv_stream(channelId_);
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Failed to start recv stream." << std::hex;
        return DG_ERR_INIT_FAIL;
    }

    thread_destroy_mutex_.lock();
    thread_destroy_ = false;
    thread_destroy_mutex_.unlock();
    pthread_create(&pthread_routine_, 0, AclReportThread_, (void *)this);

    AclMemDvppSP acl_mem=std::make_shared<AclMemDvpp>();
    uint32_t block_size=32*1024-1;//64*32*1024=2M=1hugepage;调用acldvppMalloc接口申请内存时，会对用户输入的size按向上对齐成32整数倍后，再多加32字节。
    uint32_t block_count=64;
    auto vega_ret=acl_mem->create(block_size*block_count);
    if(vega_ret!=DG_OK){
        LOG(ERROR)<<"Fail to create acl Mem,size "<<block_size*block_count;
    }else{
        videoInDeviceMemHeap_=std::shared_ptr<MemHeap>(new MemHeap(block_size,block_count,acl_mem->blob()),[=](void *p){
            auto mem=acl_mem;
            mem.reset();
            MemHeap *heap=(MemHeap *)p;
            delete heap;
        });
    }
    return DG_OK;
}
/// @brief  销毁通道、释放资源
/// @param
/// @return
DgError AclVDecProcV2::DeInitVdec(void) {
    thread_destroy_mutex_.lock();
    thread_destroy_ = true;
    thread_destroy_mutex_.unlock();
    //等待接收线程退出

    send_frame_run_log_="pthread_join";
    pthread_join(pthread_routine_, NULL);

    //销毁通道
    send_frame_run_log_="hi_mpi_vdec_stop_recv_stream";
    auto ret = hi_mpi_vdec_stop_recv_stream(channelId_);
    send_frame_run_log_="hi_mpi_vdec_stop_recv_stream out";
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Stop recv stream of channel " << channelId_ << " failed." << std::hex << ret;
        aclChannelManage.PushChannel(channelId_, ret);
        return DG_ERR_DEINIT_FAIL;
    }

    send_frame_run_log_="hi_mpi_vdec_destroy_chn";
    ret = hi_mpi_vdec_destroy_chn(channelId_);
    send_frame_run_log_="hi_mpi_vdec_destroy_chn out";
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Destrory channel " << channelId_ << " failed." << std::hex << ret;
        aclChannelManage.PushChannel(channelId_, ret);
        return DG_ERR_DEINIT_FAIL;
    }
    aclChannelManage.PushChannel(channelId_, 0);

    //注销frame pool
    if (bRegAclFramePoolMap) {
        auto &aclFramePoolMap = AclFramePoolMap::getInstance();
        if (DG_OK != aclFramePoolMap.UnRegStreamId(dataSize_, streamid_)) {
            LOG(ERROR) << "Fail to aclFramePoolMap.UnRegStreamId! mem_size " << vdecConf_.dataSize << " streamid_ "
                       << streamid_;
        }
        bRegAclFramePoolMap = false;
    }
    streamid_ = 0;
    vdecConf_.bSet = false;
    
    if(videoInDeviceMemHeap_){
        videoInDeviceMemHeap_.reset();
    }
    return DG_OK;
}

/// @brief  发送结束帧，刷新通道中的解码结果
/// @param
/// @return
DgError AclVDecProcV2::VdecSendEosFrame(void) {
    hi_vdec_stream stream;
    hi_vdec_pic_info outPicInfo;
    stream.addr = NULL;
    stream.len = 0;
    stream.end_of_frame = HI_FALSE;
    stream.end_of_stream = HI_TRUE;  // Stream end flage
    stream.need_display = HI_FALSE;
    outPicInfo.vir_addr = 0;
    outPicInfo.buffer_size = 0;

    send_frame_run_log_="hi_mpi_vdec_send_stream eos";
    auto ret = hi_mpi_vdec_send_stream(channelId_, &stream, &outPicInfo, -1);
    send_frame_run_log_="hi_mpi_vdec_send_stream eos out";
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Send end of stream failed. " << std::hex << ret;
        return DG_ERR_VDEC_FAIL;
    }
    return DG_OK;
}
// std::shared_ptr<AclFramePoolMap> AclFramePoolMap::me_;
// std::map<unsigned int, std::shared_ptr<AclMemBlock>> AclFrameBuffer::mMemBlock_;

}  // namespace acl
}  // namespace vega

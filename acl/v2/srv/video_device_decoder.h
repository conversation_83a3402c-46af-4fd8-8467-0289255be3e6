#ifndef VEGA_ACL_VIDEO_DEVICE_DECODER_V2_H
#define VEGA_ACL_VIDEO_DEVICE_DECODER_V2_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <atomic>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "srv/dvpp_common.h"
#include "common/Platform/processor/video_decoder_buffer.h"
#include "utils/FramebufferPool.h"
#include "common/Platform/utils/mem_heap.h"
#include "channel_manage.h"
#include "vega_time_pnt.h"

namespace vega
{
    namespace acl
    {
        class AclVDecProcV2 : public AclVDecProcBase
        {
#define ERR_DECODE_NOPIC 0x20000 //隔行码流场景下使用，隔行码流每帧发送两场，解码时其中一块无图像输出，属于正常现象，会返回该错误码。
        public:
            typedef enum {
                DECODE, 
                SKIP,  
                FULL,
                STRATEGY
            } DecStatus;
            typedef struct
            {
                AclMemDvppSP outMem;
                AclMemDvppSP inMem;
                VegaTmPnt start_time;
                DecVideoCallback cb;
                DecStatus status;
            } HI_FRAME_SP;
            typedef struct tagVdecConfig
            {
                cv::Size size;
                cv::Size stride;
                uint32_t dataSize;
                acldvppStreamFormat inFormat = H264_MAIN_LEVEL;
                acldvppPixelFormat outFormat = PIXEL_FORMAT_YUV_SEMIPLANAR_420;
                uint32_t channelId = 0;
                pthread_t threadId = 0;
                DiscardInterval discardInterval;
                aclvdecCallback callback = {0};
                bool bSet = false;
                bool fast_mode = false;
            } VdecConfig;

            typedef struct tagPackCbSeting
            {
                FRAME_DISCARD_MODE bIgnore;
                bool memFull = false;
                DecVideoCallback cb = nullptr;
                void *parent = nullptr;
                uint64 sn = 0;
                std::shared_ptr<AclMemDvpp> int_mem;
                std::shared_ptr<AclMemDvpp> out_mem;
                std::shared_ptr<acldvppStreamDesc> streamInputDesc;
                std::shared_ptr<acldvppPicDesc> picOutputDesc;
            } PackCbSeting;

            AclVDecProcV2()
            {
            }
            ~AclVDecProcV2()
            {
            }

            AclVDecProcV2 &operator=(const AclVDecProcV2 &) = delete;
            DgError DecodeVideo(MatrixSP input, MatrixType outputType, DecVideoCallback cb,
                                const VideoDecParamSP &vdecParam, bool bEos);
            DgError Init(const aclrtStream &stream, uint32_t channelId);
            DgError Deinit(void);
            std::string get_run_status() { return send_frame_run_log_+get_frame_run_log_; };

        protected:
            void *recvStreamThread(void);
            static void *AclReportThread_(void *arg);

            DgError InitVdec(const VdecConfig &config);
            DgError DeInitVdec(void);
            FrameId nextFid() { return frame_id_++; }
            DgError VdecSendEosFrame(void);
            DgError vdec_reset_chn(uint32_t chanId);
            void DecodeDone(DgError error, cv::Size size, cv::Size strideSize, MatrixType type, const PackCbSeting *packCbSeting);
            void waitVdecEnd();
            DgError GetCheckCBFrame(VegaTmPnt tp_now,HI_FRAME_SP &frameSp, uint64_t frame_id,uint32_t   frame_flag,bool check);

        private:
            hi_payload_type inputStreamtype_;
            hi_pixel_format outputFormat_;
            int32_t timeOut_ = 1000;
            pthread_t pthread_routine_;
            bool thread_destroy_ = false;
            std::mutex thread_destroy_mutex_;
            std::mutex cb_frame_mutex_;
            std::map<FrameId, HI_FRAME_SP> cb_frame_; // Pts_:HI_FRAME_SP
            FrameId frame_id_ = 1;
            FrameId recv_id_ = 1;
            aclrtRunMode runMode_ = ACL_HOST;
            aclrtContext context_ = NULL;
            int channelId_ = 0;
            uint32_t videoWidth_ = 1280;
            uint32_t videoHeight_ = 720;
            uint32_t dataSize_;
            VdecConfig vdecConf_;
            const unsigned int maxResolution_ = 4096;
            bool bRegAclFramePoolMap = false;
            
            StreamId streamid_ = 0;
            uint64 packet_sn_ = 0;
            unsigned int last_ch_erro_ = 0;
            std::string send_frame_run_log_;
            std::string get_frame_run_log_;
            std::shared_ptr<MemHeap> videoInDeviceMemHeap_;
            bool is_acl_soc_=false;
            uint64 frame_count_=0;
        };
    }
}
#endif // VEGA_ACL_VIDEO_DEVICE_DECODER_V2_H

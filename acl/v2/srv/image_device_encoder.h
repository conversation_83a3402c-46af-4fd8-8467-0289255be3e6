#ifndef VEGA_ACL_IMAGE_DEVICE_ENCODER_V2_H
#define VEGA_ACL_IMAGE_DEVICE_ENCODER_V2_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "srv/dvpp_common.h"
#include "rpc/rpc_processor.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "vpc_device.h"
#include "channel_manage.h"
#include "common/Platform/utils/mem_heap.h"
namespace vega
{
    namespace acl
    {
        class AclImageEncoderV2 : public AclImageEncoderBase
        {
        public:
            AclImageEncoderV2();
            ~AclImageEncoderV2();
            AclImageEncoderV2 &operator=(const AclImageEncoderV2 &) = delete;

            DgError JpegEncode(MatrixSP &input, MatrixSP &output, int quality);
            static DgError createChannel(int chn, int quality);
            static DgError destroyChannel(int chn);

        protected:
            DgError CombineJpegeProcess(MatrixSP &input, MatrixSP &output, int quality);

        private:
            hi_pixel_format format_ = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;
            DgError allocOutBuffer(hi_video_frame_info &frame, AclMemDvppSP &output);
            AclImageVpc vpc;
            std::shared_ptr<MemHeap> MemHeap_;
        };

    }
}

#endif
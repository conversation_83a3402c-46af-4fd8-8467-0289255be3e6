#include "vpc_device.h"
namespace vega {
namespace acl {
    static AclChannelPool channelPool(256, AclImageVpc::createChannel, AclImageVpc::destroyChannel);

     /// @brief  销毁通道、解码器停止接收码流
    DgError AclImageVpc::destroyChannel(int chn) {
        auto ret = hi_mpi_vpc_destroy_chn(chn);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "Destroy vpc channel " << chn << "  failed. " << std::hex << ret;
            return DG_ERR_HW_FAILURE;
        }        
        return DG_OK;
    }

    /// @brief  根据chnId创建通道
    /// @param chn  解码通道号[0,256)
    /// @return
    DgError AclImageVpc::createChannel(int& chn) {
        hi_s32 ret;
        hi_vpc_chn_attr stChnAttr_ = {0,0,0};


        //创建vpc通道,返回已创建的通道的通道号
        ret = hi_mpi_vpc_sys_create_chn(&chn, &stChnAttr_);
        if (ret != HI_SUCCESS) {
            return DG_ERR_INIT_FAIL;
        }
        return DG_OK;
    }

    /// @brief  缩放图片前的参数检查和初始化
    /// @param input 仅支持NV12格式缩放,位于device上
    /// @param output NV12
    /// @param resizeRatio 缩放比例
    /// @return
    DgError AclImageVpc::ResizeImage(const MatrixSP &input, MatrixSP &output, float resizeRatio) {
        // 1. todo: check input parameter
        AclImageSP aclImageSp = std::dynamic_pointer_cast<AclImage>(input);
        if (!aclImageSp) {
            LOG(ERROR) << "Can't convert matrix to aclImage. ";
            return DG_ERR_INVALID_IMAGE;
        }
        if (aclImageSp->type() != MatrixType::NV12) {
            LOG(ERROR) << "Only support NV12 resize ";
            return DG_ERR_NOT_SUPPORTED;
        }
        // 2. resize
        cv::Size size;
        int w = (int)input->size().width * resizeRatio;
        int h = (int)input->size().height * resizeRatio;
        size.width = DVPP_ALIGN_UP(w, 16);
        size.height = DVPP_ALIGN_UP(h, 2);
        if (vpcResize(input, output, size) < 0) {
            LOG(ERROR) << "Failed to vpcCrop!";
            return DG_ERR_HW_FAILURE;
        }
        return DG_OK;
    }
    /// @brief  裁剪图像,校验参数
    /// @param input NV12格式、device mem
    /// @param output
    /// @param rect  roi
    /// @return
    int AclImageVpc::CropImage(const MatrixSP &input, MatrixSP &output, cv::Rect &rect) {
        /*  输入约束：
         分辨率：10*6~8192*8192（包括8192），对于YUV420SP格式，宽高均需要2对齐
         输入内存：内存地址起始要求16Byte对齐，其中128Byte对齐性能最高
         输入图片的widthStride、heightStride：
             YUV420SP输入图片的宽（width）对齐到16
             昇腾710
         AI处理器，heightStride最小6、最大16384，对于YUV420SP/YUV440SP格式，heightStride需要2对齐，其它格式不需要。 */
        /*  输出约束：
         分辨率：10*6~4096*4096
         输出内存：内存地址起始要求16Byte对齐，其中128Byte对齐性能最高
         输出图片的widthStride、heightStride：
             widthStride最小32、最大16384
             昇腾710 AI处理器，heightStride最小6、最大16384，对于YUV420SP格式，heightStride需要2对齐，其它格式不需要。*/
        int picWidth = input->size().width;
        int picHeight = input->size().height;

        if (rect.width < 10 || rect.width > 8192 || rect.height < 6 || rect.height > 8192) {
            LOG(ERROR) << "input roi " << rect.width << "x" << rect.height
                       << ", crop image size is from 10x6 to 8192x8192";
            return -1;
        }

        //处理抠图区域的约束
        if (rect.x < 0)
            rect.x = 0;
        if (rect.y < 0)
            rect.y = 0;
        if (rect.x >= picWidth || rect.y >= picHeight) {
            LOG(ERROR) << "input roi x:" << rect.x << ", y:" << rect.y << ", w:" << rect.width << ", h:" << rect.height
                       << ", is error";
            return -1;
        }

        rect.x = rect.x & 0xFFFFFFFE;
        rect.y = rect.y & 0xFFFFFFFE;
        rect.width = DVPP_ALIGN_UP(rect.width, 16);
        rect.height = DVPP_ALIGN_UP(rect.height, 16);

        if ((rect.x + rect.width) > picWidth) {
            rect.width = DVPP_ALIGN_UP((picWidth - rect.x), 16);
            if (rect.width < 32) {
                rect.width = 32;
            }
            rect.x = picWidth - rect.width;
        }

        if ((rect.y + rect.height) > picHeight) {
            rect.height = DVPP_ALIGN_UP((picHeight - rect.y), 16);
            if (rect.height < 32) {
                rect.height = 32;
            }
            rect.y = picHeight - rect.height;
        }

        if (vpcCrop(input, output, rect) < 0) {
            LOG(ERROR) << "Failed to vpcCrop!";
            return -1;
        }

        return 0;
    }
    DgError AclImageVpc::cropResizePasteBatch(MatrixSPV &vInput, MatrixSPV &vOutput) {
        uint32_t srcPicNum = vInput.size();
        hi_vpc_pic_info *batchInputPic[srcPicNum] = { nullptr };  // pointer array
        uint32_t regionCount[srcPicNum];
        hi_vpc_pic_info outputPic;
        // uint32_t dstBufferSize[srcPicNum];

        // conf input picture
        for (uint32_t i = 0; i < srcPicNum; i++) {
            batchInputPic[i] = static_cast<hi_vpc_pic_info *>(malloc(sizeof(hi_vpc_pic_info)));
            if (batchInputPic[i] == nullptr) {
                for (uint32_t j = 0; j < i; ++j) {
                    free(batchInputPic[j]);
                    batchInputPic[j] = nullptr;
                }
                LOG(ERROR) << "malloc batchinputpic" << i << "failed ";
                return DG_ERR_MEMORY_SHORTAGE;
            }
            if (configPicInfo(vInput[i], *batchInputPic[i]) != DG_OK) {
                return DG_ERR_NOT_SUPPORTED;
            }
            regionCount[i] = 1;
        }

        // configure output region
        hi_vpc_crop_resize_paste_region cropResizePasteInfos[srcPicNum];
        for (uint32_t i = 0; i < srcPicNum; i++) {
            if (configPicInfo(vOutput[i], outputPic,false) != DG_OK) {
                return DG_ERR_NOT_SUPPORTED;
            }
            auto dgErr = vpcMemset(vOutput[i]);
            if (dgErr != DG_OK) {
                return dgErr;
            }
            if (configCropResizePasteRegion(vInput[i], vOutput[i], outputPic, cropResizePasteInfos[i]) != DG_OK) {
                return DG_ERR_NOT_SUPPORTED;
            }
        }

        uint32_t taskID = 0;
        hi_vpc_chn vpcChn = channelPool.GetChannel();
        if(vpcChn == -1){
            LOG(ERROR) << "vpc channel used up";
            return DG_ERR_HW_FAILURE;
        }
        auto ret = hi_mpi_vpc_batch_crop_resize_paste(
          vpcChn, (const hi_vpc_pic_info **)batchInputPic, srcPicNum, cropResizePasteInfos, regionCount, &taskID, -1);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "hi_mpi_vpc_batch_crop_resize_paste failed, " << vInput[0]->typestr() << " "
                       << vInput[0]->size() << vInput[0]->stride() << " ret = " << std::hex << ret;
            for (uint32_t j = 0; j < srcPicNum; j++) {
                free(batchInputPic[j]);
                batchInputPic[j] = nullptr;
            }
            channelPool.PushChannel(vpcChn,DG_OK);
            return DG_ERR_HW_FAILURE;
        }

        // asign the taskID that you get from the last interface to get the process result
        uint32_t taskIDResult = taskID;
        ret = hi_mpi_vpc_get_process_result(vpcChn, taskIDResult, -1);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "hi_mpi_vpc_get_process_result failed, ret =" << std::hex << ret;
            for (uint32_t j = 0; j < srcPicNum; j++) {
                free(batchInputPic[j]);
                batchInputPic[j] = nullptr;
            }
            channelPool.PushChannel(vpcChn, DG_OK);
            return DG_ERR_HW_FAILURE;
        }
        
        // free batchinputPic on host
        for (uint32_t j = 0; j < srcPicNum; j++) {
            free(batchInputPic[j]);
        }
        channelPool.PushChannel(vpcChn,DG_OK);
        return DG_OK;
    }
    /// @brief 抠图、resize、贴图，在调用该接口之前进行prepare，确保对齐符合要求
    /// @param input 输入图像，roi即是要抠图的区域
    /// @param output  输出图像、roi是要贴图的区域
    /// @return
    DgError AclImageVpc::cropResizePaste(const MatrixSP &input, const MatrixSP &output) {
        if(vpcPicCheck(input) != DG_OK || vpcPicCheck(output,false) != DG_OK){
            return DG_ERR_NOT_SUPPORTED;
        }
        int32_t ret;
        hi_vpc_pic_info inputPic;
        if (configPicInfo(input, inputPic) != DG_OK) {
            return DG_ERR_NOT_SUPPORTED;
        }

        hi_vpc_pic_info outputPic;
        uint32_t multiCount = 1;  // 单图抠图
        hi_vpc_crop_resize_paste_region cropResizePasteInfos[multiCount];
        for (uint32_t i = 0; i < multiCount; i++) {
            if (configPicInfo(output, outputPic) != DG_OK) {
                return DG_ERR_NOT_SUPPORTED;
            }

            auto dgErr = vpcMemset(output);
            if (dgErr != DG_OK) {
                return dgErr;
            }

            if (configCropResizePasteRegion(input, output, outputPic, cropResizePasteInfos[i]) != DG_OK) {
                return DG_ERR_NOT_SUPPORTED;
            }
        }

        uint32_t taskID = 0;
        int vpcChn = channelPool.GetChannel();
        if(vpcChn<0){
            LOG(ERROR)<<"Fail to get mpi channel!";
            return DG_ERR_HW_FAILURE;
        }
        ret = hi_mpi_vpc_crop_resize_paste(vpcChn, &inputPic, cropResizePasteInfos, multiCount, &taskID, -1);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "call hi_mpi_vpc_crop_resize_paste failed. [input info] type: " 
                    << input->typestr() << " size: " << input->size() << " stride: " << input->stride() << " roi: " <<  input->roi() << " [output info] type: " 
                    << output->typestr() << " size: " << output->size() << " stride: " << output->stride() << " roi: "  << output->roi() << std::hex << ret;
            channelPool.PushChannel(vpcChn,DG_OK);
            return DG_ERR_HW_FAILURE;
        }
        
        uint32_t taskIDResult = taskID;
        ret = hi_mpi_vpc_get_process_result(vpcChn, taskIDResult, -1);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "call hi_mpi_vpc_get_process_result failed " << std::hex << ret;
            channelPool.PushChannel(vpcChn,DG_OK);
            return DG_ERR_HW_FAILURE;
        }
        channelPool.PushChannel(vpcChn, DG_OK);
        return DG_OK;
    }

    /// @brief 检查vpc模块内存约束、分辨率约束
    /// @param input 检查对象
    /// @return 
    DgError AclImageVpc::vpcPicCheck(const MatrixSP &input,bool isInput){
        // resize宽度非16对齐，贴图外区域有随机数据
        //
        // 分辨率要求:
        // 输入图片分辨率:10*6~4096*4096
        // 宽stride:32~4096*4 高stride:6~4096*4
        //
        // 输入输出要求：
        // NV12:宽高2对齐，宽stride为宽16对齐后的值，高stride为高2对齐后的值,内存大小（单位Byte）= 宽stride * 高stride* 3/2
        // BGR:宽高无要求，宽stride为宽16对齐后、再乘以3的值，高stride无要求,内存大小（单位Byte）= 宽stride * 高stride
        // DgError ret = DG_ERR_NOT_SUPPORTED;
        // uint32_t width_stride = input->stride().width;
        // uint32_t height_stride = input->stride().height;
        // uint32_t width = input->size().width;
        // uint32_t height = input->size().height;

        // if (isInput
        //     && (width < MIN_VPC_WIDTH || height < MIN_VPC_HEIGHT || width > MAX_VPC_INPUT_WIDTH || height > MAX_VPC_INPUT_HEIGHT)) {
        //     LOG(ERROR) << "picture size " << width << "x" << height << " not supported.";
        //     return ret;
        // }
        // if (!isInput
        //     && (width < MIN_VPC_WIDTH || height < MIN_VPC_HEIGHT || width > MAX_VPC_OUTPUT_WIDTH || height > MAX_VPC_OUTPUT_HEIGHT)) {
        //     LOG(ERROR) << "picture size " << width << "x" << height << " not supported.";
        //     return ret;
        // }

        // if (input->type() == MatrixType::NV12 || input->type() == MatrixType::NV21) {
        //     if (width % MIN_VPC_ALIGN != 0 || height % MIN_VPC_ALIGN != 0) {
        //         LOG(ERROR) << "picture should be aligned to 2,picture width " << width << "x" << height
        //                    << " not supported.";
        //         return ret;
        //     }
        //     if (width_stride % VPC_STRIDE_WIDTH != 0 || height_stride % VPC_STRIDE_HEIGHT != 0) {
        //         LOG(ERROR) << "width_stride should be aligned to 16, picture stride " << width_stride << "x"
        //                    << height_stride << "pic size " << width << "x" << height << " not supported.";
        //         return ret;
        //     }
        //     if (input->memory()->size() != width_stride * height_stride * 3u / 2u) {
        //         LOG(ERROR) << "picture memory size " << input->memory()->size()
        //                    << " != " << width_stride * height_stride * 3 / 2 << " pic size " << width << "x" << height;
        //         return ret;
        //     }
        // }
        // if (input->type() == MatrixType::BGRPacked) {
        //     if (width_stride != DVPP_ALIGN_UP(width, 16) * 3) {
        //         LOG(ERROR) << "width_stride should be aligned to 16,then muliple 3, picture stride " << width_stride
        //                    << "x" << height_stride << " pic size " << width << "x" << height << " not supported.";
        //         return ret;
        //     }
        //     if (input->memory()->size() != width_stride * height_stride * 1u) {
        //         LOG(ERROR) << "picture memory size " << input->memory()->size()
        //                    << " != " << width_stride * height_stride << " pic size " << width << "x" << height;
        //         return ret;
        //     }
        // }
        return DG_OK;
    }

    /// @brief 配置输出的抠图、缩放、贴图区域
    /// @param input   待抠图的图像
    /// @param output 贴图的图像
    /// @param outputPic 输出描述配置
    /// @param cropResizePasteInfo 区域配置信息
    /// @return
    DgError AclImageVpc::configCropResizePasteRegion(const MatrixSP &input,
                                        const MatrixSP &output,
                                        hi_vpc_pic_info &outputPic,
                                        hi_vpc_crop_resize_paste_region &cropResizePasteInfo) {
        auto aclInput = std::dynamic_pointer_cast<AclImage>(input);
        if (!aclInput) {
            LOG(INFO) << "input is not aclImage";
            return DG_ERR_NOT_SUPPORTED;
        }
        auto aclOutput = std::dynamic_pointer_cast<AclImage>(output);
        if (!aclOutput) {
            LOG(INFO) << "output is not aclImage";
            return DG_ERR_NOT_SUPPORTED;
        }
        if ((unsigned)aclInput->roi().width < MIN_VPC_WIDTH || (unsigned)aclInput->roi().height < MIN_VPC_HEIGHT) {
            LOG(INFO) << "Only support  crop area  larger than" << MIN_VPC_WIDTH << "x" << MIN_VPC_HEIGHT;
            return DG_ERR_NOT_SUPPORTED;
        }
        if ((unsigned)aclOutput->roi().width < MIN_VPC_WIDTH || (unsigned)aclOutput->roi().height < MIN_VPC_HEIGHT) {
            LOG(INFO) << "Only support  paste area  larger than" << MIN_VPC_WIDTH << "x" << MIN_VPC_HEIGHT;
            return DG_ERR_NOT_SUPPORTED;
        }
        cropResizePasteInfo.dest_pic_info = outputPic;
        cropResizePasteInfo.crop_region.left_offset = aclInput->roi().x;
        cropResizePasteInfo.crop_region.top_offset = aclInput->roi().y;
        cropResizePasteInfo.crop_region.crop_width = aclInput->roi().width;
        cropResizePasteInfo.crop_region.crop_height = aclInput->roi().height;
        cropResizePasteInfo.resize_info.resize_width = aclOutput->roi().width;
        cropResizePasteInfo.resize_info.resize_height = aclOutput->roi().height;
        cropResizePasteInfo.resize_info.interpolation = 0; //Bilinear
        cropResizePasteInfo.dest_left_offset = aclOutput->roi().x;
        cropResizePasteInfo.dest_top_offset = aclOutput->roi().y;
        return DG_OK;
    }
    /// @brief 配置图片信息
    /// @param input 输入原始图片信息
    /// @param inputPic  配置的图片格式
    /// @return
    DgError AclImageVpc::configPicInfo(const MatrixSP &input, hi_vpc_pic_info &inputPic,bool isInput) {
        if (vpcPicCheck(input, isInput) != DG_OK) {
            return DG_ERR_NOT_SUPPORTED;
        }
        auto aclInput = std::dynamic_pointer_cast<AclImage>(input);
        if (!aclInput) {
            LOG(INFO) << "input is not aclImage";
            return DG_ERR_NOT_SUPPORTED;
        }
        inputPic.picture_width
          = input->type() == MatrixType::NV12 ? DVPP_ALIGN_UP(aclInput->size().width, 2) : aclInput->size().width;
        inputPic.picture_height
          = input->type() == MatrixType::NV12 ? DVPP_ALIGN_UP(aclInput->size().height, 2) : aclInput->size().height;
        inputPic.picture_width_stride = aclInput->stride().width;
        inputPic.picture_height_stride = aclInput->stride().height;
        inputPic.picture_buffer_size = aclInput->memory()->size();
        inputPic.picture_address = aclInput->memory()->blob();
        if (input->type() == MatrixType::NV12)
            inputPic.picture_format = format_;
        else if (input->type() == MatrixType::BGRPacked) {
            inputPic.picture_format = BGRformat_;
        } else {
            LOG(ERROR) << input->typestr() << " not supported! ";
            return DG_ERR_NOT_SUPPORTED;
        }
        return DG_OK;
    }
    /// @brief 根据图片格式将图片设置为黑色，支持device、host
    /// @param input
    /// @return
    DgError AclImageVpc::vpcMemset(const MatrixSP &input) {
        if (input->type() != MatrixType::NV12 && input->type() != MatrixType::BGRPacked) {
            LOG(ERROR) << "Vpc memset only support NV12 or BGR. ";
            return DG_ERR_NOT_SUPPORTED;
        }
        if (input->type() == MatrixType::BGRPacked) {
            auto ret = aclrtMemset(input->memory()->blob(), input->memory()->size(), 0, input->memory()->size());
            if (ret != HI_SUCCESS) {
                LOG(ERROR) << "memset failed, ret = " << std::hex << ret;
                return DG_ERR_HW_FAILURE;
            }
        } else {
            auto ret = aclrtMemset(input->memory()->blob(), input->MemSize() / 3 * 2, 0, input->MemSize() / 3 * 2);
            if (ret != HI_SUCCESS) {
                LOG(ERROR) << "Fail to aclrtMemset mem_size " << input->MemSize() << ", erro code " << std::hex << ret;
                return DG_ERR_HW_FAILURE;
            }
            ret = aclrtMemset(
              input->memory()->blob() + input->MemSize() / 3 * 2, input->MemSize() / 3, 128, input->MemSize() / 3);
            if (ret != HI_SUCCESS) {
                LOG(ERROR) << "Fail to aclrtMemset mem_size " << input->MemSize() << ", erro code " << std::hex << ret;
                return DG_ERR_HW_FAILURE;
            }
        }
        return DG_OK;
    }
    /// @brief 使用硬件进行缩放
    /// @param input
    /// @param output
    /// @param size 缩放的目标尺寸
    /// @return
    DgError AclImageVpc::vpcResize(const MatrixSP &input, MatrixSP &output, cv::Size size) {
        hi_s32 ret;
        hi_vpc_pic_info inputPic, outputPic;
        if (configPicInfo(input, inputPic) != DG_OK) {
            return DG_ERR_NOT_SUPPORTED;
        }

        outputPic.picture_width = size.width;
        outputPic.picture_height = size.height;
        outputPic.picture_format = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;
        outputPic.picture_width_stride = size.width;
        outputPic.picture_height_stride = size.height;
        output = std::make_shared<AclImage>();
        auto err = output->create(MatrixType::NV12, size);
        if (err != DG_OK) {
            LOG(ERROR) << "Create NV12 image on device failed";
            return err;
        }
        outputPic.picture_buffer_size = output->memory()->size();
        outputPic.picture_address = output->memory()->blob();

        uint32_t taskID = 0;
        int  vpcChn = channelPool.GetChannel();
        if(vpcChn<0){
            LOG(ERROR)<<"Fail to get mpi channel!";
            return DG_ERR_DECODE_FAIL;
        }
        ret = hi_mpi_vpc_resize(vpcChn, &inputPic, &outputPic, 0, 0, 0, &taskID, -1);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "Vpc resize failed when encode video " << std::hex << ret;
            channelPool.PushChannel(vpcChn,DG_OK);
            return DG_ERR_DECODE_FAIL;
        }
        uint32_t taskIDResult = taskID;
        ret = hi_mpi_vpc_get_process_result(vpcChn, taskIDResult, -1);
        if (ret != HI_SUCCESS) {
            LOG(ERROR) << "hi_mpi_vpc_get_process_result error " << std::hex << ret;
            channelPool.PushChannel(vpcChn,DG_OK);

            return DG_ERR_DECODE_FAIL;
        }
        channelPool.PushChannel(vpcChn, DG_OK);
        return DG_OK;
    }
    /// @brief  抠图
    /// @param input source 
    /// @param output  destination
    /// @param rect 抠图区域ROI
    /// @return 
    int AclImageVpc::vpcCrop(const MatrixSP &input, MatrixSP &output, cv::Rect &rect) {
        AclImageSP aclImageSp = std::dynamic_pointer_cast<AclImage>(input);
        if (!aclImageSp) {
            LOG(ERROR) << "input can't convert to aclImage";
            return -1;
        }
        // 1.获取软件栈的运行模式，不同运行模式影响后续的接口调用流程（例如是否进行数据传输等）
        aclrtRunMode runMode;
        auto ret = aclrtGetRunMode(&runMode);
        if (ret != ACL_SUCCESS) {
            LOG(ERROR) << "Failed to get run mode, ret = " << std::hex << ret;
            return -1;
        }

        // 6.执行抠图
        // 6.1 构造存放输入图片信息的结构体
        hi_vpc_pic_info inputPic;
        if (configPicInfo(input, inputPic) != DG_OK) {
            return DG_ERR_NOT_SUPPORTED;
        }

        // 6.3 构造存放输出图片信息的结构体
        //该参数表示抠图数量
        uint32_t multiCount = 1;
        // cropRegionInfos、dstBufferSize数组的大小与抠图数量保持一致
        hi_vpc_crop_region_info cropRegionInfos[1];
        // uint32_t dstBufferSize[1];
        hi_vpc_pic_info outputPic;

        // has only one picture,so the mem is valid
        AclMemDvppSP mem;
        for (uint32_t i = 0; i < multiCount; i++) {
            outputPic.picture_width = rect.width;
            outputPic.picture_height = rect.height;
            outputPic.picture_format = format_;
            outputPic.picture_width_stride = rect.width;
            outputPic.picture_height_stride = rect.height;
            outputPic.picture_buffer_size = outputPic.picture_width_stride * outputPic.picture_height_stride * 3 / 2;
            // ret = hi_mpi_dvpp_malloc(0, &outputPic.picture_address, outputPic.picture_buffer_size);
            mem = std::make_shared<AclMemDvpp>();
            auto err = mem->create(outputPic.picture_buffer_size, 1);
            if (err != DG_OK) {
                LOG(ERROR) << "Maclloc device memory for output failed";
                return -1;
            }
            outputPic.picture_address = mem->blob();

            //表示从输入图片中抠出以左上角为原点、分辨率960*540的子图
            cropRegionInfos[i].dest_pic_info = outputPic;
            cropRegionInfos[i].crop_region.left_offset = rect.x;
            cropRegionInfos[i].crop_region.top_offset = rect.y;
            cropRegionInfos[i].crop_region.crop_width = rect.width;
            cropRegionInfos[i].crop_region.crop_height = rect.height;
        }

        // 6.4 调用抠图接口
        uint32_t taskID = 0;
        hi_vpc_chn vpcChn = channelPool.GetChannel();
        ret = hi_mpi_vpc_crop(vpcChn, &inputPic, cropRegionInfos, 1, &taskID, -1);
        if (ret != ACL_SUCCESS) {
            LOG(ERROR) << "call hi_mpi_vpc_crop failed, failed to send crop task, ret = " << std::hex << ret;
            channelPool.PushChannel(vpcChn, DG_OK);
            return -1;
        }

        // 6.5 等待任务处理结束，任务处理结束后，输出图片数据在outputPic.picture_address指向的内存中
        uint32_t taskIDResult = taskID;
        ret = hi_mpi_vpc_get_process_result(vpcChn, taskIDResult, -1);
        if (ret != ACL_SUCCESS) {
            LOG(ERROR) << "Failed to get vpc process result, ret = " << std::hex << ret;
            channelPool.PushChannel(vpcChn, DG_OK);
            return -1;
        }
        channelPool.PushChannel(vpcChn, DG_OK);

        auto dst = std::make_shared<AclImage>();
        auto err = dst->create(MatrixType::NV12,
                               cv::Size(outputPic.picture_width, outputPic.picture_height),
                               mem,
                               1,
                               outputPic.picture_width_stride,
                               outputPic.picture_height_stride);
        if (err != DG_OK) {
            LOG(ERROR) << "Failed to create output frame for image";
            return -1;
        }
        if (dst->reqMemSize() > (int)outputPic.picture_buffer_size) {
            LOG(ERROR) << "Fail because dataSize " << outputPic.picture_buffer_size << " < reqMemSize "
                       << dst->reqMemSize() << " !";
            return -1;
        }
        output = dst;
        return 0;
    }
}
}  // namespace vega
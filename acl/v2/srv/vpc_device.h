#ifndef VEGA_ACL_VPC_DEVICE
#define VEGA_ACL_VPC_DEVICE
#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>

#include <condition_variable>

#include "channel_manage.h"
#include "dg_base_types.h"
#include "error.h"
#include "glog/logging.h"
#include "rpc/rpc_processor.h"
#include "srv/dvpp_common.h"
#include "utils/acl_image.h"
#include "utils/acl_memory.h"
#include "vega_matrix.h"

namespace vega {
namespace acl {

class AclImageVpc {
public:
    AclImageVpc() {
    }
    ~AclImageVpc() {
    }

public:
    static DgError destroyChannel(int chn);
    static DgError createChannel(int &chn);
    DgError ResizeImage(const MatrixSP &input, MatrixSP &output, float resizeRatio);
    int CropImage(const MatrixSP &input, MatrixSP &output, cv::Rect &rect);
    DgError cropResizePasteBatch(MatrixSPV &vInput, MatrixSPV &vOutput);
    DgError cropResizePaste(const MatrixSP &input, const MatrixSP &output);

private:
    DgError vpcPicCheck(const MatrixSP &input, bool isInput = true);
    DgError configCropResizePasteRegion(const MatrixSP &input,
                                        const MatrixSP &output,
                                        hi_vpc_pic_info &outputPic,
                                        hi_vpc_crop_resize_paste_region &cropResizePasteInfo);
    DgError configPicInfo(const MatrixSP &input, hi_vpc_pic_info &inputPic, bool isInput = true);
    DgError vpcMemset(const MatrixSP &input);
    DgError vpcResize(const MatrixSP &input, MatrixSP &output, cv::Size size);
    int vpcCrop(const MatrixSP &input, MatrixSP &output, cv::Rect &rect);

private:
    hi_pixel_format format_ = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;
    hi_pixel_format BGRformat_ = HI_PIXEL_FORMAT_BGR_888;

    bool isInit_ = false;
    hi_vpc_chn vpcChn_;
    hi_vpc_chn_attr stChnAttr_;
};
}  // namespace acl
}  // namespace vega

#endif
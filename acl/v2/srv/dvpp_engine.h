#ifndef VEGA_DVPP_ENGINE_V2_H_
#define VEGA_DVPP_ENGINE_V2_H_

#include "acl/ops/acl_dvpp.h"
#include "image_engine.h"
#include "include/station/block_queue.h"
#include "srv/dvpp_common.h"
#include "utils/acl_image.h"
#include "utils/acl_memory.h"
#include "vega_sdk_config.h"
#include "vpc_device.h"
#if CANN50
# include "srv/acl_op_engine.h"
#endif
#define DVPP_MIN_W_V2 10
#define DVPP_MIN_H_V2 6
#define DVPP_MAX_W_V2 8192
#define DVPP_MAX_H_V2 8192
namespace vega {
namespace acl {
class DvppProcessV2 {
typedef struct prePareParamV{
    MatrixSPV input;
    MatrixSPV output;
    std::vector<bool> isOutputChange; 
}PrePareParamV;
typedef struct prePareParam{
    MatrixSP input;
    MatrixSP output;
    bool isOutputChange;
}PrePareParam;
public:
    DvppProcessV2() {
        dvpp_bgr_output_disable_=sdk_config::get_cfg<int>("dvpp_bgr_output_disable", 0, sdk_config::acl);
    };
    ~DvppProcessV2() {
    };

    DgError cropResize(MatrixSPV &vInput, MatrixSPV &vOutput,ProcessMatrixParam &info) {
        if (vInput.size() <= 1) {
            return DG_ERR_NOT_SUPPORTED;
        }
        PrePareParamV vParam;
        vParam.input.resize(vInput.size());
        vParam.output.resize(vInput.size());
        vParam.isOutputChange.resize(vInput.size());
        for (u_int i = 0; i < vInput.size(); i++) {
            PrePareParam param;
            auto ret = prepare(vInput[i], vOutput[i],param);
            if (DG_OK != ret) {
                if (DG_ERR_NOT_SUPPORTED != ret)
                    LOG(ERROR) << "Fail to prepare cropResize";
                return ret;
            }
            vParam.input[i]=param.input;
            vParam.output[i]=param.output;
            vParam.isOutputChange[i]=param.isOutputChange;
        }
        auto ret=vpc_.cropResizePasteBatch(vParam.input, vParam.output);
        if(ret==DG_OK){
            for(u_int i=0;i<vOutput.size();i++){
                PrePareParam param;
                param.input=vParam.input[i];
                param.output=vParam.output[i];
                param.isOutputChange=vParam.isOutputChange[i];
                if(DG_OK != outputPro(vOutput[i], param)){
                    LOG(ERROR)<<"Index "<<i<<"; fail to outputPro!";
                    return DG_ERR_HW_FAILURE;
                }
            }
        }
        if(FromDecoder::opencv != vInput[0]->fromDecoder()){
            if (vInput[0]->type() == MatrixType::BGRPacked && vOutput[0]->type() == MatrixType::NV12) {
                LOG(INFO) <<info.Info()<<". If input type is bgr and output type is NV12 , it will reduce precision and increase qps";
            }
        }
        return ret;
    }

    DgError cropResize(MatrixSP &input, MatrixSP &output,ProcessMatrixParam &info) {
        prePareParam param;
        auto ret = prepare(input, output,param);
        if (DG_OK != ret) {
            if (DG_ERR_NOT_SUPPORTED != ret)
                LOG(ERROR) << "Fail to prepare cropResize";
            return ret;
        }
        // todo: vpc通道管理
        ret=vpc_.cropResizePaste(param.input, param.output);
        if(ret==DG_OK){
            if(DG_OK != outputPro(output, param)){
                LOG(ERROR)<<"Fail to outputPro!";
                return DG_ERR_HW_FAILURE;
            }
        }else{
             LOG(ERROR) <<info.Info()<<" ; Dvpp fail to cropResize ! "<<"input size "<<input->size()<<" stride "<<input->stride()<<" aligned in roi "<< param.input->roi()
                    <<",src in roi "<<input->roi()<<", aligned out roi: " << param.output->roi()<<", src out roi "<< output->roi()<<", output size " <<output->size()<<" stride "<<output->stride();
        }
        if(FromDecoder::opencv != input->fromDecoder()){
            if (input->type() == MatrixType::BGRPacked && output->type() == MatrixType::NV12) {
                LOG(INFO) <<info.Info()<<". If input type is bgr and output type is NV12 , it will reduce precision and increase qps";
            }
        }
        return ret;
    }
    DgError prepare(MatrixSP &input, MatrixSP &output,prePareParam &param) {
        // check type
        if (input->type() != MatrixType::BGRPacked && input->type() != MatrixType::NV12) {
            LOG(INFO) << "dvpp cropResize don't support input type of" << matrix_type_str(input->type());
            return DG_ERR_NOT_SUPPORTED;
        }
        if (output->type() != MatrixType::BGRPacked && output->type() != MatrixType::NV12) {
            LOG(INFO) << "dvpp cropResize don't support output type of " << matrix_type_str(output->type());
            return DG_ERR_NOT_SUPPORTED;
        }
        if(dvpp_bgr_output_disable_ && output->type() == MatrixType::BGRPacked){// Recommend not to process this output type, because dvpp bgr output qps is very low
            LOG(INFO) << "dvpp cropResize don't support output type of " << matrix_type_str(output->type());
            return DG_ERR_NOT_SUPPORTED;
        }

        auto acl_output = std::dynamic_pointer_cast<AclImage>(output);
        if (!acl_output) {
            LOG(INFO) << "output is not aclImage";
            return DG_ERR_NOT_SUPPORTED;
        }

        // check input size
        if (input->stride().width < DVPP_MIN_W_V2 || input->stride().height < DVPP_MIN_H_V2
            || input->stride().width > DVPP_MAX_W_V2 || input->stride().height > DVPP_MAX_H_V2) {
            LOG(ERROR) << "input stride " << input->stride() << " ,beyond the capability of DVPP!";
            return DG_ERR_INVALID_PARAM;
        }


        cv::Rect iRoi ;
        if(input->type() == MatrixType::BGRPacked){
            iRoi = fixRoi(input,false);

        }else{
            iRoi = fixRoi(input);
        }
        cv::Rect oRoi;
        if(output->type()==MatrixType::NV12){
            oRoi = fixRoi(output,true);
         }else{
            oRoi = fixRoi(output,false);
         }
         if (oRoi.x % 16 != 0) {
            int x=oRoi.x;
            oRoi.x = oRoi.x/16 * 16;
            float ratio=(float)(x-oRoi.x)/oRoi.width;   
            if(ratio>0.05){
                LOGFULL  << "ORoi: " << oRoi<<",discontact ratio "<<ratio;
                return DG_ERR_NOT_SUPPORTED;
            }
        }
        if(output->type()==MatrixType::NV12){
            if (oRoi.width % 16 != 0) {
                int w=oRoi.width;
                oRoi.width = oRoi.width/16 * 16;
                float ratio=(float)(w-oRoi.width)/w;
                if(ratio>0.05){
                     LOGFULL  << "ORoi: " << oRoi<<",discontact ratio "<<ratio;
                     return DG_ERR_NOT_SUPPORTED;
                }
            }
        }
        oRoi = cv::Rect(oRoi.tl(), oRoi.size());
        // alignment:
        // NV12  宽2对齐、高2对齐；宽stride为宽16对齐后的值，高stride为高2对齐后的值。
        //  BGR     宽高无要求 宽stride 16对齐后乘3、高stride无要求
        bool change;
        auto ret = CreatFitMatrix(input, param.input, change, input->type());
        if (ret != DG_OK) {
            LOG(ERROR) << "Fail to creatFitMatrix";
            return ret;
        }

        ret = CreatFitMatrix(output, param.output,param.isOutputChange, output->type(), false);
        if (ret != DG_OK) {
            LOG(ERROR) << "Fail to creatFitMatrix";
            return ret;
        }
        if (param.output->roi().area() != param.output->size().area()) {
            if (output->type() == MatrixType::NV12) {
                auto acl_erro = aclrtMemset(
                  param.output->memory()->blob(), param.output->MemSize() / 3 * 2, 0, param.output->MemSize() / 3 * 2);
                if (acl_erro != ACL_SUCCESS) {
                    LOG(ERROR) << "Fail to aclrtMemset mem_size " << param.output->MemSize() << ", erro code " << acl_erro;
                }
                acl_erro = aclrtMemset(param.output->memory()->blob() + param.output->MemSize() / 3 * 2,
                                            param.output->MemSize() / 3,
                                            128,
                                            param.output->MemSize() / 3);
                if (acl_erro != ACL_SUCCESS) {
                    LOG(ERROR) << "Fail to aclrtMemset mem_size " << param.output->MemSize() << ", erro code " << acl_erro;
                }
            } else {
                auto acl_erro
                  = aclrtMemset(param.output->memory()->blob(), param.output->MemSize(), 0, param.output->MemSize());
                if (acl_erro != ACL_SUCCESS) {
                    LOG(ERROR) << "Fail to aclrtMemset mem_size " << param.output->MemSize() << ", erro code " << acl_erro;
                }
            }
        }
        input->setRoi(iRoi);
        output->setRoi(oRoi);
        return DG_OK;
    }
    DgError outputPro(MatrixSP &output, prePareParam param){
        if(param.isOutputChange){
            auto acl_output=std::dynamic_pointer_cast<AclImage>(output);
             auto acl_src= std::dynamic_pointer_cast<AclImage>(param.output);
            auto ret=acl_output->copy(acl_src);
            if(ret!=DG_OK){
                LOG(ERROR) << " Faile to copy !";
                return DG_ERR_ABORTED;
            }
        }
        output->setRoi(param.output->roi());
        return DG_OK;
    }

private:
    cv::Rect fixRoi(MatrixSP &matrix,bool alignedBy2=true) {
        auto roi = matrix->roi();
        auto unit = UNIT(matrix->type());
        auto pixel = matrix->stride();
        pixel.width /= unit;
        roi =roi & matrix->rect();
        if(roi.width<DVPP_MIN_W_V2 ){
            int dif=DVPP_MIN_W_V2-roi.width;
            roi.width=DVPP_MIN_W_V2 ;
            roi.x-=dif/2;
        }
        if(roi.height<DVPP_MIN_H_V2 ){
            int dif=DVPP_MIN_H_V2-roi.height;
            roi.height=DVPP_MIN_H_V2 ;
            roi.y-=dif/2;
        } 
        if(alignedBy2){
            if (roi.x % 2 != 0) {
                roi.x--;
            }
            if (roi.y % 2 != 0) {
                roi.y--;
            }
        }
        if (roi.x < 0) {
            roi.x = 0;
        }
        if (roi.y < 0) {
            roi.y = 0;
        }
        if(alignedBy2){
            if (roi.width % 2 != 0) {
                roi.width++;
                if (roi.width > pixel.width - roi.x)
                    roi.width -=2;
            }
            if (roi.height % 2 != 0) {
                roi.height++;
                if (roi.height > pixel.height - roi.y)
                    roi.height-=2;
            }
        }
        return roi;
    }

    DgError CreatFitMatrix(MatrixSP input, MatrixSP &output, bool &change, MatrixType matrixType, bool copy = true) {
        output = std::dynamic_pointer_cast<AclImage>(input);
        change = false;

        // NV12:宽高2对齐，宽stride为宽16对齐后的值，高stride为高2对齐后的值,内存大小（单位Byte）= 宽stride * 高stride* 3/2
        if (!output || input->stride().width % ACL_IMAGE_ALIGN_W != 0 || input->stride().height % ACL_IMAGE_ALIGN_H != 0
            || matrixType != input->type()) {
            change = true;
            auto output_acl = std::make_shared<AclImage>();
            int element_size = 1;
            cv::Size size = input->stride();
            if (input->type() == MatrixType::BGRPacked) {
                element_size = 3;
                size.width /= 3;
            }
            RawStream data = RawStream(input->data(), [=](void *data) {});
            bool isHostMem = false;
            if (!output) {
                isHostMem = true;
            }
            if (matrixType != input->type()) {
                copy = false;
            }
            if (copy) {
                auto ret = output_acl->create(input->type(), size, element_size, data, isHostMem);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Fail to create acl Matrix!";
                    return DG_ERR_ABORTED;
                }
            } else {
                auto ret = output_acl->create(matrixType, VegaDataType::CHAR, size);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Fail to create acl Matrix!";
                    return DG_ERR_ABORTED;
                }
            }
            output_acl->setRoi(input->roif(), true);
            output_acl->setFrameId(input->frameId());
            output_acl->setStreamId(input->streamId());
            output=output_acl;
        }
        return DG_OK;
    }

private:
    const int MinWidth_ = 10;
    const int MinHeight_ = 6;
    int use_aicpu_op_engine_ = 0;
    bool dvpp_bgr_output_disable_=1;
    AclImageVpc vpc_;
};

class DvppEngineV2 : public ImageEngine {
public:
    BlockQueue<std::shared_ptr<DvppProcessV2>> dvppProQ_;
    DvppEngineV2() : ImageEngine(10, "DVPP") {
        // create 10 dvpp process
        for (int i = 0; i < 10; i++) {
            std::shared_ptr<DvppProcessV2> dvppProcess = std::make_shared<DvppProcessV2>();
            dvppProQ_.push(dvppProcess);
        }
    }
    /**
     * Process matrixes.
     *
     * @param cmd command code
     * @param marks only those marks false should be processed
     * @param inputs input matrix, if partially processed, the input matrix can be replaced
     * @param outputs outputs matrixes
     * @param stream vega stream
     * @param param processing parameters
     * @return DG_OK if all of inputs are done
     */
    DgError process(VegaMatrixCmd cmd,
                    std::vector<bool> &marks,
                    MatrixSPV &inputs,
                    MatrixSPV &outputs,
                    VegaStream stream,
                    ProcessMatrixParam &param) override {
        if (cmd != VegaMatrixCmd::CROP_RESIZE) {
            return DG_ERR_NOT_SUPPORTED;
        }
        std::shared_ptr<DvppProcessV2> dvppProcess = dvppProQ_.pop();
        DgError ret = DG_OK;
        int cnt = 0;
        for (auto i = 0u; i < marks.size(); i++) {
            if (!marks[i])  // if false then cnt++
                cnt++;
        }
        inc(cnt);  // size += cnt
        if((unsigned int)cnt==marks.size()){
            ret = dvppProcess->cropResize(inputs, outputs,param);
            if (DG_OK == ret) {
                for (u_int i = 0u; i < marks.size(); i++)
                    marks[i] = true;
                dvppProQ_.push(dvppProcess);
                dec(cnt);
                return DG_OK;
            } else {
                if (DG_ERR_NOT_SUPPORTED != ret)
                    LOG(ERROR) << "Fail to dvpp batch cropResize";
            }
        }
        // process one by one
        ret = DG_OK;

        for (auto i = 0u; i < inputs.size(); i++) {
            DgError err;
            if (marks[i]) {
                continue;
            }
            err = dvppProcess->cropResize(inputs[i], outputs[i],param);
            cnt--;
            dec();
            if (err == DG_OK) {
                marks[i] = true;
            } else if (ret == DG_OK) {
                ret = err;
            }
        }
        dec(cnt);
        dvppProQ_.push(dvppProcess);
        return ret;
    }
};

}  // namespace acl
}  // namespace vega
#endif

#ifndef VEGA_ACL_CHANNEL_MANAGE
#define VEGA_ACL_CHANNEL_MANAGE
#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "srv/dvpp_common.h"
#include "rpc/rpc_processor.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "vpc_device.h"

namespace vega
{
    namespace acl
    {
        class AclImageVpc;

        enum aclVecChannelStatus
        {
            uninitialized,
            initialized, // initialized but not occupy，can be used directly,for image decode and encode
            occupy,
            bad
        };
        typedef struct
        {
            aclVecChannelStatus status;
            unsigned int last_erro;
        } AclVedcChannel;

        class AclChannelManageV2
        {
        public:
            AclChannelManageV2(int size)
            {
                lck.lock();
                channel.resize(size); // decode: [0,256) encode:[0,128)
                for (unsigned int i = 0; i < channel.size(); i++)
                {
                    channel[i].status = uninitialized;
                    channel[i].last_erro = 0;
                }
                lck.unlock();
            }
            ~AclChannelManageV2()
            {
            }

            int GetChannel()
            {
                lck.lock();
                int ch = -1;
                for (unsigned int i = 0; i < channel.size(); i++)
                {
                    if (channel[i].status == uninitialized)
                    {
                        ch = i;
                        channel[i].status = occupy;
                        channel[i].last_erro = 0;
                        break;
                    }
                }
                if (ch == -1)
                {
                    LOG(ERROR) << " video decoder all  "<<channel.size()<<" channel "<<"have been occupy!";
                }
                lck.unlock();
                return ch;
            }
            int GetChannel(int from)
            {
                if(from <0){
                    from =0;
                }
                lck.lock();
                int ch = -1;
                for (unsigned int i = from; i < channel.size(); i++)
                {
                    if (channel[i].status == uninitialized)
                    {
                        ch = i;
                        channel[i].status = occupy;
                        channel[i].last_erro = 0;
                        break;
                    }
                }
                if (ch == -1)
                {
                    LOG(ERROR) << " video decoder all  "<<channel.size()<<" channel "<<"have been occupy!";
                }
                lck.unlock();
                return ch;
            }

            void PushChannel(int id, unsigned int last_erro)
            {
                lck.lock();
                if (id >= 0 && (unsigned int)id < channel.size())
                {
                    channel[id].status = uninitialized;
                    if (last_erro != 0)
                    {
                        channel[id].last_erro = last_erro;
                        LOG(ERROR) << "vdec channel " << id << " is bad ,erro " << last_erro;
                    }
                }
                else
                {
                    LOG(ERROR) << "invalid channel " << id;
                }
                lck.unlock();
            }

        private:
            std::mutex lck;
            std::vector<AclVedcChannel> channel;
        };
        class AclChannelPool
        {
        public:
            using CreateChannel = std::function<DgError(int&)>;
            using DestroyChannel = std::function<DgError(int)>;
            AclChannelPool(){}
            AclChannelPool(int size, CreateChannel c, DestroyChannel d)
            {
                lck.lock();
                channel.resize(size); // decode: [0,256) encode:[0,128)
                for (unsigned int i = 0; i < channel.size(); i++)
                {
                    channel[i].status = uninitialized;
                    channel[i].last_erro = 0;
                }
                lck.unlock();
                createChannel = c;
                destroyChannel = d;
            }

            ~AclChannelPool() {}

            void FreeAllChannel()
            {
                lck.lock();
                if (isDeinit)
                {
                    lck.unlock();
                    return;
                }
                for (unsigned int i = 0; i < channel.size(); i++)
                {
                    if (channel[i].status == initialized)
                    {
                        auto err = destroyChannel(i);
                        if (err == DG_OK)
                        {
                            channel[i].status = uninitialized;
                        }
                        else
                        {
                            LOG(ERROR) << "Destroy channel " << i << "failed";
                            channel[i].status = bad;
                        }
                        channel[i].last_erro = err;
                    }
                }
                isDeinit = true;
                lck.unlock();
            }
            /// @brief  查找是否有可用通道，返回已创建通道，或者未初始化通道
            /// @param status 通道状态
            /// @return >0 通道号 < 0 无可用
            int GetChannel()
            {
                int ch = -1;
                lck.lock();
                for (int i = 0; i < (int)channel.size(); i++)
                {
                    if (channel[i].status == initialized) //已初始化通道直接返回
                    {
                        ch = i;
                        channel[i].status = occupy;
                        channel[i].last_erro = DG_OK;
                        break;
                    }
                    else if (channel[i].status == uninitialized) //未创建的可用通道
                    {
                        auto err = createChannel(i);
                        if (err == DG_OK)
                        {
                            ch = i;
                            channel[i].status = occupy;
                            channel[i].last_erro = DG_OK;
                            break;
                        }else{
                            channel[i].last_erro = err;
                        }
                    }
                }
                if (ch == -1)
                {
                    std::stringstream log;
                    log << "acl  channel hasn't free;";
                    for (unsigned int i = 0; i < channel.size(); i++)
                    {
                        log << "\nch" << i;
                        if (channel[i].status == initialized)
                        {
                            log << ",initialized";
                        }
                        else if (channel[i].status == occupy)
                        {
                            log << ",in useing";
                        }
                        else if (channel[i].status == uninitialized)
                        {
                            log << ", uninitialized last erro " << channel[i].last_erro;
                        }
                    }
                    LOG(ERROR) << log.str();
                }
                lck.unlock();
                return ch;
            }

            /// @brief  释放通道资源，只设置状态为free，不真正释放通道，便于复用
            /// @param id  通道号
            /// @param last_erro 最后错误码
            void PushChannel(int id, unsigned int last_erro)
            {
                lck.lock();
                channel[id].status = initialized;
                if (id >= 0 && (unsigned int)id < channel.size())
                {
                    channel[id].status = initialized;
                    if (last_erro != 0)
                    {
                        channel[id].last_erro = last_erro;
                        LOG(ERROR) << "vdec channel " << id << " is bad ,erro " << last_erro;
                    }
                }
                else
                {
                    LOG(ERROR) << "invalid channel " << id;
                }
                lck.unlock();
            }

        protected:
            CreateChannel createChannel;
            DestroyChannel destroyChannel;
            std::mutex lck;
            std::vector<AclVedcChannel> channel;
            bool isDeinit = false;
            int quality;
        };

    }
}

#endif
#include <sys/time.h>
#include <sys/prctl.h>
#include "image_device_encoder.h"
namespace vega
{
    namespace acl
    {
        class channelPoolJPGE : public AclChannelPool
        {
        public:
            using CreateJPGEChannel = std::function<DgError(int, int)>;
            channelPoolJPGE(int size, CreateJPGEChannel c, DestroyChannel d)
            {
                lck.lock();
                channel.resize(size); // decode: [0,256) encode:[0,128)
                for (unsigned int i = 0; i < channel.size(); i++)
                {
                    channel[i].status = uninitialized;
                    channel[i].last_erro = 0;
                }
                lck.unlock();
                createChannel = c;
                destroyChannel = d;
            }
            /// @brief  查找是否有可用通道，返回已创建通道，或者未初始化通道
            /// @param status 通道状态
            /// @return >0 通道号 < 0 无可用
            int GetChannel(int quality)
            {
                int ch = -1;
                lck.lock();
                for (unsigned int i = 0; i < channel.size(); i++)
                {
                    if (channel[i].status == initialized) //已初始化通道直接返回
                    {
                        ch = i;
                        channel[i].status = occupy;
                        channel[i].last_erro = DG_OK;
                        break;
                    }
                    else if (channel[i].status == uninitialized) //未创建的可用通道
                    {
                        auto err = createChannel(i, quality);
                        if (err == DG_OK)
                        {
                            ch = i;
                            channel[i].status = occupy;
                            channel[i].last_erro = DG_OK;
                            break;
                        }
                        else{
                            channel[i].last_erro = err;
                        }
                    }
                }
                if (ch == -1)
                {
                    std::stringstream log;
                    log << "acl  channel hasn't free;";
                    for (unsigned int i = 0; i < channel.size(); i++)
                    {
                        log << "\nch" << i;
                        if (channel[i].status == initialized)
                        {
                            log << ",initialized";
                        }
                        else if (channel[i].status == occupy)
                        {
                            log << ",in useing";
                        }
                        else if (channel[i].status == uninitialized)
                        {
                            log << ", uninitialized last erro " << channel[i].last_erro;
                        }
                    }
                    LOG(ERROR) << log.str();
                }
                lck.unlock();
                return ch;
            }

        private:
            CreateJPGEChannel createChannel;
        };

        static channelPoolJPGE channelPool(128, AclImageEncoderV2::createChannel, AclImageEncoderV2::destroyChannel);
        DgError AclImageEncoderV2::destroyChannel(int chn)
        {
            // 解码器停止接收码流
            auto ret = hi_mpi_venc_stop_chn(chn);
            if (ret != HI_SUCCESS)
            {
                LOG(ERROR) << "Failed to  stop jpege stream of channel " << chn << " !ret = " << std::hex << ret;
                return DG_ERR_DEINIT_FAIL;
            }

            // 释放资源 、去初始化
            ret = hi_mpi_venc_destroy_chn(chn);
            if (ret != HI_SUCCESS)
            {
                LOG(ERROR) << "Failed to  free jpege channel " << chn << "! ret = " << std::hex << ret;
                return DG_ERR_DEINIT_FAIL;
            }
            return DG_OK;
        }

        DgError AclImageEncoderV2::createChannel(int chn, int quality)
        {
            int maxWidth = 8192;
            int maxHeight = 8192;

            // 5.创建编码通道
            hi_venc_chn_attr attr{};                 // venc_attr 编码器属性 / rc_attr 码率控制器属性 / gop_attr GOP Mode类型的结构体。
            attr.venc_attr.type = HI_PT_JPEG;        // 编码协议类型
            attr.venc_attr.profile = 0;              //编码的等级，静态属性 jpeg取0,表示Baseline
            attr.venc_attr.max_pic_width = maxWidth; //编码图最大尺寸
            attr.venc_attr.max_pic_height = maxHeight;
            attr.venc_attr.pic_width = maxWidth;  // 编码图像宽度，必须是MIN_ALIGN的整数倍
            attr.venc_attr.pic_height = maxHeight; // 除jpeg协议外，编码图像高度，必须是MIN_ALIGN的整数倍。
            // attr.venc_attr.buf_size = DVPP_ALIGN_UP(aclImageSp->memory()->size(), 64); //码流buffer大小，单位为Byte，静态属性，必须是64的整数倍。
            attr.venc_attr.buf_size = 0;                                  // mode  zero copy
            attr.venc_attr.is_by_frame = HI_TRUE;                         // HI_TRUE：按帧获取 HI_FALSE：按包获取。
            attr.venc_attr.jpeg_attr.dcf_en = HI_FALSE;                   // RW; Range:[0, 1]; support dcf
            attr.venc_attr.jpeg_attr.recv_mode = HI_VENC_PIC_RECV_SINGLE; // RW; Config the receive mode;
            attr.venc_attr.jpeg_attr.mpf_cfg.large_thumbnail_num = 0;     // type hi_u8
            auto ret = hi_mpi_venc_create_chn(chn, &attr);
            if (ret != HI_SUCCESS)
            {
                if (ret == (signed)HI_ERR_VENC_EXIST)
                    return DG_ERR_ALREADY_EXIST;
                LOG(ERROR) << "Failed to create dvpp channel, ret = " << std::hex << ret;
                return DG_ERR_INIT_FAIL;
            }

            // 6.设置JPEGE参数
            hi_venc_jpeg_param param; // JPEG协议编码通道的高级参数集合,用于设置或者获取指定JPEGE编码通道的量化参数的数据结构
            ret = hi_mpi_venc_get_jpeg_param(chn, &param);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to get jepg param, ret = " << std::hex << ret;
                return DG_ERR_INIT_FAIL;
            }
            param.qfactor = quality; //编码质量范围0xFFFFFFFF或[1, 100]，数值越小图片质量越差，默认100
            ret = hi_mpi_venc_set_jpeg_param(chn, &param);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to set jpeg param, ret = " << std::hex << ret;
                return DG_ERR_INIT_FAIL;
            }

            // 7.通知编码器开始接收输入数据
            hi_venc_start_param recv_param{}; //接收图像参数结构体指针，用于指定需要接收的图像帧数。
            recv_param.recv_pic_num = -1;     //编码通道连续接收并编码的帧数，取值范围：-1或正整数。
            ret = hi_mpi_venc_start_chn(chn, &recv_param);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to start dvpp channel, ret = " << std::hex << ret;
                return DG_ERR_VENC_FAIL;
            }
            return DG_OK;
        }
        AclImageEncoderV2::~AclImageEncoderV2()
        {
            channelPool.FreeAllChannel();
        }

        AclImageEncoderV2::AclImageEncoderV2() {
            AclMemDvppSP acl_mem = std::make_shared<AclMemDvpp>();
            uint32_t block_size = 2 * 1024 * 1024;  // 2M=1hugepage;调用acldvppMalloc接口申请内存时，会对用户输入的size按向上对齐成32整数倍后，再多加32字节。
            uint32_t block_count = 64;
            auto vega_ret = acl_mem->create(block_size * block_count);
            if (vega_ret != DG_OK) {
                LOG(ERROR) << "Fail to create acl Mem,size " << block_size * block_count;
            } else {
                MemHeap_= std::shared_ptr<MemHeap>(new MemHeap(block_size, block_count, acl_mem->blob()), [=](void *p) {
                        auto mem = acl_mem;
                        mem.reset();
                        MemHeap *heap = (MemHeap *)p;
                        delete heap;
                    });
            }
        }
        /// @brief  将NV12编码成Jpeg图像
        /// @param input 输入图像，NV12，位于Device上
        /// @param output 输出图像，Jpeg，位于Host上
        /// @param quality 编码质量
        /// @return
        DgError AclImageEncoderV2::JpegEncode(MatrixSP &input, MatrixSP &output, int quality)
        {
            // Check input address and input type.
            if (nullptr == input)
            {
                LOG(ERROR) << "Invalid input matrix";
                return DG_ERR_INVALID_PARAM;
            }

            if (input->type() != MatrixType::NV12)
            {
                LOG(ERROR) << "Invalid input frame type:" << input->typestr() << ", only support nv12 !!!";
                return DG_ERR_INVALID_PARAM;
            }
            int chn = channelPool.GetChannel(quality);
            if (chn < 0)
            {
                LOG(ERROR) << "Get JPEGE channel failed ";
                return DG_ERR_INIT_FAIL;
            }
            DgError dgErr = CombineJpegeProcess(input, output, chn);
            if (dgErr != DG_OK)
            {
                LOG(ERROR) << "CombineJpegeProcess failed!";
            }
            channelPool.PushChannel(chn, DG_OK);
            return dgErr;
        }

        /// @brief  计算编码后所需内存大小，在device上预先开辟内存用来存储输出的jpeg
        /// @param frame 帧信息
        /// @param out 编码后的JPEG图像
        /// @return
        DgError AclImageEncoderV2::allocOutBuffer(hi_video_frame_info &frame, AclMemDvppSP &output)
        {
            hi_s32 ret = HI_SUCCESS;
            // get the size of output buffer
            hi_venc_jpeg_param stParamJpeg;
            hi_u32 out_buffer_size = 0;
            hi_u32 i;
            stParamJpeg.qfactor = 100;
            for (i = 0; i < HI_VENC_JPEG_QT_COEF_NUM; i++)
            {
                stParamJpeg.y_qt[i] = 255;
                stParamJpeg.cb_qt[i] = 255;
                stParamJpeg.cr_qt[i] = 255;
            }
            ret = hi_mpi_venc_get_jpege_predicted_size(&frame, &stParamJpeg, &out_buffer_size); //预估JPEGE编码一帧图片所需的输出缓冲区大小
            if (ret != HI_SUCCESS)
            {
                LOG(ERROR) << "Get predicted buffer size failed. " << std::hex << ret;
                return DG_ERR_VENC_FAIL;
            }
            // malloc output buffer
            output = std::make_shared<AclMemDvpp>();
            if (MemHeap_) {
                unsigned char *ptr = (unsigned char *)MemHeap_->alloc(out_buffer_size);
                if (ptr) {
                    RawStream data = RawStream(ptr, [=](void *data) {
                        if (MemHeap_) {
                            MemHeap_->free(ptr, out_buffer_size);
                        }
                    });
                    if (DG_OK != output->create(data, out_buffer_size, 1)) {
                        LOG(ERROR) << "Fail to creat acl memory !";
                    }
                } else {
                    LOG(WARNING) << "Fail to allocate memory of size " << out_buffer_size<< " from device memory heap!";
                }
            } else {
                LOG(ERROR) << "device memory heap has not been initialized.";
            }
            if(!output->good()){
                LOG(WARNING)<<"creat memory by acldvppMalloc!";
                auto erro = output->create(out_buffer_size, 1);
                if (erro != DG_OK){
                    LOG(ERROR) << "Fail to create  acl memory of size "<<out_buffer_size;
                    output = nullptr;
                    return DG_ERR_INVALID_PARAM;
                }
            }
            return DG_OK;
        }
        /// @brief  将YUV格式编码成JPEG格式
        /// @param input  输入NV12位于devide
        /// @param output  输出JPEG位于host
        /// @param quality 编码质量
        /// @return
        DgError AclImageEncoderV2::CombineJpegeProcess(MatrixSP &input, MatrixSP &output, int chn)
        {
            /*
             * 输入约束：
             * JPEG Encode分辨率: 32*32 ~ 8192*8192
             * 图片格式: YUV420SP(NV12, NV21)
             * widthStride: align(16)，兼容16倍数,128性能最优
             * heightStride: 等于图片高，或图片高度向上对齐到16数值
             * 输入内存：首地址128对齐，输入内存大小与图片格式相关
             */

            /*      输出约束：
                 格式：JPEG压缩格式的图片文件
                 输出内存：不需要用户管理输出内存，由DVPP内部管理内存。 */

            if (input->hasRoi())
            {
                MatrixSP dst;
                cv::Rect cropRect = input->roi();
                if (vpc.CropImage(input, dst, cropRect) < 0)
                {
                    LOG(ERROR) << "Failed to crop image";
                    return DG_ERR_VENC_FAIL;
                }
                input = dst;
            }
            // input is on the device
            std::shared_ptr<AclMemDvpp> aclMem = std::dynamic_pointer_cast<AclMemDvpp>(input->memory());
            if (aclMem == nullptr)
            {
                LOG(ERROR) << "input aclMem is nullptr";
                return DG_ERR_NOT_EXIST;
            }

            AclImageSP aclImageSp = std::dynamic_pointer_cast<AclImage>(input);
            if (!aclImageSp)
            {
                LOG(ERROR) << "input can't convert to aclImage";
                return DG_ERR_INVALID_IMAGE;
            }

            auto width = (aclImageSp->size().width+1)/2*2;
            auto height = (aclImageSp->size().height+1)/2*2;
            auto widthStride = aclImageSp->stride().width;
            auto heightStride = aclImageSp->stride().height;
            // 8.2 发送输入数据，开始编码
            hi_video_frame_info frame{}; //原始图像信息
            frame.mod_id = HI_ID_VENC;   // DVPP内的子模块ID，预留参数。
            frame.v_frame.width = width;
            frame.v_frame.height = height;
            frame.v_frame.field = HI_VIDEO_FIELD_FRAME;          //帧场模式 预留参数
            frame.v_frame.pixel_format = format_;                //视频图像像素格式。
            frame.v_frame.video_format = HI_VIDEO_FORMAT_LINEAR; //视频图像格式。在昇腾310 AI处理器上，使用VENC功能时，该参数为预留参数，暂不支持。
            frame.v_frame.compress_mode = HI_COMPRESS_MODE_NONE; //视频压缩模式
            frame.v_frame.dynamic_range = HI_DYNAMIC_RANGE_SDR8; //预留
            frame.v_frame.color_gamut = HI_COLOR_GAMUT_BT709;    //预留
            frame.v_frame.width_stride[0] = widthStride;         //输出图像分量的宽度数据跨距，输出图像分量的宽度数据跨距。YUV图像则为Y、U、V分量的数据跨距。RGB图像则为R、G、B分量的数据跨距。
            frame.v_frame.width_stride[1] = widthStride;         // U V
            // frame.v_frame.width_stride[2] = widthStride;         // U V
            frame.v_frame.virt_addr[0] = aclImageSp->memory()->blob();                                                    //图像在Device内存中的起始虚拟地址  Y
            frame.v_frame.virt_addr[1] = (hi_void *)((uintptr_t)frame.v_frame.virt_addr[0] + widthStride * heightStride); // U
            // frame.v_frame.virt_addr[2] = (hi_void *)((uintptr_t)frame.v_frame.virt_addr[1] + widthStride * heightStride); // V
            frame.v_frame.frame_flag = 0; //解码场景下，表示该帧解码是否成功，取值范围
            frame.v_frame.time_ref = 0;   //图像帧序列号。
            frame.v_frame.pts = 0;        //图像时间戳。

            hi_img_stream out_stream;
            hi_s32 ret;

            AclMemDvppSP out;
            auto err = allocOutBuffer(frame, out); //应该是device memory
            if (err != DG_OK)
            {
                LOG(ERROR) << "Malloc output iamge failed";
                return err;
            }
            out_stream.addr = out->blob();
            out_stream.len = out->size();
            ret = hi_mpi_venc_send_jpege_frame(chn, &frame, &out_stream, 10000);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to hi_mpi_venc_send_frame, ret = " << std::hex << ret;
                return DG_ERR_VENC_FAIL;
            }

            hi_venc_stream stream;
            stream.pack_cnt = 1;                             //一帧码流的所有包的个数。
            stream.pack = new hi_venc_pack[stream.pack_cnt]; //帧码流包结构。
            std::shared_ptr<hi_venc_pack> autoFreeVariable=std::shared_ptr<hi_venc_pack>(stream.pack,[&](void *data){
                hi_mpi_venc_release_stream(chn, &stream);
                delete [] stream.pack;
                stream.pack=0;
            });
            ret = hi_mpi_venc_get_stream(chn, &stream, -1);  //获取编码的码流,采用阻塞方式
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to hi_mpi_venc_get_stream, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }
            auto outputSize = stream.pack[0].len - stream.pack[0].offset;
            // output should be on the host memory
            output = std::make_shared<Matrix>();
            auto erro = output->create(MatrixType::JPEG, cv::Size(outputSize, 1));
            if (erro != DG_OK)
            {
                LOG(ERROR) << "Create  image on host  failed!";
                output = nullptr;
                return DG_ERR_INVALID_PARAM;
            }
            ret = aclrtMemcpy(output->memory()->blob(), outputSize, stream.pack[0].addr + stream.pack[0].offset, outputSize, ACL_MEMCPY_DEVICE_TO_HOST);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to hi_mpi_venc_get_stream, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }
            return DG_OK;
        }

    }
}

#include <sys/time.h>
#include <sys/prctl.h>
#include "video_device_encoder.h"
namespace vega
{
    namespace acl
    {
        static AclChannelManageV2 aclChannelManageEnc(128);

        DgError AclVencProcV2::Init(const aclrtStream &stream)
        {
            return DG_OK;
        }
        /// @brief  创建通道、接收线程、资源初始化
        /// @param from 源数据类型
        /// @param to 编码类型
        /// @param param 视频信息
        /// @param cb 回调函数
        /// @return
        DgError AclVencProcV2::CreateVenc(MatrixType from, MatrixType to, const VencBuffer::VencParam &param, VencBuffer::VencCallback cb)
        {
            if (from != MatrixType::NV12)
            {
                LOG(ERROR) << "Invalid input frame type:" << (int)from << ", only support nv12";
                return DG_ERR_INVALID_PARAM;
            }

            switch (to)
            {
            case MatrixType::H264:
                encodeType_ = HI_PT_H264;
                break;
            case MatrixType::H265:
                encodeType_ = HI_PT_H265;
                break;
            default:
                LOG(ERROR) << "Invalid venc stream Format type:" << (int)to << ", only support h264 | h265";
                return DG_ERR_INVALID_PARAM;
            }

            vencParam_ = param;
            vencCallback_ = cb;
            maxWidth_ = vencParam_.size.width;
            maxHeight_ = vencParam_.size.height;
            //缩放
            encSize = vencParam_.size;
            if ((vencParam_.resizeRatio != 1.0f) && !CheckResizeRatio(vencParam_.resizeRatio))
            {
                int w = (int)vencParam_.size.width * vencParam_.resizeRatio;
                int h = (int)vencParam_.size.height * vencParam_.resizeRatio;
                encSize.width = DVPP_ALIGN_UP(w, 16);
                encSize.height = DVPP_ALIGN_UP(h, 2);
                maxWidth_ = encSize.width;
                maxHeight_ = encSize.height;
                LOG(ERROR) << "video " << vencParam_.size.width << "x" << vencParam_.size.height
                           << " resize to " << encSize.width << "x" << encSize.height
                           << ", ratio: " << vencParam_.resizeRatio;
            }

            auto ret = InitResource((uint64_t)threadId_);
            if (ret != DG_OK)
            {
                return DG_ERR_INIT_FAIL;
            }

            //控制接收线程退出
            bQuit_ = false;
            // create acl report threadId
            thread_destroy_mutex_.lock();
            thread_destroy_ = false;
            thread_destroy_mutex_.unlock();
            if (pthread_create(&threadId_, nullptr, AclReportThread_, this) != 0)
            {
                LOG(ERROR) << "Failed to create receive  thread";
                return DG_ERR_INIT_FAIL;
            }
            return DG_OK;
        }

        DgError AclVencProcV2::DestroyVenc(void)
        {
            bQuit_ = true;
            return DG_OK;
        }
        /// @brief  销毁通道、系统去初始化
        /// @param
        /// @return
        DgError AclVencProcV2::FreeResource(void)
        {
            if (threadId_ != 0)
                pthread_join(threadId_, NULL);
            threadId_ = 0;
            auto ret = hi_mpi_venc_stop_chn(channelId_);
            if (ret != HI_SUCCESS)
            {
                LOG(ERROR) << "Stop channel " << channelId_ << " receive stream failed. " << std::hex << ret;
            }
            ret = hi_mpi_venc_destroy_chn(channelId_);
            if (ret != HI_SUCCESS)
            {
                LOG(ERROR) << "Destroy channel " << channelId_ << "  failed. " << std::hex << ret;
            }
           
	        cb_frame_mutex_.lock ();
    	    cb_frame_.clear();
	        cb_frame_mutex_.unlock ();
            LOG(ERROR)<<"FreeResource channel"<< channelId_<<" successfully!";
            return DG_OK;
        }

        void *AclVencProcV2::AclReportThread_(void *arg)
        {
            prctl(PR_SET_NAME, "AclVEncReportThread");
            auto aclVdec = static_cast<AclVencProcV2 *>(arg);
            if (aclVdec != nullptr)
            {
                return aclVdec->recvStreamThread();
            }
            return nullptr;
        }

        /// @brief  接收编码后的码流
        /// @param
        /// @return
        void *AclVencProcV2::recvStreamThread(void)
        {
            auto ret = aclrtSetCurrentContext(context_);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "recvStreamThread set context error. " << std::hex << ret;
                return nullptr;
            }
            hi_venc_stream stream;
            hi_venc_chn_status stat;
            FrameId frameId = -1;

            while (1)
            {
                bool exit=false;
		        thread_destroy_mutex_.lock();
                if (thread_destroy_)
                {
                    thread_destroy_mutex_.unlock();
                    pthread_exit(nullptr);
		            exit=true;
                }
                thread_destroy_mutex_.unlock();
                if(exit)
                {
		            return nullptr;
                }


                ret = hi_mpi_venc_query_status(channelId_, &stat);
                if (ret != HI_SUCCESS)
                {
                    LOG(ERROR) << "Failed to hi_mpi_venc_query_status, ret = " << std::hex << ret;
                    continue;
                }
                if (stat.cur_packs == 0)
                {
                    continue;
                }

                stream.pack_cnt = stat.cur_packs;                        //一帧码流的所有包的个数=当前帧的码流包个数
                stream.pack = new hi_venc_pack[stream.pack_cnt];         //帧码流包结构。
                ret = hi_mpi_venc_get_stream(channelId_, &stream, 1000); //获取编码的码流,采用阻塞方式
                if (ret != HI_SUCCESS)
                {
                    if (ret == (hi_s32)HI_ERR_VENC_BUF_EMPTY)
                    {
	                    delete [] stream.pack;
       		            stream.pack = NULL;
                        continue;
                    }
                    LOG(ERROR) << "Failed to hi_mpi_venc_get_stream, ret = " << std::hex << ret;
                    EncodeDone(nullptr, 0);
               	    delete [] stream.pack;
                    stream.pack = NULL;
                    continue;
                }

                for (unsigned int i = 0; i < stream.pack_cnt; i++)
                {
                    auto outputSize = stream.pack[i].len - stream.pack[i].offset;
                    EncodeDone(stream.pack[i].addr + stream.pack[i].offset, outputSize);
                    // release acl mem
                    frameId = stream.pack[i].pts;
                    cb_frame_mutex_.lock();
                    auto it = cb_frame_.find(frameId);
                    if (it != cb_frame_.end()) {
                        cb_frame_.erase(it);
                    }
                    cb_frame_mutex_.unlock();
                }
                getFrame_ += stream.pack_cnt;
                ret = hi_mpi_venc_release_stream(channelId_, &stream);
                if (ret != HI_SUCCESS)
                {
                    LOG(ERROR) << "free stream failed " << std::hex << ret;
                }
                delete [] stream.pack;
                stream.pack = NULL;
            }
            return nullptr;
        }

        void *AclVencProcV2::AclReportThread(void)
        {
            vegaSetCurrentContext();
            while (!bQuit_)
            {
                (void)aclrtProcessReport(30); // 30ms
            }
            return nullptr;
        }

        /// @brief  初始化资源，设置编码参数
        /// @param threadId
        /// @param format 源数据类型
        /// @param enType  编码视频格式
        /// @return
        DgError AclVencProcV2::InitResource(uint64_t threadId)
        {
            aclError aclRet = aclrtGetRunMode(&runMode_);
            if (aclRet != ACL_SUCCESS)
            {
                LOG(ERROR) << "Get run mode error " << aclRet;
                return DG_ERR_INIT_FAIL;
            }
            auto ret = aclrtGetCurrentContext(&context_);
            if (ret != ACL_SUCCESS)
            {
                LOG(ERROR) << "Failed to get current context." << std::hex << ret;
                return DG_ERR_INIT_FAIL;
            }

            frameId_ = 0;

            hi_venc_chn_attr attr{};
            /*****编码器属性*****/
            attr.venc_attr.type = encodeType_; //编码类型
            attr.venc_attr.profile = 0;        //编码等级，越大质量越好 H.264 [0,3] H265[0,1]
            attr.venc_attr.max_pic_width = maxWidth_;
            attr.venc_attr.max_pic_height = maxHeight_;
            attr.venc_attr.pic_width = maxWidth_;
            attr.venc_attr.pic_height = maxHeight_;
            attr.venc_attr.is_by_frame = HI_TRUE; //按帧/包获取

            if (encodeType_ == HI_PT_H264)
            {
                attr.venc_attr.h264_attr.rcn_ref_share_buf_en = HI_FALSE; //是否使能参考帧和回写帧复用bñffe
                attr.venc_attr.buf_size = 1024 * 1024 * 4;                // stream buffer size 4M:1024 * 1024 * 4
                attr.rc_attr.rc_mode = HI_VENC_RC_MODE_H264_VBR;          // rc模式 todo: vbr/cbr
                // H.264协议编码通道VBR模式属性
                attr.rc_attr.h264_vbr.gop = 30;            //一个图像组的帧数 [1, 65536]
                attr.rc_attr.h264_vbr.stats_time = 1;      // VBR码率统计时间，以秒为单位
                attr.rc_attr.h264_vbr.src_frame_rate = 30; //输入码率
                attr.rc_attr.h264_vbr.dst_frame_rate = 30; //输出码率
                attr.rc_attr.h264_vbr.max_bit_rate = 4000; //编码器最大输出码率
            }
            else if (encodeType_ == HI_PT_H265)
            {
                attr.venc_attr.h265_attr.rcn_ref_share_buf_en = HI_FALSE; //是否使能参考帧和回写帧复用bñffe
                attr.venc_attr.buf_size = 1024 * 1024 * 2;                // stream buffer size 2M:1024 * 1024 * 2
                attr.rc_attr.rc_mode = HI_VENC_RC_MODE_H265_VBR;          // rc模式
                // H.265协议编码通道VBR模式属性
                attr.rc_attr.h265_vbr.gop = 30;
                attr.rc_attr.h265_vbr.stats_time = 1;
                attr.rc_attr.h265_vbr.src_frame_rate = 30;
                attr.rc_attr.h265_vbr.dst_frame_rate = 30;
                attr.rc_attr.h265_vbr.max_bit_rate = 4000;
            }
            else
            {
                LOG(ERROR) << "Not supported encodeType" << encodeType_;
                return DG_ERR_INVALID_PARAM;
            }

            /*****帧结构类型*****/
            attr.gop_attr.gop_mode = HI_VENC_GOP_MODE_NORMAL_P; //编码GOP类型,编码单参考帧P帧GOP类型
            attr.gop_attr.normal_p.ip_qp_delta = 3;             // I帧相对P帧的QP（Quantisation Parameter）差值。 取值范围：[-10, 30]。

            int i=0;
            for(;i<MAX_ENCODER_CHANNEL;i++){
            	ret = hi_mpi_venc_create_chn(i, &attr);
                if (ret == HI_SUCCESS) {
		            break;
                }
            }
 
            if (i >=MAX_ENCODER_CHANNEL){
                LOG(FATAL) << "create video encode channel all failed ";
                return DG_ERR_INIT_FAIL;
            }
	        channelId_=i; 
            LOG(ERROR) << "Create video encode channel " << channelId_ << " successfully!";

            //启动编码器
            hi_venc_start_param recv_param{}; //指定需要接收的图像帧数
            recv_param.recv_pic_num = -1;     //编码通道连续接收并编码的帧数 ，-1/正整数
            ret = hi_mpi_venc_start_chn(channelId_, &recv_param);
            if (ret != HI_SUCCESS)
            {
                LOG(ERROR) << "Start video encode channel error " << std::hex << ret;
                return DG_ERR_INIT_FAIL;
            }
            return DG_OK;
        }

        /// @brief  等待编码完成
        void AclVencProcV2::waitEncFinish()
        {
            if (frameId_ != getFrame_){
		        sleep(1);
		        LOG(ERROR)<<"video encoder send number "<<frameId_<<",get frame number "<<getFrame_;
	        }
            frameId_=0;
            getFrame_=0;
            thread_destroy_mutex_.lock();
            thread_destroy_ = true;
            thread_destroy_mutex_.unlock();
            FreeResource();
            return;
        }

        DgError AclVencProcV2::VencSendEosFrame(void)
        {
            thread_destroy_mutex_.lock();
            thread_destroy_ = true;
            thread_destroy_mutex_.unlock();
            return DG_OK;
        }
        /// @brief  将NV12编码成视频格式
        /// @param input 输入的NV12帧
        /// @param kIntv
        /// @param forceIFrame I帧
        /// @param resizeRatio 缩放比例
        /// @param videoEos 结束帧标志
        /// @return
        DgError AclVencProcV2::EncodeVideo(MatrixSP input, int kIntv, bool forceIFrame, float resizeRatio, bool videoEos)
        {
            if (videoEos)
            {
                VencSendEosFrame();
                waitEncFinish();
                return DG_OK;
            }

            if (input == nullptr)
            {
                LOG(ERROR) << "Invalid input matrix";
                return DG_ERR_INVALID_PARAM;
            }

            if (input->type() != MatrixType::NV12)
            {
                LOG(ERROR) << "Invalid input frame type, Only surport nv12";
                return DG_ERR_INVALID_PARAM;
            }
            if ((resizeRatio != vencParam_.resizeRatio) && !CheckResizeRatio(resizeRatio))
            {
                VencSendEosFrame();
                thread_destroy_mutex_.lock();
                thread_destroy_ = true;
                thread_destroy_mutex_.unlock();
                FreeResource();

                // re-init ven configuration 重新初始化编码配置
                int w = (int)vencParam_.size.width * vencParam_.resizeRatio;
                int h = (int)vencParam_.size.height * vencParam_.resizeRatio;
                encSize.width = DVPP_ALIGN_UP(w, 16);
                encSize.height = DVPP_ALIGN_UP(h, 2);
                vencParam_.resizeRatio = resizeRatio;
                maxWidth_ = encSize.width;
                maxHeight_ = encSize.height;
                if (InitResource((uint64_t)threadId_) != DG_OK)
                {
                    return DG_ERR_INIT_FAIL;
                }
                thread_destroy_mutex_.lock();
                thread_destroy_ = false;
                thread_destroy_mutex_.unlock();
                //创建接收线程
                if (pthread_create(&threadId_, nullptr, AclReportThread_, this) != 0)
                {
                    LOG(ERROR) << "Failed to create receive  thread";
                    return DG_ERR_INIT_FAIL;
                }
            }

            std::shared_ptr<AclMemDvpp> aclMem = std::dynamic_pointer_cast<AclMemDvpp>(input->memory());
            if (aclMem == nullptr)
            {
                LOG(ERROR) << "Input aclMem is nullptr";
                return DG_ERR_NOT_EXIST;
            }

            if (vencParam_.resizeRatio != 1.0f && !CheckResizeRatio(resizeRatio))
            {
                MatrixSP dst;
                if (vpc.ResizeImage(input, dst, vencParam_.resizeRatio) != DG_OK)
                {
                    return DG_ERR_DECODE_FAIL;
                }
                input = dst;
            }

            //输入输出约束;
            auto width = input->size().width;
            auto height = input->size().height;
            auto widthStride = input->stride().width;
            auto heightStride = input->stride().height;
            hi_video_frame_info frame{};
            frame.pool_id = 0;
            frame.mod_id = HI_ID_VENC; //预留参数
            frame.v_frame.width = width;
            frame.v_frame.height = height;
            frame.v_frame.field = HI_VIDEO_FIELD_FRAME;                      //帧场模式,预留
            frame.v_frame.pixel_format = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420; //像素格式NV12
            frame.v_frame.video_format = HI_VIDEO_FORMAT_LINEAR;             //视频格式
            frame.v_frame.compress_mode = HI_COMPRESS_MODE_NONE;
            frame.v_frame.dynamic_range = HI_DYNAMIC_RANGE_SDR8; //动态范围
            frame.v_frame.color_gamut = HI_COLOR_GAMUT_BT709;    //色域范围
            frame.v_frame.width_stride[0] = widthStride;
            frame.v_frame.width_stride[1] = widthStride;
            frame.v_frame.virt_addr[0] = input->memory()->blob();                                                                  // Y
            frame.v_frame.virt_addr[1] = (hi_void *)((uintptr_t)frame.v_frame.virt_addr[0] + widthStride * heightStride); // U、V
            frame.v_frame.pts = frameId_++;                                                                               //时间戳
            frame.v_frame.time_ref = frame.v_frame.pts * 2;
            
            // Cache frame information
            HI_FRAME_SP frameSp;
            frameSp.inMem = std::dynamic_pointer_cast<AclMemDvpp>(input->memory());
            cb_frame_mutex_.lock();
            cb_frame_[frame.v_frame.pts] = frameSp;
            cb_frame_mutex_.unlock();

            signed int ret;
            ret = hi_mpi_venc_send_frame(channelId_, &frame, -1);
            if (ret != HI_SUCCESS)
	        {
            	cb_frame_mutex_.lock();
                auto it = cb_frame_.find(frame.v_frame.pts);
                if (it != cb_frame_.end()) {
                   cb_frame_.erase(it);
                }
                cb_frame_mutex_.unlock();
                LOG(ERROR) << "hi_mpi_venc_send_frame failed. " << std::hex << ret;
		        return DG_ERR_VENC_FAIL;
	        }

            return DG_OK;
        }

        /// @brief  编码完成，将编码结果回调
        /// @param dataDev  dev上存放数据的指针
        /// @param dataSize  数据大小
        void AclVencProcV2::EncodeDone(const void *dataDev, uint32_t dataSize)
        {
            if (dataDev == nullptr || dataSize < 4)
            {
                vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                return;
            }

            MatrixType type = MatrixType::Undefined;
            switch (encodeType_)
            {
            case HI_PT_H264:
            {
                type = MatrixType::H264;
            }
            break;
            case HI_PT_H265:
            {
                type = MatrixType::H265;
            }
            break;
            default:
                LOG(ERROR) << "Invalid stream type, only support h264 | h265";
                vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                return;
            }

            MatrixAlign align;
            align.mode_ = MatrixAlignMode::MEMORY;
            align.mem_ = 1;
            align.stride_ = {1, 1};

            cv::Size size(dataSize, 1);

            MatrixProperty prop(VegaDataType::CHAR);
            if (DG_OK != prop.create(type, size, align))
            {
                LOG(ERROR) << "MatrixProperty create failed";
                vencCallback_(nullptr, DG_ERR_VENC_FAIL);
            }
            else
            {
                auto dst = std::make_shared<Matrix>();
                if (DG_OK != dst->create(prop, align.mem_))
                {
                    LOG(ERROR) << "Matrix create failed";
                    vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                    return;
                }

                // copy output to host memory
                auto dataHost = dst->memory()->blob();
                auto aclRet = aclrtMemcpy(dataHost, dataSize, dataDev, dataSize, ACL_MEMCPY_DEVICE_TO_HOST);
                if (aclRet != ACL_SUCCESS)
                {
                    LOG(ERROR) << "acl memcpy data to host failed, dataSize:" << dataSize << ", ret = " << aclRet;
                    vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                }
                else
                {
                    vencCallback_(dst, DG_OK);
                }
            }
        }

        /// @brief
        /// @param resizeRatio
        /// @return
        int AclVencProcV2::CheckResizeRatio(const float &resizeRatio)
        {
            if (resizeRatio < MIN_RESIZE_SCALE || resizeRatio > MAX_RESIZE_SCALE)
            {
                LOG(ERROR) << "Resize scale should be in range [1/32, 16], which is " << resizeRatio;
                return -1;
            }
            return 0;
        }
    }
}

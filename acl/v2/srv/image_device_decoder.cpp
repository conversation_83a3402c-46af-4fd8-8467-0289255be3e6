#include <sys/prctl.h>
#include <sys/time.h>

#include "common/utils/jpeg_exif.h"
#include "common/utils/jpegd_check/JpegdCheck.h"
#include "image_device_decoder.h"

namespace vega {
    extern bool isAclSoc();
namespace acl {
// static AclChannelManage aclChannelManage2(256);
static AclChannelPool channelPool(256, AclImageDecoderV2::createChannel, AclImageDecoderV2::destroyChannel);

AclImageDecoderV2::~AclImageDecoderV2() {
    channelPool.FreeAllChannel();
}
/// @brief  销毁通道、解码器停止接收码流
DgError AclImageDecoderV2::destroyChannel(int chn) {
    auto ret = hi_mpi_vdec_stop_recv_stream(chn);
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Failed to  stop vdec stream of channel " << chn << " !ret = " << std::hex << ret;
        return DG_ERR_DECODE_FAIL;
    }

    // 释放资源 、去初始化
    ret = hi_mpi_vdec_destroy_chn(chn);
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Failed to  free vdec chanel " << chn << "! ret = " << std::hex << ret;
        return DG_ERR_DECODE_FAIL;
    }
    return DG_OK;
}

/// @brief  根据chnId创建通道
/// @param chn  解码通道号
/// @return
DgError AclImageDecoderV2::createChannel(int& chn) {
    //创建通道
    hi_vdec_chn_attr chnAttr;
    chnAttr.type = HI_PT_JPEG;               // video type to be decoded
    chnAttr.mode = HI_VDEC_SEND_MODE_FRAME;  // send by stream or by frame
    // max picture size
    chnAttr.pic_width = MAX_JPEGD_WIDTH;
    chnAttr.pic_height = MAX_JPEGD_HEIGHT;
    chnAttr.stream_buf_size = chnAttr.pic_width * chnAttr.pic_height * 3 / 2;
    auto ret = hi_mpi_vdec_create_chn(chn, &chnAttr);
    if (ret != ACL_SUCCESS) {
        if (ret == (signed)HI_ERR_VDEC_EXIST) {
            LOG(ERROR) << "Channel " << chn << " has created. " << std::hex << ret;
            return DG_ERR_ALREADY_EXIST;
        }
        LOG(ERROR) << "Failed to  create channel " << chn << "!  ret = " << std::hex << ret;
        return DG_ERR_INIT_FAIL;
    }

    // 设置通道属性，
    hi_vdec_chn_param chnParam;
    // 3.1获取通道参数，必须保证通道已创建
    ret = hi_mpi_vdec_get_chn_param(chn, &chnParam);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to  get Channel Param!  ret = " << ret;
        return DG_ERR_INIT_FAIL;
    }
    chnParam.pic_param.pixel_format = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;  // out put pixel format
    chnParam.pic_param.alpha = 255;                                        // RGB decoding transparency
    chnParam.display_frame_num = 0;                                        //解码后缓存图像帧数
    //设置解码通道参数
    ret = hi_mpi_vdec_set_chn_param(chn, &chnParam);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to  set decode channel failed! ret = " << ret;
        return DG_ERR_INIT_FAIL;
    }

    //解码器启动接收码流
    ret = hi_mpi_vdec_start_recv_stream(chn);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to  start channel! ret = " << ret;
        return DG_ERR_VDEC_FAIL;
    }
    return DG_OK;
}

/// @brief  对Jpeg图片解码
/// @param input 输入图片、peg类型、位于host上
/// @param output  输出图片、位于device上
/// @param outputType 只支持NV12
/// @return
DgError AclImageDecoderV2::JpegDecode(const MatrixSP &input, MatrixSP &output, MatrixType outputType) {
    /*            输入约束：
                    支持格式：.jpg、.jpeg、.JPG、.JPEG
                    分辨率： 最大分辨率：8192*8192，最小分辨率：32*32
                     输入内存： 首地址要求128对齐，输入内存的大小就是指实际的输入图片所占用的大小 */

    /*            输出约束：
                    输出图片格式：针对不同的源图片格式
                    输出内存：输出内存首地址要求128字节对齐，输出图片的widthStride（对齐后的宽度），对齐到64；
                    输出图片的heightStride（对齐后的高度），对齐到16。 */
    if (nullptr == input) {
        LOG(ERROR) << "Invalid input matrix";
        return DG_ERR_INVALID_PARAM;
    }
    auto size = input->memory()->size();
    auto data = input->memory()->blob();
    if (size <= 0 || data == nullptr) {
        LOG(ERROR) << "Invalid input image, size: " << size;
        return DG_ERR_INVALID_PARAM;
    }

    if (input->type() != MatrixType::JPEG) {
        LOG(ERROR) << "Invalid input image type:" << input->typestr() << ", not support !!!";
        return DG_ERR_INVALID_PARAM;
    }

    if (outputType != MatrixType::NV12) {
        LOG(ERROR) << "Invalid output image type:" << matrix_type_str(outputType) << ", not support !!!";
        return DG_ERR_INVALID_PARAM;
    }

    // Check jpeg 'Encoding Process'
    JPEG_CHECK::JpegdCheck jpegdCheck;
    struct JPEG_CHECK::jpeg_decompress_struct libjpegHandler;
    if (!jpegdCheck.IsCaseValid(data, size, libjpegHandler)) {
        LOG(ERROR) << "jpegdCheck IsCaseValid = false";
        return DG_ERR_FULL;
    }
    int chn = channelPool.GetChannel();
    if (chn < 0) {
        LOG(ERROR) << "Get vdec channel failed";
        return DG_ERR_VDEC_FAIL;
    }
    auto dgErr = CombineJpegDecProcess(input, output, chn);
    if (dgErr != DG_OK) {
        LOG(ERROR) << "CombineJpegDecProcess Error, error  " << dgErr;
    }
    channelPool.PushChannel(chn, DG_OK);
    return dgErr;
}

/// @brief  解码图片、发送码流、采用阻塞方式获取结果
/// @param input
/// @param output
/// @param chn
/// @return
DgError AclImageDecoderV2::CombineJpegDecProcess(const MatrixSP &input, MatrixSP &output, int chn) {
    uint32_t width = 0;
    uint32_t height = 0;
    auto inputSize = input->memory()->size();

    // 获取软件栈的运行模式
    aclrtRunMode runMode;
    auto ret = aclrtGetRunMode(&runMode);  // ACL_DEVICE or ACL_HOST / 0 or 1
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to  get acl run mode! ret = " << ret;
        return DG_ERR_INIT_FAIL;
    }

    // 发送码流
    // 申请输入内存
    auto inMemDev = std::make_shared<AclMemDvpp>();
    if(isAclSoc()){
        RawStream data = RawStream(input->data(), [=](void *data) {
            });
        if(DG_OK!=inMemDev->create(data, input->memory()->size(), 1)){
            LOG(ERROR)<<"Fail to creat acl memory !";
            return DG_ERR_VDEC_FAIL;
        }
    }else{
        ret = inMemDev->fromHost(input->memory());
        if (ret != DG_OK) {
            LOG(ERROR) << "Fail to acl mem copy from host!";
            return DG_ERR_VDEC_FAIL;
        }
    }

    // 构造存放输入图片信息的结构体
    hi_vdec_stream stStream{};
    hi_img_info stImgInfo{};
    stStream.pts = 0;
    if (runMode == ACL_HOST) {
        stStream.addr = (uint8_t *)input->memory()->blob();
    } else {
        stStream.addr = (uint8_t *)inMemDev->blob();
    }
    stStream.len = inputSize;
    stStream.end_of_frame = HI_TRUE;
    stStream.end_of_stream = HI_FALSE;
    stStream.need_display = HI_TRUE;

    //  获取输入图片信息
    ret = hi_mpi_dvpp_get_image_info(
      HI_PT_JPEG,
      &stStream,
      &stImgInfo);  //对于昇腾710
                    // AI处理器，标准形态下，在Host上调用本接口时，该结构体内的addr参数配置的地址必须是Host上的内存地址。
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to  get image info, ret = " << ret;
        return DG_ERR_VDEC_FAIL;
    }
    stStream.addr = (uint8_t *)inMemDev->blob();
    // check
    width = stImgInfo.width;
    height = stImgInfo.height;
    if (width > MAX_JPEGD_WIDTH || width < MIN_JPEGD_WIDTH) {
        LOG(ERROR) << "Input width is invalid, not in [" << MIN_JPEGD_WIDTH << ", " << MAX_JPEGD_WIDTH << "].";
        return DG_ERR_VDEC_FAIL;
    }

    if (height > MAX_JPEGD_HEIGHT || height < MIN_JPEGD_HEIGHT) {
        LOG(ERROR) << "Input height is invalid, not in [" << MIN_JPEGD_HEIGHT << ", " << MAX_JPEGD_HEIGHT << "].";
        return DG_ERR_VDEC_FAIL;
    }
    // 构造存放输出图片信息的结构体，并申请输出内存
    hi_vdec_pic_info outPicInfo{};
    outPicInfo.width = stImgInfo.width;
    outPicInfo.height = stImgInfo.height;
    outPicInfo.width_stride = stImgInfo.width_stride;
    outPicInfo.height_stride = stImgInfo.height_stride;
    outPicInfo.buffer_size = stImgInfo.width_stride * stImgInfo.height_stride * 3 / 2;
    outPicInfo.pixel_format = HI_PIXEL_FORMAT_YUV_SEMIPLANAR_420;
    auto mem = std::make_shared<AclMemDvpp>();
    auto err = mem->create(outPicInfo.buffer_size, 1);
    if (err != DG_OK) {
        LOG(ERROR) << "Maclloc device memory for output failed";
        return DG_ERR_VDEC_FAIL;
    }
    outPicInfo.vir_addr = (uint64_t)mem->blob();

    // 发送需解码的输入图片
    ret = hi_mpi_vdec_send_stream(chn, &stStream, &outPicInfo, 0);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Failed to  send picture to be decoded, ret = " << std::hex << ret;
        return DG_ERR_VDEC_FAIL;
    }

    // 接收解码结果
    // 获取解码结果
    hi_video_frame_info frame;
    ret = hi_mpi_vdec_get_frame(chn, &frame, NULL, &stStream, 1000);
    if (ret == ACL_SUCCESS) {
        auto decResult = frame.v_frame.frame_flag;
        // 隔行码流
        if(decResult == 2){
            LOG(INFO) << "interlaced stream decResult = 2";
        }else if(decResult != 0) {
            LOG(ERROR) << "Failed to  get decode result, ret = "  << decResult ;
            return DG_ERR_VDEC_FAIL;
        }
    } else {
        LOG(ERROR) << "Failed to  get decode result, ret = " << std::hex << ret;
        return DG_ERR_VDEC_FAIL;
    }

    // 如果运行模式为ACL_HOST，且Host上需要展示JPEGD输出的图片数据，则需要申请Host内存，通过aclrtMemcpy接口将Device的输出图片数据传输到Host
    AclImageSP dst = std::make_shared<AclImage>();
    err = dst->create(MatrixType::NV12,
                      cv::Size(outPicInfo.width, outPicInfo.height),
                      mem,
                      1,
                      outPicInfo.width_stride,
                      outPicInfo.height_stride);
    if (err != DG_OK) {
        LOG(ERROR) << "Failed to create output frame for image";
        return DG_ERR_VDEC_FAIL;
    }
    if (dst->reqMemSize() > (int)outPicInfo.buffer_size) {
        LOG(ERROR) << "Fail because dataSize " << outPicInfo.buffer_size << " < reqMemSize " << dst->reqMemSize()
                   << " !";
        return DG_ERR_VDEC_FAIL;
    }
    output = dst;

    // 释放frame
    ret = hi_mpi_vdec_release_frame(chn, &frame);
    if (ret != HI_SUCCESS) {
        LOG(ERROR) << "Failed to free frame" << std::hex << ret;
    }
    return DG_OK;
}
}  // namespace acl
}  // namespace vega

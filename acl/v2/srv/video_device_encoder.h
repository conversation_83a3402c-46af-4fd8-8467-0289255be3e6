#ifndef VEGA_ACL_VIDEO_DEVICE_ENCODER_V2_H
#define VEGA_ACL_VIDEO_DEVICE_ENCODER_V2_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <atomic>
#include "error.h"
#include "vega_matrix.h"
#include "glog/logging.h"
#include "dg_base_types.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "srv/dvpp_common.h"
#include "common/Platform/processor/video_encoder_buffer.h"
#include "vpc_device.h"
#define MAX_ENCODER_CHANNEL 128
namespace vega
{
    namespace acl
    {

        class AclVencProcV2 : public AclVencProcBase
        {
        public:
            typedef struct {
                AclMemDvppSP inMem;
            } HI_FRAME_SP;

            AclVencProcV2()
            {
                threadId_ = 0;
            }
            ~AclVencProcV2()
            {
                DestroyVenc();
            }
            AclVencProcV2 &operator=(const AclVencProcV2 &) = delete;

            DgError Init(const aclrtStream &stream); // set stream_

            DgError CreateVenc(MatrixType from, MatrixType to, const VencBuffer::VencParam &param, VencBuffer::VencCallback cb);
            DgError DestroyVenc(void);
            DgError EncodeVideo(MatrixSP input, int kIntv, bool forceIFrame, float resizeRatio, bool videoEos);

        protected:
            static void *AclReportThread_(void *arg);
            void *AclReportThread(void);
            void *recvStreamThread(void);
            DgError InitResource(uint64_t threadId);
            DgError VencSendEosFrame(void);
            DgError FreeResource(void);
            DgError vpcResize(MatrixSP &input);

            void EncodeDone(const void *dataDev, uint32_t dataSize);
            void waitEncFinish();
            int CheckResizeRatio(const float &resizeRatio);

        private:
            uint32_t channelId_ = 32; //[0,128)
            aclrtRunMode runMode_ = ACL_HOST;
            bool bQuit_ = true;
            pthread_t threadId_ = 0;
            VencBuffer::VencParam vencParam_;
            VencBuffer::VencCallback vencCallback_ = nullptr;
            cv::Size encSize = {0, 0};
            unsigned int minWidth_ = 128;
            unsigned int minHeight_ = 128;
            unsigned int maxWidth_ = 1280;
            unsigned int maxHeight_ = 720;
            unsigned int minAlign = 2;
            hi_payload_type encodeType_;
            aclrtContext context_ = NULL;
            FrameId frameId_ = 0;
            FrameId getFrame_ = 0;
            bool thread_destroy_ = false;
            std::mutex thread_destroy_mutex_;
            AclImageVpc vpc;
            std::mutex cb_frame_mutex_;
            std::map<FrameId, HI_FRAME_SP> cb_frame_;  // Pts_:HI_FRAME_SP

        };
    }
}

#endif

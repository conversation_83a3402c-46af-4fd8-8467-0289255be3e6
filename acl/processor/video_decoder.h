#ifndef ACL_VEGA_VIDEO_DECODER_H
#define ACL_VEGA_VIDEO_DECODER_H

#include "rpc/rpc_processor.h"
#include "vega_channel.h"
#include "sched.h"
#include "vega_sdk_config.h"
#include "common/Platform/processor/video_decoder.h"
#include "common/Platform/processor/video_decoder_buffer.h"
# include "srv/video_device_decoder.h"
#if CANN50 && ACL_MEDIA_V2
# include "v2/srv/video_device_decoder.h"
#endif

namespace vega {
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        class AclVdecBuffer : public VideoBuffer {
        public:
            AclVdecBuffer(uint32_t  channelId):VideoBuffer(channelId){
            //Ascend 710都是CANN50
#if CANN50 && ACL_MEDIA_V2
            forceV1_ =(bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false , sdk_config::acl);
            aclSocName_ = aclrtGetSocName();
            if ((aclSocName_.compare("Ascend710") == 0 || aclSocName_.compare("Ascend310P3") == 0
                 || aclSocName_.compare("Ascend310P1") == 0)
                && (!forceV1_)) {
                aclVDecoder_ = std::make_shared<AclVDecProcV2>();
            } else {
                aclVDecoder_ = std::make_shared<AclVDecProc>();
            }
#else
            aclVDecoder_ = std::make_shared<AclVDecProc>();
#endif

            unsigned int max_frames_store_total = 0;
            sdk_config::Platform platform = vegaGetPlatform();
            max_frames_store_total = sdk_config::get_cfg<int>("max_frames_store_total", 620, platform);
            if (max_frames_store_total_ != max_frames_store_total) {
                max_frames_store_total_ = max_frames_store_total;
                if (channelId == 0)
                    LOG(WARNING) << "acl max_frames_store_total_ change to " << max_frames_store_total_;
            }
            }

            virtual ~AclVdecBuffer() {
                DestroyVdec();
            }
            void DestroyVdec() override {
                if (aclStream_ != nullptr) {
                    if (aclVDecoder_)
                        aclVDecoder_.reset();
                    aclrtDestroyStream(aclStream_);
                    aclStream_ = nullptr;
                    bVdecInit_ = false;
                }
            }
            DgError InitVdec() override {
                if (!bVdecInit_) {
                    aclError ret = aclrtCreateStream(&aclStream_);
                    if (ret != ACL_SUCCESS) {
                        LOG(ERROR) << "Failed to create stream, ret = " << ret;
                        return DG_ERR_INIT_FAIL;
                    }
#if CANN50 && ACL_MEDIA_V2
                forceV1_ =(bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false , sdk_config::acl);
                    aclSocName_ = aclrtGetSocName();
                    if ((aclSocName_.compare("Ascend710") == 0 || aclSocName_.compare("Ascend310P3") == 0
                         || aclSocName_.compare("Ascend310P1") == 0)
                        && (!forceV1_)) {
                        if (!aclVDecoder_)
                            aclVDecoder_ = std::make_shared<AclVDecProcV2>();
                    } else {
                        if (!aclVDecoder_)
                            aclVDecoder_ = std::make_shared<AclVDecProc>();
                    }
#else
                    if (!aclVDecoder_)
                        aclVDecoder_ = std::make_shared<AclVDecProc>();
#endif
                    if (aclVDecoder_->Init(aclStream_, channelId_) != DG_OK) {
                        aclrtDestroyStream(aclStream_);
                        aclStream_ = nullptr;
                        return DG_ERR_INIT_FAIL;
                    }

                    bVdecInit_ = true;
                }
                return DG_OK;
            }

            DgError deleteStream(StreamId sid) override {
                return aclVDecoder_->DecodeVideo(nullptr, MatrixType::Undefined, nullptr, nullptr, true);
            }

            DgError decode(StreamId sid, MatrixSP input, MatrixType outputType, \
                            DecVideoCallback cb, const VideoDecParamSP &vdecParam) override {
                if (!bVdecInit_) {
                    LOG(ERROR) << "Must initialize before decoding ";
                    return DG_ERR_DECODE_FAIL;
                }
                return aclVDecoder_->DecodeVideo(input, outputType, cb, vdecParam, false);
            }
            std::string getDeviceDecstatus() override {
                if (!aclVDecoder_) {
                    return "acl video decoder is null!";
                }
                return aclVDecoder_->get_run_status();
            }

        private:
            std::string aclSocName_ = "Ascend310";
            bool forceV1_ = false;
            aclrtStream aclStream_ = nullptr;
            AclVDecProcBaseSP aclVDecoder_;
        };

#define ACL_MAX_VIDEO_DEC_WIDTH  4096
#define ACL_MAX_VIDEO_DEC_HEIGHT 4096
#define ACL_MAX_VIDEO_DEC_NUM    20
        class AclVdecManger : public VdecManger<AclVdecBuffer> {
        public:
            AclVdecManger(const AclVdecManger &) = delete;
            AclVdecManger & operator =(const AclVdecManger &) = delete;
            ~AclVdecManger() {}

            cv::Size GetMaxResolution(std::string param) override {
                cv::Size size(ACL_MAX_VIDEO_DEC_WIDTH,ACL_MAX_VIDEO_DEC_HEIGHT);
                return size;
            }
            unsigned int GetMaxDecoNum() override {
                unsigned int  max_vdc_num=ACL_MAX_VIDEO_DEC_NUM;
#if CANN50
                std::string acl_soc_name=aclrtGetSocName();
                if(acl_soc_name.compare("Ascend310")==0){
                    max_vdc_num=20;
                }else if(acl_soc_name.compare("Ascend710")==0 || acl_soc_name.compare("Ascend310P3")==0
                || acl_soc_name.compare("Ascend310P1")==0){
                    max_vdc_num=150;
                }
#endif
                LOG(WARNING)<<"max_vdc_num "<<max_vdc_num;
                return max_vdc_num;
            }
            AclVdecManger(){init();}
        };

        class VideoDecoder:public vega::VideoDecoder<AclVdecManger>{
        public:
            VideoDecoder() : vega::VideoDecoder<AclVdecManger>() {
            }

            ~VideoDecoder() = default;

            int getBatchSize() override {
                return std::numeric_limits<int>::max();
            }
        };
    }
}
#endif //VEGA_VIDEO_DECODER_H

#ifndef ACL_VEGA_VIDEO_ENCODER_H
#define ACL_VEGA_VIDEO_ENCODER_H

#include "rpc/rpc_processor.h"
#include "vega_channel.h"
#include "sched.h"
#include "vega_sdk_config.h"
#include "common/Platform/processor/video_encoder.h"
#include "common/Platform/processor/video_encoder_buffer.h"
# include "srv/video_device_encoder.h"
#if CANN50 && ACL_MEDIA_V2
# include "v2/srv/video_device_encoder.h"
#endif
namespace vega {
    namespace acl {

        #define ACL_MAX_VIDEO_ENC_NUM 1 //不支持单进程多线程场景

        class AclVencBuffer : public VencBuffer {
        public:
            AclVencBuffer() {
#if CANN50 && ACL_MEDIA_V2
            aclSocName_ = aclrtGetSocName();
            forceV1_ = (bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false, sdk_config::acl);
            if ((aclSocName_.compare("Ascend710") == 0 || aclSocName_.compare("Ascend310P3") == 0
                 || aclSocName_.compare("Ascend310P1") == 0)
                && (!forceV1_)) {
                aclVenc_ = std::make_shared<AclVencProcV2>();
            } else {
                aclVenc_ = std::make_shared<AclVencProc>();
            }
#else
            aclVenc_ = std::make_shared<AclVencProc>();
#endif
            }
            virtual ~AclVencBuffer() {
                if (aclStream_ != nullptr) {
                    aclrtDestroyStream(aclStream_);
                    aclStream_ = nullptr;
                }
            }
           DgError InitVenc() override {
                if (!bVencInit_) {
                    aclError ret = aclrtCreateStream(&aclStream_);
                    if (ret != ACL_SUCCESS) {
                        LOG(ERROR) << "Failed to create stream, ret = " << ret;
                        return DG_ERR_INIT_FAIL;
                    }
                    if (aclVenc_->Init(aclStream_) != DG_OK) {
                        aclrtDestroyStream(aclStream_);
                        aclStream_ = nullptr;
                        return DG_ERR_INIT_FAIL;
                    }
                    bVencInit_ = true;
                }
                return DG_OK;
            }

            DgError createVenc(MatrixType from, MatrixType to, const VencParam &param, VencCallback cb) override {
                return aclVenc_->CreateVenc(from, to, param, cb);
            }
    
            DgError encodeVideo(MatrixSP input, int kIntv, bool forceIFrame, float resizeRatio, bool eos) override {
                return aclVenc_->EncodeVideo(input, kIntv, forceIFrame, resizeRatio, eos);
            }

            DgError destroyVenc(void) override {
                return aclVenc_->DestroyVenc();
            }

        private:
            std::string aclSocName_ = "Ascend310";
            bool forceV1_ = false;
            aclrtStream aclStream_ = nullptr;
            AclVencProcBaseSP aclVenc_;
        };


    class AclVencManger: public vega::VencManger<AclVencBuffer>{
        public:
            AclVencManger(const AclVencManger &) = delete;
            AclVencManger & operator =(const AclVencManger &) = delete;
            ~AclVencManger() {}

            static AclVencManger& GetInstance() {
                static std::mutex lck;
                lck.lock();
                if (instance_ == nullptr) {
                    auto vencMgr = new AclVencManger;
                    instance_ = std::shared_ptr<AclVencManger>(vencMgr);
                }
                lck.unlock();
                return *instance_;
            }

            static void Destroy() {
                instance_.reset();
            }
        virtual int GetMaxVencNum(){
            return ACL_MAX_VIDEO_ENC_NUM;
        }
        private:
            AclVencManger() { init(); }

        private:
            static std::shared_ptr<AclVencManger> instance_;
        };
        std::shared_ptr<AclVencManger> AclVencManger::instance_ = nullptr;


    class VideoEncoder : public vega::VideoEncoder<AclVencManger> {
        public:
            VideoEncoder() : vega::VideoEncoder<AclVencManger>() {
            }
            ~VideoEncoder() {}

            int getBatchSize() override { return std::numeric_limits<int>::max(); }
        };

    }
}

#endif //VEGA_VIDEO_DECODER_H

//
// Created by ch<PERSON><PERSON><PERSON><PERSON> on 3/18/2021.
//

#ifndef VEGA_ACL_IMAGE_DECODER_H
#define VEGA_ACL_IMAGE_DECODER_H
#include "common/Platform/processor/image_decoder.h"
#include "utils/acl_image.h"
#include "srv/dvpp_common.h"
#if CANN50 && ACL_MEDIA_V2
#include "v2/srv/image_device_decoder.h"
#endif
#include "srv/image_device_decoder.h"


namespace vega
{
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl
    {

        class Decoder : public vega::ImageDecoder<acl::AclImage>
        {
        public:
            Decoder() : ImageDecoder(DVPP_IMAGE_ALIGN) {
// 1.CANN50下运行时自适应v1或v2; 
#if CANN50 && ACL_MEDIA_V2 
                forceV1_ =(bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false , sdk_config::acl);
                aclSocName_ = aclrtGetSocName();
                if ((aclSocName_.compare("Ascend710") == 0 || aclSocName_.compare("Ascend310P3") == 0
                     || aclSocName_.compare("Ascend310P1") == 0)
                    && (!forceV1_)) {
                    aclImageDec_ = std::make_shared<AclImageDecoderV2>();
                } else {
                    aclImageDec_ = std::make_shared<AclImageDecoder>();
                }
#else  // 2.CANN50下只编译v1
                aclImageDec_ = std::make_shared<AclImageDecoder>();
#endif
            }
            ~Decoder() {}

            int getBatchSize() override
            {
                return std::numeric_limits<int>::max();
            }

        protected:
            DgError decodeJpeg(std::shared_ptr<RpcContext> &ctx, int batchIdx) override
            {
                vegaSetCurrentContext();
                DgError err=DG_OK;
                auto input = creatInput(ctx, batchIdx);
                if (input == nullptr)
                {
                    LOG(ERROR) << "Fail to creatInput!";
                    return DG_ERR_DECODE_FAIL;
                }
                if (input->type() != MatrixType::JPEG)
                {
                    LOG(ERROR) << "input is'nt jpeg";
                    return DG_ERR_DECODE_FAIL;
                }

                MatrixSP output;
                if (aclImageDec_)
                    err = aclImageDec_->JpegDecode(input, output, MatrixType::NV12);
                if (err == DG_OK) {
                    frameDone(ctx, batchIdx, output);
                }
                return err;
            }
        private:
            std::string aclSocName_ = "Ascend310";
            bool forceV1_ = false;
            AclImageDecoderBaseSP aclImageDec_ = nullptr;
        };
    }
}

#endif // VEGA_ACL_IMAGE_DECODER_H

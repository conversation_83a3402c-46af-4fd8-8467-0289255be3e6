//
// Created by root on 10/9/21.
//

#ifndef ACL_OPTICAL_FLOW_PROCESSOR_
#define ACL_OPTICAL_FLOW_PROCESSOR_
#include "common/processor/optical_flow/vega_optical_flow_processor.h"
#include "srv/acl_op_engine.h"
#include "utils/FramebufferPool.h"
#include "srv/dvpp_common.h"

namespace vega {
namespace acl {
class AclMemPool {
public:
    static AclMemPool& getInstance() {
        static AclMemPool instance;
        return instance;
    }

    AclMemDvppSP get(const DG_U32 size) {
        std::lock_guard<std::mutex> lock(mutex_);
        for (auto it = pool_.begin(); it != pool_.end(); ++it) {
            if ((*it)->size() == size) {
                auto mem = *it;
                pool_.erase(it);
                return mem;
            }
        }

        auto mem = std::make_shared<AclMemDvpp>();
        if (mem->create(size) != DG_OK) {
            LOG(ERROR) << "Failed to create AclMemDvpp";
            return nullptr;
        }
        return mem;
    }

    void put(const AclMemDvppSP& mem) {
        if(!mem) return;
        std::lock_guard<std::mutex> lock(mutex_);
        pool_.push_back(mem);
    }

    static void destroyInstance() {
        AclMemPool& instance = getInstance();
        instance.pool_.clear();
    }

    AclMemPool() {}
    ~AclMemPool() {}

private:
    std::mutex mutex_;
    std::vector<AclMemDvppSP> pool_;

    AclMemPool(const AclMemPool&) = delete;
    AclMemPool& operator=(const AclMemPool&) = delete;
};


class AclOpticalFlowProcessor : public OpticalFlowProcessor {
public:
    AclOpticalFlowProcessor() {
        aclOpProcess_ = std::make_shared<AclOpProcess>();
        aclOpProcess_->aclopCompile();
        resizeSize_ = 0;
    }

    ~AclOpticalFlowProcessor() {
        AclMemPool::destroyInstance();
    }

public:
    int getBatchSize() override {
        return std::numeric_limits<int>::max();
    }

    DgError init() override {
        stream_ = requestStream();
        if (stream_ != VEGA_INVALID_STREAM) {
            return DG_OK;
        } else {
            return DG_ERR_STREAM_FAILURE;
        }
    }

protected:
    DgError optFlow(std::shared_ptr<RpcContext> &ctx, int batchIdx) {
        DgError error = DG_OK;
        auto &task       = ctx->tasks[batchIdx];
        auto opticalFlow = task->getString(Option::optical_flow_, "unsupported");
        auto dstSize     = task->size_;
        if (opticalFlow == "unsupported") {
            LOG(ERROR) << "option optical_flow_ not set,unsuppoted.";
            return DG_ERR_INVALID_PARAM;
        } else {
            OpticalFlowInput input;
            error = input.fromJson(opticalFlow);
            if (error == DG_OK) {
                auto prevFrame = VegaMatrixPool::getInstance().getMatrix(input.prev_streamId, input.prev_frameId);
                auto nextFrame = VegaMatrixPool::getInstance().getMatrix(input.next_streamId, input.next_frameId);
                if (!prevFrame || !nextFrame) {
                    LOG(ERROR) << "Frame may not exist, (sid,fid) of prev frame (" << input.prev_streamId << "," << input.prev_frameId
                               << "), next frame (" << input.next_streamId << "," << input.next_frameId << ")";
                    return DG_ERR_INVALID_PARAM;
                }
                if (prevFrame->type() != MatrixType::NV12 || nextFrame->type() != MatrixType::NV12) {
                    LOG(ERROR) << "optical flow only support NV12 type, prev frame type " << prevFrame->typestr() << " next frame type "
                               << nextFrame->typestr();
                    return DG_ERR_INVALID_PARAM;
                }
                // resize
                streamId_                   = prevFrame->streamId();
                AclMemDvppSP prevMem = nullptr,nextMem = nullptr;
                if (prevFrame->size() != dstSize) {
                    AclImageSP prevFrameResized = std::make_shared<AclImage>();
                    AclImageSP nextFrameResized = std::make_shared<AclImage>();
                    resizeSize_ = dstSize.width * dstSize.height * YUV_BGR_SIZE_CONVERT_3 / YUV_BGR_SIZE_CONVERT_2;
                    // for resize
                    MatrixSPV srcMatrixs, dstMatrixs;
                    ProcessMatrixParam param;
                    srcMatrixs.push_back(prevFrame);
                    srcMatrixs.push_back(nextFrame);
                    auto &memPool_ = AclMemPool::getInstance();
                    prevMem = memPool_.get(resizeSize_);
                    if (!prevMem) {
                        LOG(ERROR) << "Failed to get prev mem from image pool, required memory size " << resizeSize_;
                        return DG_ERR_HW_FAILURE;
                    }
                    nextMem = memPool_.get(resizeSize_);
                    if (!nextMem) {
                        LOG(ERROR) << "Failed to get prev mem from image pool, required memory size " << resizeSize_;
                        return DG_ERR_HW_FAILURE;
                    }
                    if (prevFrameResized->create(MatrixType::NV12, dstSize, prevMem) != DG_OK
                        || nextFrameResized->create(MatrixType::NV12, dstSize, nextMem) != DG_OK) {
                        LOG(ERROR) << "Create aclImage from aclMemDvpp failed. aclMemDvpp's size " << prevFrame->size()
                                   << " required aclImage's size" << dstSize;
                        return DG_ERR_HW_FAILURE;
                    }
                    dstMatrixs.push_back(prevFrameResized);
                    dstMatrixs.push_back(nextFrameResized);
                    auto erro = vegaProcessMatrix(srcMatrixs, dstMatrixs, VegaMatrixCmd::CROP_RESIZE, nullptr, param);
                    if (erro != DG_OK) {
                        LOG(ERROR) << "vegaProcessMatrix failed!";
                        return DG_ERR_HW_FAILURE;
                    }
                    prevFrame = prevFrameResized;
                    nextFrame = nextFrameResized;
                }
                // create output memory for flow,use mem pool
                dataSize_ = prevFrame->stride().area() * sizeof(float) * 2;  // flow has two channels
                auto &memPool_ = AclMemPool::getInstance();
                std::shared_ptr<AclMemDvpp> dev = nullptr;
                dev = memPool_.get(dataSize_);
                if (!dev) {
                    LOG(ERROR) << "Failed to get output mem for optFlow output, required memory size " << dataSize_;
                    return DG_ERR_HW_FAILURE;
                }

                // execute
                auto dgErr = aclOpProcess_->opticalFlow(prevFrame, nextFrame, dev, input, stream_);
                if (dgErr != DG_OK) {
                    LOG(ERROR) << "execute optical flow op failed";
                    return dgErr;
                }
                // copy the flow from device to host
                cv::Mat flow_host = cv::Mat(prevFrame->stride().height, prevFrame->stride().width, CV_32FC2);
                dgErr             = AclMemDvpp::AclMemcpy(flow_host.data, dev->size(), dev->blob(), dev->size(), ACL_MEMCPY_DEVICE_TO_HOST);
                if (dgErr != DG_OK) {
                    LOG(ERROR) << "mem copy from device to host failed";
                    return dgErr;
                }
                // free mem to pool
                memPool_.put(dev);
                if (resizeSize_) {
                    memPool_.put(prevMem);
                    memPool_.put(nextMem);
                }
                OpticalFlowData data;
                cv::Mat flow = flow_host(cv::Rect(0, 0, prevFrame->size().width, prevFrame->size().height));
                data.fromCvMat(flow);
                opticalFlowDone(ctx, batchIdx, data);
            } else {
                LOG(ERROR) << "Invalid optivcal flow json string" << opticalFlow;
                return DG_ERR_INVALID_PARAM;
            }
        }
        return DG_OK;
    }

private:
    std::shared_ptr<AclOpProcess> aclOpProcess_;
    AclMemPool memPool_;
    int dataSize_;
    int resizeSize_ ;
    StreamId streamId_;
    aclrtStream stream_;
};
}  // namespace acl

}  // namespace vega
#endif  // ACL_OPTICAL_FLOW_PROCESSOR_

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/13/23.
//

#ifndef ACL_ABS_DIFF_PROCESSOR_
#define ACL_ABS_DIFF_PROCESSOR_
#include "common/processor/abs_diff/vega_abs_diff_processor.h"
#include "srv/acl_op_engine.h"
#include "srv/dvpp_common.h"
#include "utils/FramebufferPool.h"

namespace vega {
namespace acl {
class AclAbsDiffProcessor : public AbsDiffProcessor {
public:
    AclAbsDiffProcessor() {
        aclOpProcess_ = std::make_shared<AclOpProcess>();
        aclOpProcess_->aclopCompile();
        resizeSize_ = 0;
    }

    ~AclAbsDiffProcessor() {
        AclMemPool::destroyInstance();
    }

public:
    int getBatchSize() override {
        return std::numeric_limits<int>::max();
    }

    DgError init() override {
        stream_ = requestStream();
        if (stream_ != VEGA_INVALID_STREAM) {
            return DG_OK;
        } else {
            return DG_ERR_STREAM_FAILURE;
        }
    }

protected:
    DgError absDiff(std::shared_ptr<RpcContext> &ctx, int batchIdx) {
        DgError error    = DG_OK;
        auto &task       = ctx->tasks[batchIdx];
        auto absDiff = task->getString(Option::abs_diff_, "unsupported");
        if (absDiff == "unsupported") {
            LOG(ERROR) << "option abs_diff_ not set,unsuppoted.";
            return DG_ERR_INVALID_PARAM;
        } else {
            AbsDiffInput input;
            error = input.fromJson(absDiff);
            if (error == DG_OK) {
                auto prevFrame = VegaMatrixPool::getInstance().getMatrix(input.prev_streamId, input.prev_frameId);
                auto nextFrame = VegaMatrixPool::getInstance().getMatrix(input.next_streamId, input.next_frameId);
                if (!prevFrame || !nextFrame) {
                    LOG(ERROR) << "Frame may not exist, (sid,fid) of prev frame (" << input.prev_streamId << "," << input.prev_frameId
                               << "), next frame (" << input.next_streamId << "," << input.next_frameId << ")";
                    return DG_ERR_INVALID_PARAM;
                }
                if (prevFrame->type() != MatrixType::NV12 || nextFrame->type() != MatrixType::NV12) {
                    LOG(ERROR) << "abs diff only support NV12 type, prev frame type " << prevFrame->typestr() << " next frame type "
                               << nextFrame->typestr();
                    return DG_ERR_INVALID_PARAM;
                }
                // resize
                streamId_            = prevFrame->streamId();
                // create output memory for flow,use mem pool
                dataSize_                       = prevFrame->stride().area() * sizeof(unsigned char) * 1;  // CV_8UC1
                auto &memPool_                  = AclMemPool::getInstance();
                std::shared_ptr<AclMemDvpp> dev = nullptr;
                dev                             = memPool_.get(dataSize_);
                if (!dev) {
                    LOG(ERROR) << "Failed to get output mem for absdiff output, required memory size " << dataSize_;
                    return DG_ERR_HW_FAILURE;
                }

                // execute
                auto dgErr = aclOpProcess_->absDiff(prevFrame, nextFrame, dev, stream_);
                if (dgErr != DG_OK) {
                    LOG(ERROR) << "execute abs diff op failed";
                    return dgErr;
                }
                // copy the flow from device to host
                cv::Mat flow_host = cv::Mat(prevFrame->stride().height, prevFrame->stride().width, CV_8UC1);
                dgErr             = AclMemDvpp::AclMemcpy(flow_host.data, dev->size(), dev->blob(), dev->size(), ACL_MEMCPY_DEVICE_TO_HOST);
                if (dgErr != DG_OK) {
                    LOG(ERROR) << "mem copy from device to host failed";
                    return dgErr;
                }
                // free mem to pool
                memPool_.put(dev);
                AbsDiffData<unsigned char> data;
                // to handle stride problem
                cv::Mat flow = flow_host(cv::Rect(0, 0, prevFrame->size().width, prevFrame->size().height));
                data.fromCvMat(flow);
                absDiffDone(ctx, batchIdx, data);
            } else {
                LOG(ERROR) << "Invalid abs diff json string" << absDiff;
                return DG_ERR_INVALID_PARAM;
            }
        }
        return DG_OK;
    }

private:
    std::shared_ptr<AclOpProcess> aclOpProcess_;
    AclMemPool memPool_;
    int dataSize_;
    int resizeSize_;
    StreamId streamId_;
    aclrtStream stream_;
};
}  // namespace acl
}  // namespace vega

#endif  // ACL_ABS_DIFF_PROCESSOR_

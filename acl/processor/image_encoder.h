//
// Created by ch<PERSON><PERSON><PERSON><PERSON> on 3/18/2021.
//

#ifndef VEGA_ACL_IMAGE_ENCODER_H
#define VEGA_ACL_IMAGE_ENCODER_H

#include "common/Platform/processor/image_encoder.h"
#include "utils/acl_image.h"
#include "srv/dvpp_common.h"
#if CANN50 && ACL_MEDIA_V2
#include "acl/v2/srv/image_device_encoder.h"
#endif
#include "srv/image_device_encoder.h"
namespace vega
{
    namespace acl
    {
        class FetchFrame : public vega::FetchFrame
        {
        public:
            FetchFrame() : vega::FetchFrame() {
#if CANN50 && ACL_MEDIA_V2
                forceV1_ =(bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false , sdk_config::acl);
                aclSocName_ = aclrtGetSocName();
                if ((aclSocName_.compare("Ascend710") == 0 || aclSocName_.compare("Ascend310P3") == 0
                     || aclSocName_.compare("Ascend310P1") == 0)
                    && (!forceV1_)) {
                    if (aclSocName_.compare("Ascend310P1") == 0) {
                        isAclSoc_ = true;
                    }
                    aclImageEnc_ = std::make_shared<AclImageEncoderV2>();
                } else {
                    aclImageEnc_ = std::make_shared<AclImageEncoder>();
                }
#else  
                aclImageEnc_ = std::make_shared<AclImageEncoder>();
#endif
            }
            ~FetchFrame() {}

        protected:
            // encode image to jpeg
            DgError encodeJpg(MatrixSP &in, MatrixSP &out, int quality, cv::Size dstSize={0,0}) override
            {
                vegaSetCurrentContext();
                if(dstSize.area()!=0)
                {
                    LOG(ERROR) << "unsupport encode resize JPG !";
                    return DG_ERR_NOT_SUPPORTED;
                }
                return aclImageEnc_->JpegEncode(in, out, quality);
            }

            // encode image to gray
            DgError encodeGray(MatrixSP &in, MatrixSP &out, cv::Size dstSize={0,0}) override
            {
                AclImageSP aclImageIn = std::dynamic_pointer_cast<AclImage>(in);
                if (!aclImageIn || !aclImageIn->isYuv())
                {
                    LOG(ERROR) << "Not supported!";
                    return DG_ERR_INVALID_IMAGE;
                }
                if(dstSize.area()==0)
                {
                    dstSize.width = in->roi().width;
                    dstSize.height = in->roi().height;
                }
                // 1.crop & resize
                MatrixSPV srcMatrixs, dstMatrixs;
                ProcessMatrixParam param;
#if CANN50
                auto imageOut = std::make_shared<AclImage>();
                auto erro = imageOut->create(aclImageIn->type(), dstSize); // create a matrix(nv12) on device memory
                if (erro != DG_OK)
                {
                    LOG(ERROR) << "Create imageOut on device failed!";
                    imageOut = nullptr;
                    return erro;
                }
#else
                MatrixSP imageOut = std::make_shared<Matrix>();
                auto erro = imageOut->create(aclImageIn->type(), dstSize); // create a matrix(nv12) on host memory
                if (erro != DG_OK)
                {
                    LOG(ERROR) << "Create imageOut on device failed!";
                    imageOut = nullptr;
                    return erro;
                }
                param.addOption(ProcessMatrixOption::force_opencv);

#endif

                srcMatrixs.push_back(aclImageIn);
                dstMatrixs.push_back(imageOut);
                erro = vegaProcessMatrix(srcMatrixs, dstMatrixs, VegaMatrixCmd::CROP_RESIZE, nullptr, param);
                if (erro != DG_OK)
                {
                    LOG(ERROR) << "vegaProcessMatrix failed!";
                    return erro;
                }

                // 2.copy the Y plane,from device to host
                if (isAclSoc_) {
                    out=std::dynamic_pointer_cast<Matrix>(imageOut);
                    return DG_OK;
                }
                out = std::make_shared<Matrix>();
                erro = out->create(MatrixType::Gray, imageOut->size()); // create a matrix(gray) on host memory
                if (erro != DG_OK)
                {
                    LOG(ERROR) << "Create Gray image  failed!";
                    out = nullptr;
                    return erro;
                }
#if CANN50
                auto ret=ACL_SUCCESS;
                auto area=dstSize.area();
                if(imageOut->stride().area()!=area){
                    ret = aclrtMemcpy2d(out->data(), imageOut->size().width, imageOut->data(),
                                         DVPP_ALIGN_UP(dstSize.width, VPC_STRIDE_WIDTH),
                                         dstSize.width, dstSize.height, ACL_MEMCPY_DEVICE_TO_HOST); // be careful of memory alignment
                }else{
                    ret = aclrtMemcpy(out->data(),area, imageOut->data(),area, ACL_MEMCPY_DEVICE_TO_HOST);
                }
                if (ret != ACL_SUCCESS)
                {
                    LOG(ERROR) << "copy gray from device to host failed! ret: " << ret;
                    return DG_ERR_INVALID_ADDRESS;
                }
#else
                // Acl doesn't has 2d function,so use opencv and copy memory from host to host.
                auto dataLen = imageOut->size().width * imageOut->size().height;
                memcpy(out->data(), imageOut->data(), dataLen);
#endif
                return DG_OK;
            }

        private:
            std::string aclSocName_ = "Ascend310";
            bool isAclSoc_ = false;
            bool forceV1_ = false;
            AclImageEncoderBaseSP aclImageEnc_;
        };
    }
}

#endif // VEGA_ACL_IMAGE_ENCODER_H

//
// Created by ch<PERSON><PERSON><PERSON><PERSON> on 6/29/22.
//
#if ACL
#ifndef VEGA_ACL_KEY_POINT_INFER_PLUGIN_H
#define VEGA_ACL_KEY_POINT_INFER_PLUGIN_H
#include <vector>
#include "srv/acl_op_execute.h"
#include "utils/acl_memory.h"
#include "vega_sdk_config.h"
#include "common/processor/infer_plugin/keypoint_plugin.h"
#include "utils/mem_host_device_pair.h"
namespace vega
{
    namespace acl
    {
        class KeyPointPlugin:public vega::KeyPointPlugin
        {
        public:
            typedef struct
            {
                unsigned long long in_addr;
                unsigned long long out_addr;
                unsigned int w;
                unsigned int h;
                unsigned int channel_slice;
                unsigned int batch_size;
                unsigned int proc_type;
            }AiCpuKeyPointParam;
            KeyPointPlugin(){};
            ~KeyPointPlugin() = default;
            DgError Init(std::shared_ptr<ModelConfig> config) override{
                DgError ret=DG_OK;
                int use_aicpu_op_engine=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
                if(!use_aicpu_op_engine){
                    return DG_ERR_INVALID_PARAM;
                }
                if(DG_OK != InitParam(config)){
                    return DG_ERR_INVALID_PARAM;
                }
                if (procType_ != NotGaussianBlur){
                    return DG_ERR_INVALID_PARAM;
                }
                config->is_copy_to_host_ = false;
                Output_= std::make_shared<MemHostDevicePool<float>>(buffer_pool_size_, channel_slice_size_* batchSize_*3,true,true);
                aicpu_param_= std::make_shared<MemHostDevicePool<unsigned char>>(buffer_pool_size_, sizeof(AiCpuKeyPointParam)
                +sizeof(unsigned long long),true,true);

                MemHostDevicePool<unsigned char>::MemPairSP inParam=aicpu_param_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                MemHostDevicePool<float>::MemPairSP output=Output_->pop();
                if(!output){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    aicpu_param_->push(inParam);
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(inParam->host,[&](void *data){
                    aicpu_param_->push(inParam);
                    Output_->push(output);
                });
                memset(inParam->host,0,inParam->size);
                AiCpuKeyPointParam *pInParam=(AiCpuKeyPointParam *)inParam->host;
                pInParam->proc_type=procType_;
                if(DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};
                auto aclOpExecute=AclOpExecute::pop();
               
                ret=aclOpExecute->executeOp(param_shape, inParam->device->blob(),op_type_,true);//first compile the aclop
                AclOpExecute::push(aclOpExecute);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to executeOp "<<op_type_;
                    return DG_ERR_HW_FAILURE;
                }
                return DG_OK;
            }
            void afInferProcHook(DataBlob<float> infer_output, std::vector<std::shared_ptr<float>> & innerDataV,
             int batchSize, VegaStream stream) override{
                DgError ret=DG_OK;
                MemHostDevicePool<unsigned char>::MemPairSP inParam=aicpu_param_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return ;
                }
                MemHostDevicePool<float>::MemPairSP output=Output_->pop();
                if(!output){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    aicpu_param_->push(inParam);
                    return ;
                }
                std::shared_ptr<float> keyPointHost=std::shared_ptr<float>(output->host,[=](void *prt){
                    aicpu_param_->push(inParam);
                    Output_->push(output);
                });
                innerDataV.resize(1);
                innerDataV[0]=keyPointHost;
                DG_U8 *ptr=inParam->host;
                unsigned long long *param_addr=(unsigned long long *)ptr;
                *param_addr=(unsigned long long)inParam->device->blob()+sizeof(unsigned long long);
                ptr+=sizeof(unsigned long long);
                AiCpuKeyPointParam *pInParam=(AiCpuKeyPointParam *)ptr;
                float *layerPtr = infer_output.begin();
                pInParam->in_addr=(unsigned long long)layerPtr;
                pInParam->out_addr=(unsigned long long)output->device->blob();
                pInParam->w=width_;
                pInParam->h=height_;
                pInParam->channel_slice=channel_slice_size_;
                pInParam->batch_size=batchSize;
                pInParam->proc_type=procType_;
                if(DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return;
                }
                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};
                auto aclOpExecute=AclOpExecute::pop();
                ret=aclOpExecute->executeOp(param_shape, inParam->device->blob(),op_type_,false);
                AclOpExecute::push(aclOpExecute);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to executeOp "<<op_type_;
                    return ;
                }
                if(DG_OK != MemHostDevicePool<float>::SynDeviceToHost(output)){
                    LOG(ERROR)<<"Fail to SynDeviceToHost!";
                    return ;
                }
            }
        public:
            std::shared_ptr<MemHostDevicePool<float>> Output_;
            std::shared_ptr<MemHostDevicePool<unsigned char>> aicpu_param_;
            int buffer_pool_size_=12;
             std::string op_type_ = "VegaKeyPoint";
        };
    }
}
#endif
#endif

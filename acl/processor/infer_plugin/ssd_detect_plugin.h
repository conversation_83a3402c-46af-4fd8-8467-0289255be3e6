//
// Created by ch<PERSON><PERSON><PERSON><PERSON> on 4/29/22.
//
#if ACL
#ifndef VEGA_ACL_SSD_INFER_PLIN_H
#define VEGA_ACL_SSD_INFER_PLIN_H
#include <vector>
#include "srv/acl_op_execute.h"
#include "utils/acl_memory.h"
#include "vega_sdk_config.h"
#include "common/processor/infer_plugin/ssd_detect_plugin.hpp"
#include "utils/mem_host_device_pair.h"
namespace vega{
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        class SSDDetectPlugin :public vega::SSDDetectPlugin<float>{
        public:
            typedef struct {
                unsigned int width;
                unsigned int height;
                unsigned int stride;
                unsigned int layer_steps;
                unsigned int anchor_steps;
                unsigned int anchor_number;
                unsigned int conf_size;
                unsigned int local_size;
                float step_w;
                float step_h;
            }Layer;
            typedef struct
            {
                unsigned int layer_num;
                unsigned int class_num;
                unsigned int actual_class_num;
                unsigned int batch_size;
                unsigned int img_width;
                unsigned int img_height;
                unsigned int is_sigmoid_type;
                unsigned int clip;
                float offset;
                unsigned int top_k;
                unsigned int keep_topk;
                float conf0_minus_max;
                float nms_thres;
                unsigned int share_location;
            } PostHead;

            explicit SSDDetectPlugin(const std::string &json_file, GetLayerDim F1, GetLayerData F2,int batchSize)
            :vega::SSDDetectPlugin<float>(json_file, F1, F2)  {
                batch_size_=batchSize;
                top_k_=100; 
                keep_topk_=100;
                sdk_config::Platform platform=vegaGetPlatform();
                int engine_num = sdk_config::get_cfg<int>("aicpu_engine_num",3,platform);
                ssdPostOut_= std::make_shared<MemHostDevicePool<float>>(engine_num, 7*keep_topk_*batch_size_+1,true,true);
                //addr of param + device_SSDPostParam_addr+SSDpostOutput_addr+batchSize+infer_ouput_adress
                dynamic_param_= std::make_shared<MemHostDevicePool<unsigned char >>(engine_num, sizeof(unsigned long long)+
                sizeof(unsigned long long)+sizeof(unsigned long long)+sizeof(unsigned long long)+
                sizeof(unsigned long long)*2*prior_layer_num_,true,true);
                
            };

            ~SSDDetectPlugin() {
            };
            DgError init(void *opGetDim, int align, int imageW, int imageH, std::vector<float> thres) override {
                init_param(opGetDim, align, imageW, imageH, thres);
                if(DG_OK!=acl_init_post_param()){
                    LOG(ERROR)<<"fail to acl_init_post_param";
                    return DG_ERR_HW_FAILURE;
                }
                ///compile the aicpu op
                std::shared_ptr<MemBase> paramHost=std::make_shared<MemBase>();
                //addr of param + device_SSDPostParam_addr+SSDpostOutput_addr+batchSize+infer_ouput_adress
                auto ret=paramHost->create(sizeof(unsigned long long)+sizeof(unsigned long long)+sizeof(unsigned long long)
                +sizeof(unsigned long long)+sizeof(unsigned long long)*2*prior_layer_num_);
                if(ret!=DG_OK){
                    LOG(ERROR)<<"Fail to creat MemBase!";
                    return DG_ERR_HW_FAILURE;
                }
                memset(paramHost->blob(),0,paramHost->size());
                AclMemDvppSP acl_dynamic_param=std::make_shared<AclMemDvpp>();
                ret=acl_dynamic_param->fromHost(paramHost);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to create AclMemDvpp!";
                    return DG_ERR_HW_FAILURE;
                }
                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};
                auto aclOpExecute=AclOpExecute::pop();

                ret=aclOpExecute->executeOp(param_shape, acl_dynamic_param->blob(),op_type_,true);  //first compile the aclop
                if(DG_OK != ret){
                        LOG(ERROR)<<"Fail to executeOp "<<op_type_;
                }
                AclOpExecute::push(aclOpExecute);
                return ret;
            }
            DgError GetDetectionOut(void *opGetData, int batchSize, std::vector<std::vector<float>> &output,vega::VegaStream stream) override {
                output.resize(batchSize);

                MemHostDevicePool<unsigned char>::MemPairSP param=dynamic_param_->pop();
                if(!param){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                MemHostDevicePool<float>::MemPairSP ssdPostOut=ssdPostOut_->pop();
                if(!ssdPostOut){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    dynamic_param_->push(param);
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(param->host,[&](void *data){
                    dynamic_param_->push(param);
                    ssdPostOut_->push(ssdPostOut);
                });
                //device_SSDPostParam_addr+SSDpostOutput_addr+batchSize+infer_ouput_adress
                DG_U8 *ptr=param->host;
                unsigned long long *param_addr=(unsigned long long *)ptr;
                *param_addr=(unsigned long long)param->device->blob()+sizeof(unsigned long long);
                ptr+=sizeof(unsigned long long);
                unsigned long long *device_SSDPostParam_addr=(unsigned long long *)ptr;
                *device_SSDPostParam_addr=(unsigned long long)(device_SSDPostParam_->blob());
                ptr+=sizeof(unsigned long long);
                unsigned long long *ssd_post_out_addr=(unsigned long long *)ptr;
                *ssd_post_out_addr=(unsigned long long)(ssdPostOut->device->blob());
                ptr+=sizeof(unsigned long long);
                unsigned long long *batch=(unsigned long long *)ptr;
                *batch=batchSize;
                ptr+=sizeof(unsigned long long);
                for(int i=0;i<prior_layer_num_;i++){
                    int local_size=0, conf_size=0;
                    unsigned long long *local=(unsigned long long *)ptr;
                    *local=(unsigned long long)getOutputLayerData(convName_[2 * i + 1], 0, local_size, opGetData);
                    ptr+=sizeof(unsigned long long);
                    unsigned long long *conf=(unsigned long long *)ptr;
                    *conf=(unsigned long long)getOutputLayerData(convName_[2 * i], 0, conf_size, opGetData);
                    ptr+=sizeof(unsigned long long);
                }
                auto ret = MemHostDevicePool<unsigned char>::SynHostToDevice(param);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                auto aclOpExecute=AclOpExecute::pop();
               

                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};
                ret=aclOpExecute->executeOp(param_shape, param->device->blob(),op_type_,false);
                AclOpExecute::push(aclOpExecute);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to executeOp "<<op_type_;
                    return DG_ERR_HW_FAILURE;
                }
                unsigned int base_num=batchSize*2;
                unsigned char *host=(unsigned char *)ssdPostOut->host;
                unsigned char *device=(unsigned char *) ssdPostOut->device->blob();
                int element_size=7*sizeof(float);
                ret=AclMemDvpp::AclMemcpy(host, sizeof(unsigned int)+base_num*element_size, device,
                     sizeof(unsigned int)+base_num*element_size, ACL_MEMCPY_DEVICE_TO_HOST);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to AclMemcpy!";
                    return DG_ERR_HW_FAILURE;
                }
                unsigned int num=*(unsigned int *)ssdPostOut->host;
                if(num==0)
                    return DG_OK;
                if(num>(unsigned int)batchSize*keep_topk_) {
                    for(unsigned int i=0;i<conf_thres_.size();i++){
                        LOG(ERROR)<<" i "<<i<<",conf_thres "<<conf_thres_[i];
                    }
                    LOG(ERROR)<<" num "<<num<<",top_k_ "<<top_k_<<",keep_topk_ "<<keep_topk_<<",batch size "<<batchSize;
                    LOG(ERROR)<<"Unexpect ssd_output_data happen!";
                    return DG_OK;
                }
                if (num > base_num){
                    unsigned int size=(num-base_num)*element_size;
                    ret = AclMemDvpp::AclMemcpy(host+ sizeof(unsigned int)+base_num*element_size, size,
                            device + sizeof(unsigned int)+base_num*element_size, size, ACL_MEMCPY_DEVICE_TO_HOST);
                    if (DG_OK != ret){
                        LOG(ERROR) << "Fail to AclMemcpy!";
                        return DG_ERR_HW_FAILURE;
                    }
                }
                float *data=ssdPostOut->host+1;
                for(unsigned int i=0;i<num;i++){
                   int batchIdx=*data;
                   for(int j=0;j<7;j++){
                       output[batchIdx].push_back(*data);
                       data++;
                   }
                }
                return DG_OK;
            }

            DgError acl_init_post_param(){
                std::shared_ptr<MemBase> paramHost=std::make_shared<MemBase>();
                int variance_list_size=0;
                for(unsigned int i=0;i<variance_list_.size();i++) {
                    variance_list_size+=variance_list_[i].size();
                }
                variance_list_size*=sizeof(float);
                variance_list_size+=sizeof(unsigned int)*(1+variance_list_.size());

                int anchor_vec_size=0;
                for(unsigned int i=0;i<anchor_vec_.size();i++){
                    anchor_vec_size+=anchor_vec_[i].size();
                }
                anchor_vec_size*=sizeof(float);
                anchor_vec_size+=sizeof(unsigned int)*(1+anchor_vec_.size());

                //PostHead+layer+conf_thres_+ log_thres+variance_list_+anchor_vec;
                auto ret=paramHost->create(sizeof(PostHead)+prior_layer_num_*sizeof(Layer)+sizeof(float)*class_num_*2
                +variance_list_size+anchor_vec_size);
                if(ret!=DG_OK){
                    LOG(ERROR)<<"Fail to creat MemBase!";
                    return DG_ERR_HW_FAILURE;
                }
                DG_U8 *ptr=paramHost->blob();
                PostHead *postHead=(PostHead *)ptr;
                postHead->layer_num=prior_layer_num_;
                postHead->class_num=class_num_;
                postHead->actual_class_num=actual_class_num_;
                postHead->batch_size=batch_size_;
                postHead->is_sigmoid_type=is_sigmoid_type_;
                postHead->top_k=top_k_; 
                postHead->clip=clip_;
                postHead->keep_topk=keep_topk_;
                postHead->conf0_minus_max=conf0_minus_max_;
                postHead->img_width=img_width_;
                postHead->img_height=img_height_;
                postHead->offset=offset_;
                postHead->nms_thres=nms_thres_;
                postHead->share_location=share_location_;
                ptr+=sizeof(PostHead);
                for(int i=0;i<prior_layer_num_;i++){
                    Layer  *layer =(Layer *)(ptr);
                    layer->width =  layer_width_[i];            
                    layer->height = layer_height_[i];
                    layer->stride = layer_stride_[i];
                    layer->layer_steps = layer_steps_[i];
                    layer->anchor_steps = layer_steps_[i]*class_num_;
                    layer->anchor_number=layer_anchor_num_[i];
                    layer->conf_size=layer_steps_[i]*layer_anchor_num_[i]*class_num_;
                    if(share_location_)
                        layer->local_size=layer_steps_[i]*layer_anchor_num_[i]*4;
                    else
                        layer->local_size=layer_steps_[i]*layer_anchor_num_[i]*4*class_num_;
                    layer->step_w= step_wh_[i].first;
                    layer->step_h= step_wh_[i].second;
                    ptr+=sizeof(Layer);
                }
                for(int i=0;i<class_num_;i++){
                    float *conf_thres=(float *)ptr;
                    *conf_thres=conf_thres_[i];
                    ptr+=sizeof(float);
                }
                for(int i=0;i<class_num_;i++){
                    float *log_thres=(float *)ptr;
                    *log_thres=log_thres_[i];
                    ptr+=sizeof(float);
                }
                unsigned int *variance_list_size_0=(unsigned int *)ptr;
                *variance_list_size_0=variance_list_.size();
                ptr+=sizeof(unsigned int);
                for(unsigned int i=0;i<variance_list_.size();i++){
                    unsigned int *variance_list_size_1=(unsigned int *)ptr;
                    *variance_list_size_1=variance_list_[i].size();
                    ptr+=sizeof(unsigned int);
                    for(unsigned int j=0;j<variance_list_[i].size();j++){
                        float *v=(float *)ptr;
                        ptr+=sizeof(float);
                        *v=variance_list_[i][j];
                    }
                }
                unsigned int *anchor_vec_size_0=(unsigned int *)ptr;
                *anchor_vec_size_0=anchor_vec_.size();
                ptr+=sizeof(unsigned int);
                for(unsigned int i=0;i<anchor_vec_.size();i++){
                    unsigned int *anchor_vec_size_1=(unsigned int *)ptr;
                    *anchor_vec_size_1=anchor_vec_[i].size();
                    ptr+=sizeof(unsigned int);
                    for(unsigned int j=0;j<anchor_vec_[i].size();j++){
                        float *v=(float *)ptr;
                        ptr+=sizeof(float);
                        *v=anchor_vec_[i][j];
                    }
                }
                device_SSDPostParam_=std::make_shared<AclMemDvpp>();
                ret=device_SSDPostParam_->fromHost(paramHost);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to create AclMemDvpp!";
                    return DG_ERR_HW_FAILURE;
                }
                return DG_OK;

            }

         virtual void afInferProcHook(DataBlob<float> infer_output, std::vector<std::shared_ptr<float>> &innerDataV,int batchSize, VegaStream stream){
                
            }
        private:
            AclMemDvppSP device_SSDPostParam_;
            std::shared_ptr<MemHostDevicePool<float>> ssdPostOut_;
            std::shared_ptr<MemHostDevicePool<unsigned char>> dynamic_param_;
            unsigned int batch_size_;
            std::string op_type_ = "VegaSSDPost";
        };
    }
}
#endif //VEGA_ACL_SSD_INFER_PLIN_H
#endif //if ACL

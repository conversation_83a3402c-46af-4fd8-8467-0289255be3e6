//
// Created by ch<PERSON><PERSON><PERSON><PERSON> on  03/12/2021.
//

#ifndef VEGA_ACL_MEMORY_H
#define VEGA_ACL_MEMORY_H
#define ENABLE_DVPP_INTERFACE 1
#include "vega_memory.h"
#include "acl/acl.h"
#include "acl/ops/acl_dvpp.h"
namespace vega {
#define ACL_MM_ALIGN 16
    namespace acl {
        class AclMemDvpp : public MemBase
        {
        public:
            explicit AclMemDvpp() {
            }

            ~AclMemDvpp() override = default;
            static DgError AclMemcpy(void *dst, unsigned int dstBytes, void *src, unsigned int srcBytes, aclrtMemcpyKind kind){
                aclError ret;
                if(dstBytes>4*1024*1024&&srcBytes>4*1024*104){
                    static std::mutex lock;
                    lock.lock();
                    ret = aclrtMemcpy(dst, dstBytes, src, srcBytes, kind);
                    lock.unlock();
                }else{
                    ret = aclrtMemcpy(dst, dstBytes, src, srcBytes, kind);
                }
                if (ret != ACL_ERROR_NONE)
                {
                    std::map<int, std::string> mStrKind{
                        {ACL_MEMCPY_DEVICE_TO_HOST, "device to host"},
                        {ACL_MEMCPY_DEVICE_TO_DEVICE, "device to device"},
                        {ACL_MEMCPY_HOST_TO_DEVICE, "host to device"},
                        {ACL_MEMCPY_HOST_TO_HOST, "host to host"},
                    };
                    LOG(ERROR) << "ACL memcpy fail, errorCode is  " << static_cast<int32_t>(ret) << ", " << mStrKind[kind]
                               << ",dest:" << std::hex << (unsigned long long)dst << ",src: " << (unsigned long long)src
                               << ",dstBytes:" << std::dec <<dstBytes << " ,srcBytes:" << srcBytes << ",total_dvpp_alloc_size_:"
                               << total_dvpp_alloc_size_.load();
                    return DG_ERR_INVALID_ADDRESS;
                }
                return DG_OK;
            }

            std::shared_ptr<MemBase> host() override {
                auto mb = std::make_shared<MemBase>();
                auto err = mb->create(size());
                if (err != DG_OK) {
                    LOG(ERROR) << "Create host mem fail, size : " << size();
                    return nullptr;
                }
                auto ret = AclMemcpy(mb->blob(), mb->size(), blob(), size(), ACL_MEMCPY_DEVICE_TO_HOST);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Memcpy GPU->CPU fail" << ret;
                    return nullptr;
                }
                return mb;
            }

            DgError cpToHost(std::shared_ptr<MemBase> mb,unsigned int cpSize)  {
                if (!mb) {
                    LOG(ERROR) << "Input host mem is null!";
                    return DG_ERR_ABORTED;
                }
                if(cpSize > mb->size() || cpSize > size()){
                    LOG(ERROR) <<" cpSize is too large,cpSize= "<<cpSize<<" ,dst size ="<< mb->size()<<" ,src size="<<size();
                    return DG_ERR_ABORTED;
                }
               auto ret = AclMemcpy(mb->blob(), mb->size(), blob(), cpSize, ACL_MEMCPY_DEVICE_TO_HOST);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Memcpy GPU->CPU fail";
                    return DG_ERR_ABORTED;
                }
                return DG_OK;
            }

            DgError fromHost(std::shared_ptr<MemBase> hostMem) override {
                if (size() != hostMem->size()) {
                    return createFromHost(hostMem);
                } else {
                    return copyFromHost(hostMem);
                }
            }

            DgError copyFromHost(MemBaseSP hmem) {
                if (!good()) {
                    LOG(ERROR) << "Cuda mem not ready";
                    return DG_ERR_INVALID_MEMORY;
                }
                if (!hmem->good()) {
                    LOG(ERROR) << "host mem not ready";
                    return DG_ERR_INVALID_MEMORY;
                }
                if (hmem->size() > size()) {
                    LOG(ERROR) << "gpu mem size not enough for host ";
                    return DG_ERR_MEMORY_SHORTAGE;
                }
                auto ret = AclMemcpy(blob(), hmem->size(), hmem->blob(), hmem->size(), ACL_MEMCPY_HOST_TO_DEVICE);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Memcpy CPU->GPU fail!";
                    return DG_ERR_HW_FAILURE;
                }

                return DG_OK;
            }

            DgError copyFromHost(RawBuffer &rb) {
                if (rb.len_of_byte <= 0) {
                    LOG(ERROR) << "Invalid host memory";
                    return DG_ERR_INVALID_PARAM;
                }
                if (!good()) {
                    LOG(ERROR) << "Gpu mem not ready";
                    return DG_ERR_INIT_FAIL;
                }
                if (rb.len_of_byte != size()) {
                    LOG(ERROR) << "Gpu mem size " << size() << " != " << rb.len_of_byte;
                    return DG_ERR_INVALID_PARAM;
                }
                auto ret = AclMemcpy(blob(), rb.len_of_byte, rb.data.get(), rb.len_of_byte,ACL_MEMCPY_HOST_TO_DEVICE);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Memcpy GPU->CPU fail";
                    return DG_ERR_HW_FAILURE;
                }

                return DG_OK;
            }

            DgError createFromHost(RawBuffer &rb, int align = ACL_MM_ALIGN) {
                if (rb.len_of_byte <= 0) {
                    LOG(ERROR) << "Invalid host memory";
                    return DG_ERR_INVALID_PARAM;
                }

                auto err = create(rb.len_of_byte, align);
                if (err != DG_OK) {
                    LOG(ERROR) << "Create GPU mem fail";
                    return err;
                }

                auto ret = AclMemcpy(blob(), rb.len_of_byte, rb.data.get(), rb.len_of_byte,ACL_MEMCPY_HOST_TO_DEVICE);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Memcpy cpu->gpu fail!";
                    return DG_ERR_HW_FAILURE;
                }

                return DG_OK;
            }

            DgError createFromHost(MemBaseSP host, int align = ACL_MM_ALIGN) {
                if (!host || host->size() <= 0) {
                    LOG(ERROR) << "Invalid host memory";
                    return DG_ERR_INVALID_PARAM;
                }

                auto err = create(host->size(), align);
                if (err != DG_OK) {
                    LOG(ERROR) << "Create GPU mem fail";
                    return err;
                }

                auto ret = AclMemcpy(blob(), host->size(), host->blob(), host->size(), ACL_MEMCPY_HOST_TO_DEVICE);
                if (ret != DG_OK) {
                    LOG(ERROR) << "Memcpy cpu->gpu fail";
                    return DG_ERR_HW_FAILURE;
                }

                return DG_OK;
            }

            std::shared_ptr<MemBase> crop(DG_U8 *addr, int len, int align) override {
                auto mem = std::make_shared<AclMemDvpp>();
                if (addr >= start() && addr + len <= start() + size()) {
                    RawStream rs = RawStream(addr, [=](void *) {});
                    auto err = mem->create(rs, len, align);
                    if (err != DG_OK) {
                        LOG(ERROR) << "Crop failed";
                        return nullptr;
                    }
                    return mem;
                } else {
                    LOG(ERROR) << "Crop invalid address";
                    return nullptr;
                }
            }
            DgError createLessLog(DG_U32 size, DG_U32 align = 4) {
                if (good()) {
                    LOG(ERROR) << "Reallocate is not allowed";
                    return DG_ERR_ALREADY_EXIST;
                }

                if (size == 0) {
                    LOG(ERROR) << "Allocate 0 byte";
                    return DG_ERR_INVALID_PARAM;
                }

                DG_U32 alignOffset = 0;
                blob_ = allocateLogPick(size, align, alignOffset);
                if (!blob_) {
                   LOG_EVERY_N(ERROR,500) << "Allocate memory fail, size " << size << " align " << align;
                    return DG_ERR_MEMORY_SHORTAGE;
                }

                start_offset_ = alignOffset;
                bytes_ = size;
                offset_ = 0;
                return DG_OK;
            }

        protected:
            RawStream allocate(DG_U32 size, DG_U32 align, DG_U32 &offset) override {
                long long total_size = total_normal_alloc_size_.load()+total_dvpp_alloc_size_.load();
                if(total_size>max_device_mem_){
                    LOG(ERROR)<<"Fail to allocate mem, total_allocate_size "<<total_size<<" greater limit "<<max_device_mem_;
                    return nullptr;
                }
                void *ptr = nullptr;
                aclError ret = acldvppMalloc((void **) &ptr, size);// ACL_MEM_MALLOC_HUGE_FIRST);
                if (ACL_RT_SUCCESS != ret) {
                    size_t free=0; 
                    size_t total=0;
                    aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);
                    LOG(ERROR) << "Fail to acldvppMalloc size=" << size << ";ret=" << ret <<"; total_allocate_size "<<total_size
                    <<",Device total memory "<<total<<",free memory "<<free;
                    return nullptr;
                }

                total_dvpp_alloc_size_ += size;
                return RawStream((DG_U8 *) ptr, [=](void *data) {
                    aclError r = acldvppFree(data);
                    total_dvpp_alloc_size_ -= size;
                    if (ACL_RT_SUCCESS != r) {
                        LOG(ERROR) << "Fail to acldvppFree,ret=" << r << "; data=" << std::hex << data;
                    }
                });
            }
            RawStream allocateLogPick(DG_U32 size, DG_U32 align, DG_U32 &offset) {
                long long total_size = total_normal_alloc_size_.load()+total_dvpp_alloc_size_.load();
                if(total_size>max_device_mem_){
                    LOG_EVERY_N(ERROR,500)<<"Fail to allocate mem, total_allocate_size "<<total_size<<" greater limit "<<max_device_mem_;
                    return nullptr;
                }
                void *ptr = nullptr;
                aclError ret = acldvppMalloc((void **) &ptr, size);// ACL_MEM_MALLOC_HUGE_FIRST);
                if (ACL_RT_SUCCESS != ret) {
                    size_t free=0; 
                    size_t total=0;
                    aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);
                    LOG_EVERY_N(ERROR,500) << "Fail to acldvppMalloc size=" << size << ";ret=" << ret <<"; total_allocate_size "<<total_size
                        <<",Device total memory "<<total<<",free memory "<<free;
                    return nullptr;
                }

                total_dvpp_alloc_size_ += size;
                return RawStream((DG_U8 *) ptr, [=](void *data) {
                    aclError r = acldvppFree(data);
                    total_dvpp_alloc_size_ -= size;
                    if (ACL_RT_SUCCESS != r) {
                        LOG(ERROR) << "Fail to acldvppFree,ret=" << r << "; data=" << std::hex << data;
                    }
                });
            }
            public:
            static  std::atomic<long long> total_dvpp_alloc_size_;
            static  std::atomic<long long> total_normal_alloc_size_;
            static long long  max_device_mem_;
        };

        using AclMemDvppSP = std::shared_ptr<AclMemDvpp>;

        class AclMem : public AclMemDvpp {
        public:
            explicit AclMem() {
            }

            ~AclMem() override = default;

        protected:
            RawStream allocate(DG_U32 size, DG_U32 align, DG_U32 &offset) override {
                long long total_size = total_normal_alloc_size_.load()+total_dvpp_alloc_size_.load();
                if(total_size>max_device_mem_){
                    LOG(ERROR)<<"Fail to allocate mem, total_alloc_size "<<total_size<<" greater limit "<<max_device_mem_;
                    return nullptr;
                }
                void *ptr = nullptr;
                total_normal_alloc_size_ += size;
                aclError ret = aclrtMalloc(&ptr, size,ACL_MEM_MALLOC_NORMAL_ONLY);//ACL_MEM_MALLOC_HUGE_FIRST
                if (ACL_RT_SUCCESS != ret) {
                    size_t free=0; 
                    size_t total=0;
                    aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);
                    LOG(ERROR) << "Fail to aclrtMalloc size=" << size << ";ret=" << ret <<"; total_allocate_size "<<total_size
                    <<",Device total memory "<<total<<",free memory "<<free;
                    return nullptr;
                }
                return RawStream((DG_U8 *) ptr, [=](void *data) {
                    total_normal_alloc_size_ -= size;
                    aclError r = aclrtFree(data);
                    if (ACL_RT_SUCCESS != r) {
                        LOGFULL << "Fail to aclrtFree,ret=" << r << "; data=" << std::hex << data;
                    }
                });
            }
        };
        using AclMemSP = std::shared_ptr<AclMem>;
    }
}

#endif //VEGA_ACL_MEMORY_H

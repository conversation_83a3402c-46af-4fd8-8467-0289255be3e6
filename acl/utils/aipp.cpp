#include "inc/aipp.h"
namespace vega {
    namespace acl {
        ColorSpace defaultSpace = ColorSpace::RGB;
        MatrixType getModelInput(std::string modelFmtCfg){
            MatrixType modelInput =  MatrixType ::Undefined;

            if(modelFmtCfg == "bgr_planar") {
                modelInput = MatrixType ::BGRPlanar;
            } else if(modelFmtCfg == "bgr_packed") {
                modelInput = MatrixType ::BGRPacked;
            } else if(modelFmtCfg == "rgb_planar") {
                modelInput = MatrixType ::RGBPlanar;
            } else if(modelFmtCfg == "gray") {
                modelInput = MatrixType ::Gray;
            }else if(modelFmtCfg == "bgra_packed") {
                modelInput = MatrixType ::ARGBPacked;
            }
            return modelInput;
        }
        // nv12_rgb、pc、wide
        AIPPConfig nv12_rgb_pc = AIPPConfig(AippMode::DYNAMIC, aclAippInputFormat::ACL_YUV420SP_U8, true, false, 
                                             256, 0, 359, 
                                             256, -88, -183,
                                             256, 454, 0,
                                             0, 128, 128,
                                             0,0,0);
        
        // nv12_rgb、tv、narrow
        AIPPConfig nv12_rgb_tv = AIPPConfig(AippMode::DYNAMIC, aclAippInputFormat::ACL_YUV420SP_U8, true, false, 
                                        298, 0, 409, 
                                        298, -100, -208,
                                        298, 516, 0,
                                        16, 128, 128,
                                        0,0,0);
        // nv12_bgr、pc、wide
        AIPPConfig nv12_bgr_pc = AIPPConfig(AippMode::DYNAMIC, aclAippInputFormat::ACL_YUV420SP_U8, true, false, 
                                             256, 454, 0, 
                                             256, -88, -183,
                                             256, 0, 359,
                                             0, 128, 128,
                                             0,0,0);
        
        // nv12_bgr、tv、narrow
        AIPPConfig nv12_bgr_tv = AIPPConfig(AippMode::DYNAMIC, aclAippInputFormat::ACL_YUV420SP_U8, true, false, 
                                        298, 516, 0, 
                                        298, -100, -208,
                                        298, 0, 409,
                                        16, 128, 128,
                                        0,0,0);     
        // bgr888(packed)_bgr(planar)
        AIPPConfig bgr_bgr = AIPPConfig(AippMode::DYNAMIC, aclAippInputFormat::ACL_RGB888_U8 , false, false, 
                                298, 516, 0, 
                                298, -100, -208,
                                298, 0, 409,
                                16, 128, 128,
                                0,0,0);
        // bgr888(packed)_bgr(planar)
        AIPPConfig bgr_rgb = AIPPConfig(AippMode::DYNAMIC, aclAippInputFormat::ACL_RGB888_U8 , false, false, 
                                298, 516, 0, 
                                298, -100, -208,
                                298, 0, 409,
                                16, 128, 128,
                                0,0,0);     
        AIPPConfig::AIPPConfig() {}

        AIPPConfig::AIPPConfig(AippMode aipp_mode, aclAippInputFormat input_format, bool csc_switch, bool rbuv_swap_switch, 
            int matrix_r0c0, int matrix_r0c1, int matrix_r0c2, 
            int matrix_r1c0, int matrix_r1c1, int matrix_r1c2,
            int matrix_r2c0, int matrix_r2c1, int matrix_r2c2,
            int input_bias_0, int input_bias_1, int input_bias_2,
            int output_bias_0, int output_bias_1, int output_bias_2)
            : aipp_mode(aipp_mode), input_format(input_format), csc_switch(csc_switch), rbuv_swap_switch(rbuv_swap_switch),
            matrix_r0c0(matrix_r0c0), matrix_r0c1(matrix_r0c1), matrix_r0c2(matrix_r0c2),
            matrix_r1c0(matrix_r1c0), matrix_r1c1(matrix_r1c1), matrix_r1c2(matrix_r1c2),
            matrix_r2c0(matrix_r2c0), matrix_r2c1(matrix_r2c1), matrix_r2c2(matrix_r2c2),
            input_bias_0(input_bias_0), input_bias_1(input_bias_1), input_bias_2(input_bias_2),
            output_bias_0(output_bias_0), output_bias_1(output_bias_1), output_bias_2(output_bias_2) {}

        void AIPPConfig::print() {
            LOG(ERROR) << "AippMode: " << aipp_mode << std::endl
                << "InputFormat: " << input_format << std::endl
                << "CSC Switch: " << csc_switch << std::endl
                << "RB/UV Swap Switch: " << rbuv_swap_switch << std::endl
                << "Matrix: " << std::endl
                << "  [" << matrix_r0c0 << ", " << matrix_r0c1 << ", " << matrix_r0c2 << "]" << std::endl
                << "  [" << matrix_r1c0 << ", " << matrix_r1c1 << ", " << matrix_r1c2 << "]" << std::endl
                << "  [" << matrix_r2c0 << ", " << matrix_r2c1 << ", " << matrix_r2c2 << "]" << std::endl
                << "Input Bias: [" << input_bias_0 << ", " << input_bias_1 << ", " << input_bias_2 << "]" << std::endl
                << "Output Bias: [" << output_bias_0 << ", " << output_bias_1 << ", " << output_bias_2 << "]" << std::endl
                << "Src Image Size (W x H): " << srcImageSizeW << " x " << srcImageSizeH << std::endl
                << "Crop Start Pos (W x H): " << cropStartPosW << " x " << cropStartPosH << std::endl
                << "Crop Size (W x H): " << cropSizeW << " x " << cropSizeH << std::endl
                << "Crop Switch: " << static_cast<int>(cropSwitch) << std::endl  
                << "Var: " << var << std::endl
                << "Mean: [" << mean[0] << ", " << mean[1] << ", " << mean[2] << "]";
        }

        std::pair<ColorRange, ColorSpace> AIPPConfig::voteColorPair(std::vector<ColorRange> colorRange, std::vector<ColorSpace> colorSpace) {
            auto countTv = std::count(colorRange.begin(), colorRange.end(), ColorRange::tv);
            auto countPc = std::count(colorRange.begin(), colorRange.end(), ColorRange::pc);
            ColorRange range = countTv >= countPc ? ColorRange::tv : ColorRange::pc;
            return std::make_pair(range, defaultSpace);
        }

        DgError AIPPConfig::getAIPPConfigByColorPair(std::pair<ColorRange, ColorSpace> &pair, MatrixType &srcType, MatrixType &dstType, cv::Size srcSize, cv::Size dstSize, AIPPConfig &aippConfig) {
            ColorRange range = pair.first;

            if (srcType == MatrixType::NV12) {
                switch (dstType) {
                case MatrixType::BGRPlanar:
                    aippConfig = (range == ColorRange::pc) ? nv12_bgr_pc : nv12_bgr_tv;
                    break;
                case MatrixType::RGBPlanar:
                    aippConfig = (range == ColorRange::pc) ? nv12_rgb_pc : nv12_rgb_tv;
                    break;
                default:
                    LOG(ERROR) << "Not supported color range from " << matrix_type_str(srcType) << " to " << matrix_type_str(dstType)
                               << std::endl;
                    return DG_ERR_NOT_SUPPORTED;
                }
            }else if(srcType == MatrixType::BGRPacked){
                switch (dstType) {
                case MatrixType::BGRPlanar:
                    aippConfig = bgr_bgr;
                    break;
                case MatrixType::RGBPlanar:
                    aippConfig = bgr_rgb;
                    break;
                default:
                    LOG(ERROR) << "Not supported color range from " << matrix_type_str(srcType) << " to " << matrix_type_str(dstType)
                               << std::endl;
                    return DG_ERR_NOT_SUPPORTED;
                }
            }else {
                LOG(ERROR) << "Not supported color range from " << matrix_type_str(srcType) << " to " << matrix_type_str(dstType)
                           << std::endl;
                return DG_ERR_NOT_SUPPORTED;
            }

            aippConfig.srcImageSizeW = srcSize.width;
            aippConfig.srcImageSizeH = srcSize.height;

            if (srcSize.area() != dstSize.area()) {
                aippConfig.cropSwitch = 1;
                aippConfig.cropStartPosW = 0;
                aippConfig.cropStartPosH = 0;
                aippConfig.cropSizeW = dstSize.width;
                aippConfig.cropSizeH = dstSize.height;
            } else {
                aippConfig.cropSwitch = 0;
            }
            return DG_OK;
        }                  
    }
}
//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 03/18/2021.
//

#ifndef VEGA_ACL_IMAGE_H
#define VEGA_ACL_IMAGE_H

#include "vega_matrix.h"
#include "acl_memory.h"
#include "acl/acl.h"

namespace vega {
    extern bool isAclSoc();
    namespace acl {

        class AclImage : public Matrix {
        public:
            AclImage() {
            }
            ~AclImage() override = default;
#define ACL_IMAGE_ALIGN_H 2
#define ACL_IMAGE_ALIGN_W 16
        public:
            DgError cacReqMemSize(MatrixType type, const cv::Size &size,int &reqMemSize,MatrixAlign *ma= nullptr) override{
                reqMemSize = 0;
                MatrixAlign matrixAlign;
                if (ma == nullptr) {
                    reqMemSize = 0;
                    matrixAlign.mem_ = VEGA_DATA_SIZE(VegaDataType::CHAR);
                    matrixAlign.mode_ = MatrixAlignMode::PIXEL;
                    matrixAlign.stride_ = cv::Size(ACL_IMAGE_ALIGN_W, ACL_IMAGE_ALIGN_H);
                    ma=&matrixAlign;
                }
                MatrixProperty mp(VegaDataType::CHAR);
                auto err = mp.create(type, size, *ma);
                if(err!=DG_OK){
                    LOG(ERROR) <<" Fail to creat MatrixProperty!";
                    return err;
                }
                reqMemSize= mp.eval();
                return err;
            };
            DgError create(MatrixType type, const cv::Size &size, int element_bytes, RawStream &data, bool isHostMem=true) {
                AclMemDvppSP mem;
                mem = std::make_shared<AclMemDvpp>();
                cv::Size stride_size( (size.width+ACL_IMAGE_ALIGN_W-1)/ACL_IMAGE_ALIGN_W*ACL_IMAGE_ALIGN_W,
                        (size.height+ACL_IMAGE_ALIGN_H-1)/ACL_IMAGE_ALIGN_H*ACL_IMAGE_ALIGN_H);
                auto err = mem->create(stride_size.area()*element_bytes, ACL_MM_ALIGN);
                if(DG_OK != err) {
                    LOG(ERROR) << "Create hiai mem failed";
                    return err;
                }
                aclrtMemcpyKind mkind=ACL_MEMCPY_HOST_TO_DEVICE;
                if(!isHostMem){
                   mkind= ACL_MEMCPY_DEVICE_TO_DEVICE;
                }
                uchar *dst = (uchar *) mem->blob();
                uchar *src = (uchar *) data.get();
                if(size.width==stride_size.width){
                    aclError ret = aclrtMemcpy(dst, mem->size(), src, size.area() * element_bytes,mkind);
                    if (ret != ACL_RT_SUCCESS) {
                        LOG(ERROR)<<"Fail to aclrtMemcpy,ret="<<ret;
                        return DG_ERR_ABORTED;
                    }
                }else {
                    unsigned int max_size_cpyed=mem->size();
                    for (int i = 0; i < size.height; i++) {
                        aclError ret = aclrtMemcpy(dst, max_size_cpyed, src, size.width * element_bytes,
                                                   mkind);
                        if (ret != ACL_RT_SUCCESS) {
                            LOG(ERROR)<<"Fail to aclrtMemcpy,ret="<<ret;
                            return DG_ERR_ABORTED;
                        }
                        max_size_cpyed-=stride_size.width * element_bytes;
                        dst += stride_size.width * element_bytes;
                        src += size.width * element_bytes;
                    }
                }
                return create(type, size, mem, ACL_MM_ALIGN, stride_size.width,stride_size.height);
            }


            DgError create(MatrixType type, const cv::Size &size, AclMemDvppSP mem, int align,int stride_w,int stride_h,VegaDataType dataType=VegaDataType::CHAR) {
                if (mem) {
                    if ((long)mem->blob() % align != 0) {
                        LOG(ERROR) << "invalid mem alignment " << align;
                        return DG_ERR_INVALID_PARAM;
                    }
                }

                MatrixAlign ma;
                createAlign(type, ma, stride_w,stride_h,align);

                MatrixProperty mp(dataType);
                auto err = mp.create(type, size, ma);
                if(err != DG_OK) {
                    LOG(ERROR) << "Invalid size or type";
                    return DG_ERR_INVALID_PARAM;
                }
                if(mem)
                    return Matrix::create(mp, mem);
                else 
                    return create(mp, ma.mem_);
            }
            DgError create(MatrixType type, const cv::Size &size, AclMemDvppSP mem = nullptr) {
                return create(type, VegaDataType::CHAR, size, mem);
            }

            DgError create(MatrixType type, const cv::Size &size, RawBuffer &data,const cv::Size &stride , int align ,bool isHostMem=true) {
                AclMemDvppSP mem = std::make_shared<AclMemDvpp>();
                cv::Size stride_size( (size.width+ACL_IMAGE_ALIGN_W-1)/ACL_IMAGE_ALIGN_W*ACL_IMAGE_ALIGN_W,
                                                      (size.height+ACL_IMAGE_ALIGN_H-1)/ACL_IMAGE_ALIGN_H*ACL_IMAGE_ALIGN_H);;
                auto memSize = 0;
                unsigned int element_bytes = UNIT(type);
                auto height = size.height;
                switch (type) {
                    case MatrixType ::BGRPacked:
                    case MatrixType ::RGBPacked: {
                        memSize = stride_size.area()*3;
                        break;
                    }
                    case MatrixType ::NV21:
                    case MatrixType ::NV12: {
                        memSize = stride_size.area()*3/2;
                        height = size.height*3/2;
                        break;
                    }
                    default:
                        LOG(ERROR) << "Invalid type";
                        break;
                }

                auto err = mem->create(memSize , ACL_MM_ALIGN);
                if(DG_OK != err) {
                    LOG(ERROR) << "Create acl mem failed";
                    return err;
                }
                aclrtMemcpyKind mkind=ACL_MEMCPY_HOST_TO_DEVICE;
                if(!isHostMem){
                    mkind= ACL_MEMCPY_DEVICE_TO_DEVICE;
                }
                uchar *dst = (uchar *) mem->blob();
                uchar *src = (uchar *) data.data.get();
                if(size.width==stride_size.width){
                    aclError ret = aclrtMemcpy(dst, mem->size(), src, size.area() * element_bytes,mkind);
                    if (ret != ACL_RT_SUCCESS) {
                        LOG(ERROR)<<"Fail to aclrtMemcpy,ret="<<ret<<";src size "<<size.area() * element_bytes<<",dst size "<<mem->size();
                        return DG_ERR_ABORTED;
                    }
                }else {
                    unsigned int max_size_cpyed=mem->size();
                    for (int i = 0; i < height; i++) {
                        aclError ret = aclrtMemcpy(dst, max_size_cpyed, src, size.width * element_bytes,
                                                   mkind);
                        if (ret != ACL_RT_SUCCESS) {
                            LOG(ERROR)<<"Fail to aclrtMemcpy,ret="<<ret;
                            return DG_ERR_ABORTED;
                        }
                        max_size_cpyed-=stride_size.width * element_bytes;
                        dst += stride_size.width * element_bytes;
                        src += size.width * element_bytes;
                    }
                }
                return create(type, size, mem, ACL_MM_ALIGN, stride_size.width,stride_size.height);
            }

            MemBaseSP createMemory(DG_U32 size, DG_U32 align) override {
                auto mem = std::make_shared<AclMemDvpp>();
                auto ret = mem->create(size, align);
                if(ret != DG_OK) {
                    LOG(ERROR) << "Create memory failed";
                    return nullptr;
                }
                return mem;
            }
            DgError create(const MatrixProperty &property, int memAlign) {
                if(mem_ && mem_->good()) {
                    LOG(ERROR) << "Duplicate creation";
                    return DG_ERR_NOT_ALLOWED;
                }

                if(!property.good()) {
                    LOG(ERROR) << "Property invalid";
                    return DG_ERR_INVALID_PARAM;
                }

                auto msz = property.eval();
                if(msz <= 0) {
                    LOG(ERROR) << "Memory size invalid: " << msz;
                    return DG_ERR_INVALID_PARAM;
                }
                mem_ = createMemory((DG_U32)msz, (DG_U32)memAlign);
                if(!mem_) {
                    LOG(ERROR) << "Memory create fail";
                    return DG_ERR_MEMORY_SHORTAGE;
                }
                property_ = property;
                return DG_OK;
            }
            DgError create(MatrixType type, VegaDataType dataType, const cv::Size &size, AclMemDvppSP mem = nullptr) {
                MatrixAlign ma;
                ma.mem_ = VEGA_DATA_SIZE(dataType);
                ma.mode_ = MatrixAlignMode ::PIXEL;
                if(type==MatrixType::JPEG)
                    ma.stride_ = cv::Size(1, 1);
                else
                    ma.stride_ = cv::Size(ACL_IMAGE_ALIGN_W, ACL_IMAGE_ALIGN_H);

                MatrixProperty mp(dataType);
                auto err = mp.create(type, size, ma);
                if(err != DG_OK) {
                    LOG(ERROR) << "Invalid property";
                    return err;
                }

                if(mem)
                    return Matrix::create(mp, mem);
                else
                    return create(mp, ma.mem_);
            }
            void createAlign(MatrixType type, MatrixAlign &ma, int stride_w,int stride_h,int align = ACL_MM_ALIGN) {
                ma.mode_ = MatrixAlignMode ::PIXEL;
                ma.mem_ = (DG_U32) align;
                ma.stride_ = {stride_w, stride_h};
            }
            DgError copy(std::shared_ptr<AclImage> input) {
                if(input->type()!=type()){
                   LOG(ERROR)<<"input type!=output type,input type:"<<matrix_type_str(input->type())<<",out type:"<<matrix_type_str(type());
                   return DG_ERR_INVALID_PARAM;
                }
                if(type()!=MatrixType::NV12&&type()!=MatrixType::BGRPacked){
                    LOG(ERROR)<<"Not support type()"<<matrix_type_str(type());
                    return DG_ERR_INVALID_PARAM;
                }
                if(input->size()!=size()){
                    LOG(ERROR)<<"input size != output size; input size:"<<input->size()<<" output size:"<<size();
                    return DG_ERR_INVALID_PARAM;
                }
                aclrtMemcpyKind mkind=ACL_MEMCPY_DEVICE_TO_DEVICE;
                uchar *dst = (uchar *) memory()->blob();
                uchar *src = (uchar *) input->memory()->blob();
                int elementSize=1;
                int height=size().height;
                if(type()==MatrixType::BGRPacked){
                    elementSize=3;
                }else if(type()==MatrixType::NV12){
                    if(input->stride().height<stride().height){
                    LOG(ERROR)<<" input stride height "<<input->stride().height<<"<  heigh "<<stride().height;
                        return DG_ERR_INVALID_PARAM;
                    }
                    elementSize=1;
                    height=stride().height*3/2;
                }
                if(stride().width==input->stride().width){
                    aclError ret = aclrtMemcpy(dst, stride().width*size().height, src, stride().width*height,mkind);
                    if (ret != ACL_RT_SUCCESS) {
                        LOG(ERROR)<<"Fail to aclrtMemcpy,ret="<<ret;
                        return DG_ERR_ABORTED;
                    }
                }else {
                    int max_size_cpyed=memory()->size();
                    unsigned int size_cpyed=size().width*elementSize;
                    for (int i = 0; i < height; i++) {
                        aclError ret = aclrtMemcpy(dst, max_size_cpyed, src, size_cpyed,mkind);
                        if (ret != ACL_RT_SUCCESS) {
                            LOG(ERROR)<<"Fail to aclrtMemcpy,ret="<<ret;
                            return DG_ERR_ABORTED;
                        }
                        dst += stride().width;
                        max_size_cpyed-=stride().width;
                        src += input->stride().width;
                    }
                }
                return DG_OK;
            }

            std::shared_ptr<Matrix> host(bool syncBack) override {
                if(syncBack){
                    if(isAclSoc()){
                        auto ret = std::make_shared<Matrix>();
                        *ret = *this;
                        return ret;
                    }
                }
                if(!good()) {
                    LOG(ERROR) << "Invalid matrix";
                    return nullptr;
                }
                auto hmem = memory()->host();
                /** clone() should not be used here */
                Matrix *pme = this;
                std::shared_ptr<Matrix> ret(new Matrix(),
                                            [=](Matrix *m) {
                                                if(syncBack) {
                                                    auto mem = this->memory();
                                                    auto err = mem->fromHost(m->memory());
                                                    if(err != DG_OK) {
                                                        LOG(ERROR) << "Copy from host to device fail";
                                                    } else {
                                                        *((Matrix *)this) = *m;
                                                        err = this->setMemory(mem);
                                                        if(err != DG_OK) {
                                                            LOG(ERROR) << "Replace host memory of matrix failed";
                                                        }
                                                    }
                                                }
                                                delete m;
                                            });

                *ret = *pme;

                auto err = ret->setMemory(hmem);
                if(err != DG_OK) {
                    LOG(ERROR) << "Replace host memory of matrix failed";
                    return nullptr;
                }

                return ret;
            }
            DgError fromHost(std::shared_ptr<Matrix> hostMatrix) override {
                if(!hostMatrix || !hostMatrix->good()) {
                    LOG(ERROR) << "Invalid device matrix";
                    return DG_ERR_INVALID_IMAGE;
                }

                if(!good()) {
                    LOG(ERROR) << "Invalid device matrix";
                    return DG_ERR_INVALID_IMAGE;
                }

                /**
                 * Copy host memory to device
                 */
                auto mem = memory();
                auto err = mem->fromHost(hostMatrix->memory());
                if(err != DG_OK) {
                    LOG(ERROR) << "Copy from host to device fail";
                    return err;
                }

                /** clone() should not be used here */
                *((Matrix *)this) = *(hostMatrix.get());
                err = setMemory(mem);
                if(err != DG_OK) {
                    LOG(ERROR) << "Replace host memory of matrix failed";
                }

                return err;
            }
            std::shared_ptr<Matrix> clone() override {
                auto out = std::make_shared<AclImage>();
                *out = *this;
                return out;
            }
        };
        using AclImageSP = std::shared_ptr<AclImage>;
    }
}
#endif //VEGA_ACL_IMAGE_H

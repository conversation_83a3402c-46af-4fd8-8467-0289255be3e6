#ifndef VEGA_ACL_AIPP_H
#define VEGA_ACL_AIPP_H
#include "vega_matrix.h"
#include "acl/acl.h"
namespace vega {
    namespace acl{
        class AIPPConfig;
        struct ACLAippInfo {
            public:
                std::vector<ColorRange> colorRange;
                std::vector<ColorSpace> colorSpace;
                std::vector<cv::Size>  size;
                MatrixType matrixType;
        };
        extern ColorSpace defaultSpace;
        extern AIPPConfig nv12_bgr_pc;
        extern AIPPConfig nv12_bgr_tv;
        extern AIPPConfig nv12_rgb_tv;
        extern AIPPConfig nv12_rgb_pc;
        enum AippMode { STATIC, DYNAMIC }; 
        class AIPPConfig {
        public:
            AIPPConfig();
            AIPPConfig(AippMode aipp_mode, aclAippInputFormat input_format, bool csc_switch, bool rbuv_swap_switch, 
                int matrix_r0c0, int matrix_r0c1, int matrix_r0c2, 
                int matrix_r1c0, int matrix_r1c1, int matrix_r1c2,
                int matrix_r2c0, int matrix_r2c1, int matrix_r2c2,
                int input_bias_0, int input_bias_1, int input_bias_2,
                int output_bias_0, int output_bias_1, int output_bias_2);

            void print();

            static std::pair<ColorRange, ColorSpace> voteColorPair(std::vector<ColorRange> colorRange, std::vector<ColorSpace> colorSpace);
            static DgError getAIPPConfigByColorPair(std::pair<ColorRange, ColorSpace> &pair, MatrixType &srcType, MatrixType &dstType, cv::Size srcSize, cv::Size dstSize, AIPPConfig &aippConfig);

            AippMode aipp_mode;
            aclAippInputFormat input_format;
            bool csc_switch;
            bool rbuv_swap_switch;
            int matrix_r0c0, matrix_r0c1, matrix_r0c2;
            int matrix_r1c0, matrix_r1c1, matrix_r1c2;
            int matrix_r2c0, matrix_r2c1, matrix_r2c2;
            int input_bias_0, input_bias_1, input_bias_2;
            int output_bias_0, output_bias_1, output_bias_2;
            int32_t srcImageSizeW, srcImageSizeH;
            int32_t cropStartPosW, cropStartPosH;
            int32_t cropSizeW, cropSizeH;
            int8_t cropSwitch;
            float var;
            std::vector<float> mean;

            typedef std::pair<ColorRange, ColorSpace> ColorPair;
        };
        MatrixType getModelInput(std::string modelFmtCfg);
    }
}
#endif //VEGA_ACL_AIPP_H

//
// Created by root on 7/7/21.
//

#ifndef VEGA_ACL_FRAMEPOOL_H
#define VEGA_ACL_FRAMEPOOL_H

#include <common/Platform/utils/FrameBufferPool.h>
#include "acl_image.h"

#define DVPP_MEM_MAX 17179869184LL //16*1024*1024*1024LL 16G
#define MEM_MARGIN 1610612736LL //1024*1024*1024*1.5;//1.5G
#define DVPP_MARGIN 1073741824LL // 1024*1024*1024;//1G
namespace vega {
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        class AclMemBlock{
        public:
            typedef struct{
                unsigned long long id;
                unsigned int count;
                std::queue<AclMemDvppSP> memCropQ;
                AclMemDvppSP mem;
            }MemLst;
            typedef struct{
                unsigned long long id;
                AclMemDvppSP mem;
            }MemCrop;
 
            AclMemBlock(unsigned int memSize){
                memSize_=memSize;
                memSizeAlign_=(memSize+127)/128*128;
                platform=vegaGetPlatform();
            }
            ~AclMemBlock(){}
            MemCrop get(){
                MemCrop memCrop;
                memCrop.id=0;
                if(!memFreeLst_.empty()){ 
                    //Use the minimum  patch memory first to reduce memory fragment
                    auto itFreeLst=memFreeLst_.begin();
                    auto itMin=memFreeLst_.begin();
                    unsigned int min=itMin->second;
                    ++itFreeLst;
                    for(;itFreeLst!=memFreeLst_.end();++itFreeLst){
                        if(itFreeLst->second<min){
                            itMin=itFreeLst;
                            min=itMin->second;
                        }
                    }
                    memCrop.id=itMin->first;
                    itMin->second--;
                    unsigned int freeNum=itMin->second;
                    if(freeNum==0){
                        memFreeLst_.erase(itMin);
                    }

                    std::map<unsigned long long,MemLst>::iterator it=mMemLst_.find(memCrop.id);
                    if(it!=mMemLst_.end()){
                        if(!it->second.memCropQ.empty()){
                            memCrop.mem=it->second.memCropQ.front();
                            it->second.memCropQ.pop();
                            if(freeNum!=it->second.memCropQ.size()){
                                LOG(FATAL)<<"There is something wrong in memFreeLst_ and mMemLst_";
                            }
                        }
                    }
                    if(!memCrop.mem){
                         memCrop.id=0;
                        LOG(FATAL)<<"There is something wrong in memFreeLst_ and mMemLst_";
                    }
                }
                if(!memCrop.mem){
                    if (platform == sdk_config::Atlas300VPro) {
                        size_t free = 0;
                        size_t total = 0;
                        aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);

                        if (free && free < MEM_MARGIN) {
                            LOG_EVERY_N(ERROR, 500) << "acl normal memory total " << total << ",free memory is " << free
                                                    << ",less than margin limition" << MEM_MARGIN;
                            return memCrop;
                        }
                        auto total_dvpp_alloc_size = acl::AclMemDvpp::total_dvpp_alloc_size_.load();
                        if (DVPP_MEM_MAX > total_dvpp_alloc_size) {
                            auto margin = DVPP_MEM_MAX - total_dvpp_alloc_size;
                            if (margin < DVPP_MARGIN) {
                                LOG_EVERY_N(ERROR, 500)
                                  << "acl total_dvpp_alloc_size " << total_dvpp_alloc_size << " greater than limition";
                                return memCrop;
                            }
                        } else {
                            LOG(ERROR) << "total_dvpp_alloc_size " << total_dvpp_alloc_size << "is abnormal!";
                        }
                    }
                    auto aclMemDvpp = std::make_shared<AclMemDvpp>();
                    if(DG_OK!=aclMemDvpp->create(memSizeAlign_*num_)){
                        LOG(ERROR)<<"fail to create aclMemDvpp,memory size "<<memSize_*num_;
                        return memCrop;
                    }
                    MemLst memLst;
                    DG_U8 *addr=(DG_U8 *)aclMemDvpp->blob();
                    memLst.mem=aclMemDvpp;
                    memLst.id=(unsigned long long)addr;
                    for(int i=0;i<num_;i++){
                        auto mem=std::dynamic_pointer_cast<AclMemDvpp>(aclMemDvpp->crop(addr, memSize_,4));
                        addr+=memSizeAlign_;
                        if(!mem){
                            LOG(ERROR)<<"Fail to crop aclMemDvpp";
                            continue; 
                        }
                        memLst.memCropQ.push(mem);
                    }
                    memLst.count=memLst.memCropQ.size();
                    if(memLst.count>0){
                        memCrop.mem=memLst.memCropQ.front();
                        memLst.memCropQ.pop();
                        memCrop.id=memLst.id;
                        mMemLst_[memLst.id]=memLst;
                        if(!memLst.memCropQ.empty())
                            memFreeLst_[memLst.id]=memLst.memCropQ.size();
                    }
                }
                return memCrop;
            }
            void push(MemCrop memCrop){
                if(!memCrop.mem){
                      LOG_EVERY_N(ERROR,500)<<"invalid memCrop!";
                    return;
                }
                std::map<unsigned long long,MemLst>::iterator it=mMemLst_.find(memCrop.id);
                if(it!=mMemLst_.end()){
                    it->second.memCropQ.push(memCrop.mem);
                    auto itFreeLst=memFreeLst_.find(memCrop.id);
                    if(it->second.count==it->second.memCropQ.size()){
                        mMemLst_.erase(it); //No one else use this memory block then free this memory block;
                        int count=it->second.count;
                        if(itFreeLst!=memFreeLst_.end()) 
                            memFreeLst_.erase(itFreeLst);
                        else if(count>1)
                            LOG(FATAL)<<"There is something wrong in memFreeLst_ and mMemLst_";
                    }else {
                        if(itFreeLst!=memFreeLst_.end()) {
                            itFreeLst->second++;
                            if(itFreeLst->second!=it->second.memCropQ.size()){
                                LOG(FATAL)<<"There is something wrong in memFreeLst_ and mMemLst_";
                            }
                        }else{
                            memFreeLst_[memCrop.id]=1;
                            if(it->second.memCropQ.size()!=1){
                                LOG(FATAL)<<"There is something wrong in memFreeLst_ and mMemLst_";
                            }
                        }
                    }
                }else {
                    LOG(FATAL)<<"There is something wrong in mMemLst_";
                }
            }
            unsigned int memSize_=0;
            private:
            unsigned int memSizeAlign_=0;
            int num_=16; 
            std::map<unsigned long long,MemLst> mMemLst_;
            std::map<unsigned long long,unsigned int> memFreeLst_;
            sdk_config::Platform platform;
        };
        class AclFrameBuffer: public FrameBuffer {
        public:
            AclFrameBuffer(){};

            ~AclFrameBuffer(){
                UnInit();
            };

            DgError Init(unsigned int mem_size) override {
                map_id_ = mem_size;
                std::map<unsigned int,std::shared_ptr<AclMemBlock>>::iterator it=mMemBlock_.find(map_id_);
                if(it!=mMemBlock_.end()){
                    memCrop_=it->second->get();
                }else{
                    std::shared_ptr<AclMemBlock> aclMemBlockPtr=std::make_shared<AclMemBlock>(map_id_);
                    mMemBlock_[map_id_]=aclMemBlockPtr;
                    memCrop_=aclMemBlockPtr->get();
                }
                if(!memCrop_.mem){
                     LOG_EVERY_N(ERROR,500)<<"AclFrameBuffer fail to init!";
                    return DG_ERR_INIT_FAIL;
                }
                mem_size_ = mem_size;
                return DG_OK;
            }

            DgError UnInit() override {
                std::map<unsigned int,std::shared_ptr<AclMemBlock>>::iterator it=mMemBlock_.find(map_id_);
                if(it!=mMemBlock_.end()){
                    it->second->push(memCrop_);
                }
                return DG_OK;
            }
            AclMemDvppSP getMem(){
                return memCrop_.mem;
            }

        private:
            AclMemBlock::MemCrop memCrop_;
            static std::map<unsigned int,std::shared_ptr<AclMemBlock>> mMemBlock_;
        };
        class AclFramePoolMap: public FramePoolMap<AclFrameBuffer> {
        public:
            AclFramePoolMap() {
                platform=vegaGetPlatform();
                max_frames_mem_ = sdk_config::get_cfg<long long>("max_frames_mem", max_frames_mem_, platform);
                max_frames_store_total_ = sdk_config::get_cfg<int>("max_frames_store_total", max_frames_store_total_, platform);
                LOG(ERROR)<<"max_frames_mem: "<<max_frames_mem_;
                LOG(ERROR)<<"max_frames_store_total: "<<max_frames_store_total_;
            }
            ~AclFramePoolMap(){};
            virtual bool IsMemShortage(size_t &free, size_t &total) override {
                if (platform == sdk_config::Atlas300VPro) {
                    free = 0;
                    total = 0;

                    auto ret=aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);
		    if(ret!=ACL_SUCCESS){
		    	LOG(ERROR)<<"Fail to aclrtGetMemInfo,error code is "<<ret;
			return false;
		    }
                    if (free < MEM_MARGIN) {
			LOG(ERROR)<<"aclrtGetMemInfo: free memory is "<<free;
                        return true;
                    }
                    auto total_dvpp_alloc_size = acl::AclMemDvpp::total_dvpp_alloc_size_.load();
                    if (DVPP_MEM_MAX > total_dvpp_alloc_size) {
                        auto margin = DVPP_MEM_MAX - total_dvpp_alloc_size;
                        if (margin < DVPP_MARGIN) {
			    LOG(ERROR)<<"aclrtGetMemInfo: free memory is "<<free;
                            free=margin;
                            return true;
                        }
                    }
                }
                return false;
            }

            static AclFramePoolMap & getInstance() {
                if (!me_) {
                    static std::mutex mtx;
                    std::unique_lock<std::mutex> mlock(mtx);
                    if(!me_)
                        me_ = std::shared_ptr<AclFramePoolMap>(new AclFramePoolMap());
                }
                return *me_;
            }

            static void destroy() {
                if (me_) {
                    static std::mutex mtx;
                    std::unique_lock<std::mutex> mlock(mtx);
                    if(me_)
                        me_.reset();
                }
            }
        protected:
            sdk_config::Platform platform;
            static std::shared_ptr<AclFramePoolMap> me_;
        };
    }
}


#endif //VEGA_ACL_FRAMEPOOL_H

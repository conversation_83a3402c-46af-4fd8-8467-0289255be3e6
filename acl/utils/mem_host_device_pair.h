//
// Created by ch<PERSON><PERSON><PERSON><PERSON> on 6/29/22.
//
#if ACL
#ifndef VEGA_ACL_MEM_HOST_DEVICE_PAIR_H
#define VEGA_ACL_MEM_HOST_DEVICE_PAIR_H
#include <vector>
#include "utils/acl_memory.h"
#include "queue/blockingconcurrentqueue.h"
namespace vega{
    namespace acl {
        template<typename T>
        class MemHostDevicePool {
        public:
            typedef struct {
                T *host;
                AclMemDvppSP device;
                int size;
                bool isQueue;
            } MemPair;
            using MemPairSP = std::shared_ptr<MemPair>;
            static DgError SynHostToDevice(MemPairSP mem)
            {
                 auto ret=AclMemDvpp::AclMemcpy(mem->device->blob(), mem->size*sizeof(T), mem->host,
                  mem->size*sizeof(T), ACL_MEMCPY_HOST_TO_DEVICE);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to AclMemcpy!";
                    return DG_ERR_HW_FAILURE;
                }
                return DG_OK;
            }
            static DgError SynDeviceToHost(MemPairSP mem){
                 auto ret=AclMemDvpp::AclMemcpy(mem->host, mem->size*sizeof(T),mem->device->blob(),
                  mem->size*sizeof(T), ACL_MEMCPY_DEVICE_TO_HOST);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to AclMemcpy!";
                    return DG_ERR_HW_FAILURE;
                }
                return DG_OK;
            }
            MemHostDevicePool(int counter, int block_size,bool host=true, bool device=true) {
                block_size_=block_size;
                host_=host;
                device_=device;
                for (int i = 0; i < counter; i++) {
                    auto mem=creat_mem();
                    if(mem)
                        q_.enqueue(mem);
                    else
                        LOG(ERROR)<<"Fail to creat_mem!";
                }
            }

            ~MemHostDevicePool() {
                MemPairSP mem;
                while (q_.try_dequeue(mem)) {}
            }
           
            void push(std::shared_ptr<MemPair> mem) {
                if(mem->isQueue)
                    q_.enqueue(mem);
            }
            MemPairSP creat_mem(){
                MemPairSP mem = std::shared_ptr<MemPair>(new MemPair, [](void *data){
                    MemPair *mem_pair = (MemPair *) data;
                    if(mem_pair->host)
                        delete [] mem_pair->host;
                    delete mem_pair; });
                mem->host = nullptr;
                if (device_){
                    mem->device = std::make_shared<AclMemDvpp>();
                    if(DG_OK!=mem->device->create(block_size_ * sizeof(T))){
                        LOG(ERROR)<<"fail to create  AclMemDvpp";
                        return nullptr;
                    }
                }
                if (host_)
                    mem->host = new T[block_size_];
                mem->size = block_size_;
                mem->isQueue = true;
                return mem;
            }
            MemPairSP pop() {
                MemPairSP mem;
                if(!q_.wait_dequeue_timed(mem, std::chrono::milliseconds(500))) {
                    mem=creat_mem();
                    if(mem)
                        mem->isQueue=false;
                    else
                        LOG(ERROR)<<"Fail to creat_mem!";
                }
                return mem;
            }

        private:
            moodycamel::BlockingConcurrentQueue<std::shared_ptr<MemPair>> q_;
            int block_size_=0;
            bool host_;
            bool device_;
        };
    }
}
#endif
#endif
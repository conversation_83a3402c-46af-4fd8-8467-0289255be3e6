#ifndef VEGA_ACL_DVPP_COMMON_H
#define VEGA_ACL_DVPP_COMMON_H

#include "acl/acl.h"
#include "acl/ops/acl_dvpp.h"
#include "common/Platform/processor/video_decoder_buffer.h"
#include "common/Platform/processor/video_encoder_buffer.h"
#if CANN50 && ACL_MEDIA_V2
# include "acl/dvpp/hi_dvpp.h" //v2 depence
#endif

#define DVPP_ALIGN_UP(x, align) ((((x) + ((align)-1)) / (align)) * (align))

const int YUV_BGR_SIZE_CONVERT_3 = 3;
const int YUV_BGR_SIZE_CONVERT_2 = 2;
const uint32_t VDEC_STRIDE_WIDTH = 16; // Vdec module output width need to align up to 16
const uint32_t VDEC_STRIDE_HEIGHT = 2; // Vdec module output width need to align up to 2
const uint32_t VDEC_SPECIAL_WIDTH = 16;
const uint32_t VDEC_SPECIAL_STRIDE = 32; // If the output picture width is less than 16, it must be aligned to 32

const uint32_t MAX_JPEGD_WIDTH = 8192;  // Max width of jpegd module
const uint32_t MAX_JPEGD_HEIGHT = 8192; // Max height of jpegd module
const uint32_t MIN_JPEGD_WIDTH = 32;    // Min width of jpegd module
const uint32_t MIN_JPEGD_HEIGHT = 32;   // Min height of jpegd module
const uint32_t MIN_JPEGD_ALIGN = 2;     // MIN_ALIGN

const uint32_t MAX_VDEC_WIDTH = 4096;   // Max width of vdec module
const uint32_t MAX_VDEC_HEIGHT = 4096;  // Max width of vdec module
const uint32_t MIN_VDEC_WIDTH = 128;    // Min width of jpegd module
const uint32_t MIN_VDEC_HEIGHT = 128;   // Min height of jpegd module
const uint32_t MIN_VDEC_ALIGN = 2;      // MIN_ALIGN

const uint32_t MIN_VPC_WIDTH = 10;// Max width of vpc module
const uint32_t MIN_VPC_HEIGHT = 6;// Min width of vpc module

const uint32_t JPEGE_STRIDE_WIDTH = 16; // Jpege module input width need to align up to 16
const uint32_t JPEGE_STRIDE_HEIGHT = 1; // Jpege module input height remains unchanged
const uint32_t YUV_BYTES_NU = 3;        // Numerator of yuv image, H x W x 3 / 2
const uint32_t YUV_BYTES_DE = 2;        // Denominator of yuv image, H x W x 3 / 2

const float MIN_RESIZE_SCALE = 0.03125; // Min resize scale of resize module
const float MAX_RESIZE_SCALE = 16.0;    // Min resize scale of resize module
const uint32_t VPC_STRIDE_WIDTH = 16;   // Vpc module output width need to align up to 16
const uint32_t VPC_STRIDE_HEIGHT = 2;   // Vpc module output height need to align up to 2
const uint8_t YUV_GREYER_VALUE = 128;   // Filling value of the resized YUV image

const uint32_t DVPP_IMAGE_ALIGN = 128; // dvpp Aline

typedef struct tagDvppDataInfo
{
    uint32_t width = 0;                                          // Width of image
    uint32_t height = 0;                                         // Height of image
    uint32_t strideW = 0;                                        // Width after align up
    uint32_t strideH = 0;                                        // Height after align up
    uint32_t frameId = 0;                                        // Needed by video
    uint32_t dataSize = 0;                                       // Size of data in byte
    acldvppPixelFormat format = PIXEL_FORMAT_YUV_SEMIPLANAR_420; // Format of image
    uint8_t *data = nullptr;                                     // Image data
} DvppDataInfo;
typedef std::shared_ptr<DvppDataInfo> DvppDataInfoSP;

namespace vega {
    namespace acl {
        class AclImageDecoderBase {
        public:
            AclImageDecoderBase() = default;
            virtual ~AclImageDecoderBase() = default;
            virtual DgError JpegDecode(const MatrixSP &input, MatrixSP &output, MatrixType outputType) = 0;
        };
        using AclImageDecoderBaseSP = std::shared_ptr<AclImageDecoderBase>;

        class AclImageEncoderBase {
        public:
            AclImageEncoderBase() = default;
            virtual ~AclImageEncoderBase() = default;
            virtual DgError JpegEncode(MatrixSP &input, MatrixSP &output, int quality) = 0;
        };
        using AclImageEncoderBaseSP = std::shared_ptr<AclImageEncoderBase>;

        class AclVDecProcBase {
        public:
            AclVDecProcBase() = default;
            virtual ~AclVDecProcBase() = default;
            virtual DgError Init(const aclrtStream &stream, uint32_t channelId) = 0;
            virtual DgError Deinit(void) = 0;
            virtual DgError DecodeVideo(MatrixSP input,
                                        MatrixType outputType,
                                        DecVideoCallback cb,
                                        const VideoDecParamSP &vdecParam,
                                        bool bEos)  = 0;
            virtual std::string get_run_status() = 0;
        };
        using AclVDecProcBaseSP = std::shared_ptr<AclVDecProcBase>;

        class AclVencProcBase {
        public:
            AclVencProcBase() = default;
            virtual ~AclVencProcBase() = default;
            virtual DgError Init(const aclrtStream &stream) = 0;
            virtual DgError CreateVenc(MatrixType from,
                                       MatrixType to,
                                       const VencBuffer::VencParam &param,
                                       VencBuffer::VencCallback cb) = 0;
            virtual DgError DestroyVenc(void) = 0;
            virtual DgError EncodeVideo(MatrixSP input, int kIntv, bool forceIFrame, float resizeRatio, bool videoEos) = 0;
        };
        using AclVencProcBaseSP = std::shared_ptr<AclVencProcBase>;
    }  // namespace acl
}  // namespace vega
#endif

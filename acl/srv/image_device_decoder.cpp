#include <sys/time.h>
#include <sys/prctl.h>
#include "image_device_decoder.h"
#include "common/utils/jpeg_exif.h"
#include "common/utils/jpegd_check/JpegdCheck.h"
#define ACL_STRIDE_WIDTH 64
#define ACL_STRIDE_HEIGHT 16
namespace vega {
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        AclImageDecoder::AclImageDecoder() {
            for(int i=0;i<8;i++) {
                std::shared_ptr<JPG_DEC_CHANNEL> jpgDecChannl=std::shared_ptr<JPG_DEC_CHANNEL>(new JPG_DEC_CHANNEL,
                    [=](JPG_DEC_CHANNEL *soure){
                        if(soure->desc){
                            acldvppDestroyChannel(soure->desc);
                            acldvppDestroyChannelDesc(soure->desc);
                        }
                        if(soure->stream){
                            aclrtDestroyStream(soure->stream);
                        }
                        delete soure;
                });
                jpgDecChannlQ_.push(jpgDecChannl);
                jpgDecChannl->desc=nullptr; 
                jpgDecChannl->stream=nullptr; 
                jpgDecChannl->desc= acldvppCreateChannelDesc();
                if (jpgDecChannl->desc == nullptr) {
                    LOG(ERROR) << "Failed to create channel desc";
                    continue ;
                }
#if CANN50
                acldvppSetChannelDescMode(jpgDecChannl->desc,DVPP_CHNMODE_JPEGD);
#endif
                auto ret = acldvppCreateChannel(jpgDecChannl->desc);
                if (ret != ACL_SUCCESS) {
                    LOG(ERROR) << "Failed to create dvpp channel, ret = " << ret;
                    acldvppDestroyChannelDesc(jpgDecChannl->desc);
                    jpgDecChannl->desc=nullptr;
                    continue ;
                }
                ret = aclrtCreateStream(&jpgDecChannl->stream);
                if (ret != ACL_SUCCESS) {
                    LOG(ERROR) << "Failed to create stream, ret = " << ret;
                    jpgDecChannl->stream=nullptr;
                }

            }
        }
        AclImageDecoder::~AclImageDecoder() {
        }
        DgError AclImageDecoder::JpegDecode(const MatrixSP &input, MatrixSP &output, MatrixType outputType) {
            if (nullptr == input) {
                LOG(ERROR) << "Invalid input matrix";
                return DG_ERR_INVALID_PARAM;
            }
            std::shared_ptr<JPG_DEC_CHANNEL> jpgDecChannl=jpgDecChannlQ_.pop();
            std::shared_ptr<aclrtStream> streamPtr=std::shared_ptr<aclrtStream>(&jpgDecChannl->stream,[=](void *p){
                jpgDecChannlQ_.push(jpgDecChannl);
            });
            if(!jpgDecChannl->desc){
                LOG(ERROR)<<"JpegDecode channel desc is nullptr!";
                return DG_ERR_INIT_FAIL;
            }
            if(!jpgDecChannl->stream){
                LOG(ERROR)<<"Fail to aclrtCreateStream in initiation!";
                return DG_ERR_INIT_FAIL;
            }
            auto size = input->memory()->size();
            auto data = input->memory()->blob();
            if (size <= 0  || data == nullptr) {
                LOG(ERROR) << "Invalid input image, size: " << size;
                return DG_ERR_INVALID_PARAM;
            }

            if (input->type() != MatrixType::JPEG) {
                LOG(ERROR) << "Invalid input image type:" << input->typestr() << ", not support !!!";
                return DG_ERR_INVALID_PARAM;
            }

            if (outputType != MatrixType::NV12) {
                LOG(ERROR) << "Invalid output image type:" << matrix_type_str(outputType) << ", not support !!!";
                return DG_ERR_INVALID_PARAM;
            }

            // Check jpeg 'Encoding Process'
            JPEG_CHECK::JpegdCheck jpegdCheck;
            struct JPEG_CHECK::jpeg_decompress_struct libjpegHandler;
            if (!jpegdCheck.IsCaseValid(data, size, libjpegHandler)) {
                LOG(ERROR) << "jpegdCheck IsCaseValid = false";
                return DG_ERR_FULL;
            }

            auto dgErr = CombineJpegDecProcess(input, output, jpgDecChannl->desc,jpgDecChannl->stream);
            return dgErr;
        }

        int AclImageDecoder::DvppJpegDec(DvppDataInfoSP &input, DvppDataInfoSP &output, acldvppChannelDesc *desc,
            const aclrtStream &aclStream) {
            // create output pic desc and set  
            auto outputDescSP = std::shared_ptr<acldvppPicDesc>(acldvppCreatePicDesc(),
                    [](acldvppPicDesc * const picDesc) {
                        if (picDesc != nullptr) {
                            acldvppDestroyPicDesc(picDesc);
                        }
                    });
            acldvppPicDesc *outputDesc = outputDescSP.get();
            if (outputDesc == nullptr) {
                LOG(ERROR) << "Failed to acldvppCreatePicDesc";
                return -1;
            }

            (void)acldvppSetPicDescData(outputDesc, output->data);
            (void)acldvppSetPicDescFormat(outputDesc, output->format);
            (void)acldvppSetPicDescSize(outputDesc, output->dataSize);
            acldvppSetPicDescWidth(outputDesc, output->width);
            acldvppSetPicDescHeight(outputDesc, output->height);
            acldvppSetPicDescWidthStride(outputDesc, output->strideW);
            acldvppSetPicDescHeightStride(outputDesc, output->strideH);

            auto ret = acldvppJpegDecodeAsync(desc, input->data, input->dataSize, outputDesc, aclStream);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to decode jpeg, ret = " << ret;
                return -1;
            }

            ret = aclrtSynchronizeStream(aclStream);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to synchronize stream, ret = " << ret;
                return -1;
            }

            ret = acldvppGetPicDescRetCode(outputDesc);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to get image info, ret = " << ret;
                return -1;
            }
            output->width = acldvppGetPicDescWidth(outputDesc);
            output->height = acldvppGetPicDescHeight(outputDesc);
            output->strideW = acldvppGetPicDescWidthStride(outputDesc);
            output->strideH = acldvppGetPicDescHeightStride(outputDesc);
            output->dataSize = acldvppGetPicDescSize(outputDesc);
            return 0;
        }

        DgError AclImageDecoder::CombineJpegDecProcess(const MatrixSP &input, MatrixSP &output,acldvppChannelDesc *desc,
            const aclrtStream &aclStream) {
            uint32_t width = 0;
            uint32_t height = 0;
            int32_t components = 0;
            uint32_t size = input->memory()->size();
            void *data = (void*)input->memory()->blob();

            aclError ret = acldvppJpegGetImageInfo(data, size, &width, &height, &components);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to get image info of jpeg, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }

            if (width > MAX_JPEGD_WIDTH || width < MIN_JPEGD_WIDTH) {
                LOG(ERROR) << "Input width is invalid, not in [" << MIN_JPEGD_WIDTH << ", " << MAX_JPEGD_WIDTH << "].";
                return DG_ERR_VDEC_FAIL;
            }

            if (height > MAX_JPEGD_HEIGHT || height < MIN_JPEGD_HEIGHT) {
                LOG(ERROR) << "Input height is invalid, not in [" << MIN_JPEGD_HEIGHT << ", " << MAX_JPEGD_HEIGHT << "].";
                return DG_ERR_VDEC_FAIL;
            }

            DvppDataInfoSP dvppInputData = std::make_shared<DvppDataInfo>();
            dvppInputData->width  = width;
            dvppInputData->height = height;
            dvppInputData->format = format_;

            // Get the buffer size of decode output according to the input data and output format
            uint32_t outBuffSize = 0;
            ret = acldvppJpegPredictDecSize(data, size, format_, &outBuffSize);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to predict decode size of jpeg image, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }

            auto inMemDev = std::make_shared<AclMemDvpp>();
            ret=inMemDev->fromHost(input->memory());
            if(ret!=DG_OK){
                LOG(ERROR)<<"Fail to acl mem copy from host!";
                return DG_ERR_VDEC_FAIL;
            }
            dvppInputData->data = inMemDev->blob();
            dvppInputData->dataSize = size;

            DvppDataInfoSP dvppOutpuData = std::make_shared<DvppDataInfo>();
            dvppOutpuData->format = format_;
            sdk_config::Platform platform=vegaGetPlatform();
            if(platform != sdk_config::acl){//acl710
                dvppOutpuData->width = width;
                dvppOutpuData->height = height;
                dvppOutpuData->strideW = (width+ACL_STRIDE_WIDTH-1)/ACL_STRIDE_WIDTH*ACL_STRIDE_WIDTH;
                dvppOutpuData->strideH = (height+ACL_STRIDE_HEIGHT-1)/ACL_STRIDE_HEIGHT*ACL_STRIDE_HEIGHT;
                dvppOutpuData->dataSize = dvppOutpuData->strideW*dvppOutpuData->strideH*3/2;//outBuffSize;
            }else {//acl310
                dvppOutpuData->dataSize = outBuffSize;
            }
            auto mem = std::make_shared<AclMemDvpp>();
            auto err = mem->create(dvppOutpuData->dataSize, 1);
            if (err != DG_OK) {
                LOG(ERROR) << "Create decode image nv12 mem failed";
                return DG_ERR_VDEC_FAIL;
            }
            dvppOutpuData->data=mem->blob();

            if (DvppJpegDec(dvppInputData, dvppOutpuData,desc,aclStream) < 0) {
                dvppInputData->data = nullptr;
                LOG(ERROR) <<"Fail to DvppJpegDec!";
                return DG_ERR_VDEC_FAIL;
            }

            if (dvppOutpuData->width < 1 || dvppOutpuData->height < 1) {
                LOG(ERROR) << "dvppOutpuData error, width: " << dvppOutpuData->width 
                           << " , height: " << dvppOutpuData->height;
                return DG_ERR_VDEC_FAIL;
            }

            dvppInputData->data = nullptr;

            auto dst = std::make_shared<AclImage>();
            err = dst->create(MatrixType::NV12,cv::Size(dvppOutpuData->width, dvppOutpuData->height), mem, 1,dvppOutpuData->strideW,dvppOutpuData->strideH);
            if (err != DG_OK) {
                LOG(ERROR) << "Failed to create output frame for image";
                return DG_ERR_VDEC_FAIL;
            }
            if(dst->reqMemSize()>(int)dvppOutpuData->dataSize){
                LOG(ERROR) << "Fail because dataSize "<<dvppOutpuData->dataSize<<" < reqMemSize "<<dst->reqMemSize()<<" !";
            }
            dst->setStreamId(input->streamId());
            output = dst;
            return DG_OK;
        }
    }
}

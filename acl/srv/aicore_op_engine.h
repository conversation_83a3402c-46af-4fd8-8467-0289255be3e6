/*
 * Copyright(C) 2022. Deepglint Technologies Co.,Ltd. All rights reserved.
 *  by <PERSON><PERSON><PERSON><PERSON><PERSON>@deepglint.com
 */
#ifndef VEGA_AICORE_OP_EXECUTE_H_
#define VEGA_AICORE_OP_EXECUTE_H_
#include <cstdio>
#include <vector>
#include "vega_sdk_config.h"
#include "acl/acl.h"
#include "utils/acl_memory.h"
#include "image_engine.h"
#include "utils/inc/operator_desc.h"
#include "utils/acl_image.h"
#include "acl_op_engine.h"
namespace vega {
namespace acl {
extern sdk_config::Platform vegaGetPlatform();
class AicoreOp{
public:
    AicoreOp() {
    }
    ~AicoreOp() {
    }
    DgError Init(){
         std::string model_path = sdk_config::get_cfg<std::string>("aicpu_model_path","",sdk_config::acl);
        LoadModel(model_path);
        return DG_OK;
    }
    DgError LoadModel(std::string model_path);
    OperatorDesc CreateOpResizeBilinearV2Desc(cv::Size inputSize,cv::Size outputSize);
    OperatorDesc CreateOpResizeDesc(cv::Size inputSize,cv::Size outputSize);
    DgError ResizeInfer(cv::Size inputSize,cv::Size outputSize,void *inputBuf,int inputBufSize,
        void *outputBuf,int32_t outputBufSize,aclrtStream stream);
    DgError aippInfer(aclmdlAIPP *aippParmsSet,uint32_t input_size, void *input_data,uint32_t output_size, void *output_data,
        aclrtStream stream);
    DgError cropResize(MatrixSP &input,MatrixSP &output,ProcessMatrixParam &param,aclrtStream stream);
private:
    void FreeAclDataBuf(std::vector<aclDataBuffer *> vBuffer0,std::vector<aclDataBuffer *> vBuffer1);
    void SetAippInfo(aclAippInfo *aippInfo);
    aclmdlDataset *CreateAndFillDataset(void * bufs, size_t sizes);
    void DestroyDataset(aclmdlDataset *dataset);
    DgError aippCrop(AclImageSP input, AclMemDvppSP &memBgrOut,ProcessMatrixParam param,aclrtStream stream);
    DgError aiCpuPast(AclImageSP input,AclImageSP &output,aclrtStream stream);
    DgError SetAippInfo(aclmdlAIPP *mdlAipp,int32_t srcImageSizeW, int32_t srcImageSizeH,int32_t cropStartPosW,
    int32_t cropStartPosH,int32_t cropSizeW, int32_t cropSizeH,int32_t paddingSizeTop, int32_t paddingSizeBottom,
    int32_t paddingSizeLeft, int32_t paddingSizeRight,int16_t dtcPixelMeanChn0,int16_t dtcPixelMeanChn1,
    int16_t dtcPixelMeanChn2,float scale);
    std::string opResizeBilinearV2_ = "ResizeBilinearV2";
    std::string opResize_ = "Resize";
    uint32_t aipp_modelId_=0;
    size_t aipp_index_=0;
    const u_int32_t max_width_=2560;
    const u_int32_t max_height_=2560;
};
class AicoreOpEngine : public ImageEngine {
public:
    AicoreOpEngine() : ImageEngine(0, "AicoreOp") {
        for(int i=0;i<1;i++) {
            std::shared_ptr<AicoreOp> aicoreOp = std::make_shared<AicoreOp>();
            aicoreOp->Init();
            AicoreOpQ_.push(aicoreOp);
        }
    }
    ~AicoreOpEngine() {
    }
    DgError process(VegaMatrixCmd cmd,
                    std::vector<bool> &marks,
                    MatrixSPV &inputs,
                    MatrixSPV &outputs,
                    VegaStream stream,
                    ProcessMatrixParam &param) override {
        if (cmd != VegaMatrixCmd::CROP_RESIZE) {
            return DG_ERR_NOT_SUPPORTED;
        }
        DgError ret = DG_OK;
        int cnt = 0;
        for (auto i = 0u; i < marks.size(); i++) {
            if (!marks[i])
                cnt++;
        }
        inc(cnt);
         std::shared_ptr<AicoreOp> aicoreOp = AicoreOpQ_.pop();
        for (auto i = 0u; i < inputs.size(); i++) {
            DgError err;
            if (marks[i]) {
                continue;
            }
            switch (cmd) {
            case VegaMatrixCmd::CROP_RESIZE: {
                err = aicoreOp->cropResize(inputs[i], outputs[i], param,aclrtStream(stream));
                break;
            }
            default:
                err = DG_ERR_NOT_SUPPORTED;
                break;
            }
            cnt--;
            dec();

            if (err == DG_OK) {
                marks[i] = true;
                // todo: copy host to device
            } else if (ret == DG_OK) {
                ret = err;
            }
        }
        dec(cnt);
        AicoreOpQ_.push(aicoreOp);
        return ret;
    }
    BlockQueue<std::shared_ptr<AicoreOp>> AicoreOpQ_;
};
}  // namespace acl
}  // namespace vega
#endif
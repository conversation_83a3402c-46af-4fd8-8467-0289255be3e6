
#include "vega_stream.h"
#include "acl/acl.h"
#if CANN50 && ACL_MEDIA_V2
# include "../v2/srv/dvpp_engine.h"
#endif
# include "dvpp_engine.h"
#include <sys/types.h>
#include "image_engine.h"
#include "utils/acl_memory.h"
#if CANN50
#include "acl_op_engine.h"
#include "aicore_op_engine.h"
#endif
#include "utils/FramebufferPool.h"
namespace vega {
#ifdef USE_PLATFROM_STREM_IMPL
    static aclrtContext acl_context_;
    static  int acl_device_id_;
    extern  DgError vegaDestroyImageEngine();
    static sdk_config::Platform platform_=sdk_config::acl;
    static bool is_acl_soc_=false;
    long long  acl::AclMemDvpp::max_device_mem_;
    std::atomic<long long > acl::AclMemDvpp::total_dvpp_alloc_size_;
    std::atomic<long long > acl::AclMemDvpp::total_normal_alloc_size_;
    sdk_config::Platform vegaGetPlatform(){
        return platform_;
    }
    bool isAclSoc(){
        return is_acl_soc_;
    }
    DgError vegaPlatformInit(const std::string &cfgFile,std::string str_dev_id,std::string &soc_name,
    std::string &device_name,int &engineThreadNum){
        int device_id = std::stoi(str_dev_id);
        if(device_id<0){
            LOG(ERROR) << "device_id " << device_id<<" error!";
            return DG_ERR_HW_FAILURE;
        }
        acl_device_id_=device_id;
        const char *aclConfigPath =nullptr;
        std::string  path= sdk_config::get_cfg<std::string>("acl_config_path","", sdk_config::acl);
        if(!path.empty()){
            aclConfigPath=path.c_str();
            LOG(INFO)<<"acl_config_path: "<<path;
        }
        aclError ret = aclInit(aclConfigPath);
        if (ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "aclInit failed,  errorCode is " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
        ret = aclrtCreateContext(&acl_context_, device_id);
        if (ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "aclrtCreateContext,  errorCode is " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
        soc_name="A310";
#if CANN50
        std::string acl_soc_name=aclrtGetSocName();
        LOG(ERROR)<<"acl_soc_name "<<acl_soc_name;
        if(acl_soc_name.compare("Ascend710")==0 || acl_soc_name.compare("Ascend310P3")==0
        || acl_soc_name.compare("Ascend310P1")==0){
            auto forceV1 =(bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false , sdk_config::acl);
            if(!forceV1){
                auto ret = hi_mpi_sys_init();
                if (ret != HI_SUCCESS) {
                    LOG(FATAL) << "hi_mpi_sys_init failed.  " << std::hex << ret;
                }
            }
            soc_name="A710";
            size_t free; 
            size_t total;
            aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);
            LOG(ERROR)<<"A710 total memory: "<<total<<",free memory "<<free;
            if(total>35000000000){
                device_name="Atlas300VPro";
                platform_=sdk_config::Atlas300VPro;
            }else {
                device_name="Atlas300IPro";
                platform_=sdk_config::Atlas300IPro;
            }
            engineThreadNum= sdk_config::get_cfg<int>("Engine_Thread_Num",10, platform_);
            LOG(ERROR)<<device_name<<" total memory: "<<total<<".";
            if(acl_soc_name.compare("Ascend310P1")==0){
                is_acl_soc_=true;
            }else{
                is_acl_soc_=false;
            }
        }
#endif
        LOG(ERROR)<<"acl Soc: "<<soc_name;
        acl::AclMemDvpp::max_device_mem_ = sdk_config::get_cfg<long long >("max_device_mem_allocate",2000000000, platform_);
        
        LOG(ERROR)<<"max_device_mem_allocate is "<<acl::AclMemDvpp::max_device_mem_;
#if CANN50
        int use_aicpu_op_engine=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
        if(use_aicpu_op_engine){
            acl::AclOpExecute::aicpu_engine_init();
        }
#endif
        return DG_OK;
    }
    DgError vegaPlatformUnInit(){
         VegaMatrixPool::destroy();
        acl::AclFramePoolMap::destroy();
        vegaDestroyImageEngine();
#if CANN50
        acl::AclOpExecute::clear();
#endif
        std::string acl_soc_name=aclrtGetSocName();
        if(acl_soc_name.compare("Ascend710")==0 || acl_soc_name.compare("Ascend310P3")==0
        || acl_soc_name.compare("Ascend310P1")==0){
            auto forceV1 =(bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false , sdk_config::acl);
            if(!forceV1){
                hi_mpi_sys_exit();
            }
        }
        auto ret =aclrtDestroyContext(acl_context_);
        if(ret!=ACL_ERROR_NONE){
            LOG(ERROR) << "aclrtDestroyContext,  errorCode is" << static_cast<int32_t>(ret);
        }
        ret=aclFinalize();
        if(ret!=ACL_ERROR_NONE) {
            LOG(ERROR) << "aclFinalize,  errorCode is" << static_cast<int32_t>(ret);
        }
        LOG(ERROR)<<"vegaPlatForm uninitialize sucessfully!";
        return DG_OK;
    }
    DgError vegaGetDevice(int &device) {
        device=acl_device_id_;
        auto ret=aclrtSetCurrentContext(acl_context_);
        if(ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "aclrtSetCurrentContext failed, errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
        return DG_OK;
    }
    DgError vegaSetDevice(int device) {
        VEGA_UNUSED(device);
        auto ret=aclrtSetCurrentContext(acl_context_);
        if(ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "aclrtSetCurrentContext failed, errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
        return DG_OK;
    }
    DgError vegaCreateStream(VegaStream *stream) {
        auto ret = aclrtCreateStream((aclrtStream *)stream);
        if(ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "Fail to create stream, errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_SERVICE_NOT_AVAILABLE;
        }

        return DG_OK;
    }
    DgError vegaSetCurrentContext(){
        auto ret=aclrtSetCurrentContext(acl_context_);
        if(ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "aclrtSetCurrentContext failed, errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
        return DG_OK;
    }
    DgError vegaDestroyStream(VegaStream stream) {
        if(stream != VEGA_INVALID_STREAM) {
            auto ret = aclrtDestroyStream((aclrtStream)stream);
            if(ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to aclrtDestroyStream, errorCode is  " << static_cast<int32_t>(ret);
                return DG_ERR_INVALID_ADDRESS;
            }
        }
        return DG_OK;
    }
    DgError vegaStreamAddCallback(VegaStreamCallback callback, VegaStream stream, void *userdata) {
        if(stream == VEGA_INVALID_STREAM) {
            callback(userdata);
            return DG_OK;
        } else {
            auto tid=pthread_self();
            auto ret = aclrtSubscribeReport(tid, (aclrtStream)stream);
            if(ret != ACL_SUCCESS) {
                LOG(ERROR) << "aclrtSubscribeReport fail: " << ret;
                return DG_ERR_STREAM_FAILURE;
            }
            ret = aclrtLaunchCallback( (aclrtCallback)callback, userdata, ACL_CALLBACK_BLOCK,(aclrtStream)stream);
            if(ret != ACL_SUCCESS) {
                LOG(ERROR) << "Add callback fail: " << ret;
                 return DG_ERR_STREAM_FAILURE;
            }
        }
        return DG_OK;
    }
    DgError vegaStreamSync(VegaStream stream) {
        if(stream == VEGA_INVALID_STREAM) {
            LOG(ERROR) << "Sync invalid stream";
            return DG_ERR_INVALID_PARAM;
        }

        auto ret = aclrtSynchronizeStream((aclrtStream)stream);
        if(ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "Sync stream fail, errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_STREAM_FAILURE;
        }

        return DG_OK;
    }
    DgError vegaMemcpy(void *dst, void *src, int bytes, int option, VegaStream stream) {
        if(dst == nullptr || src == nullptr) {
            LOG(ERROR) << "null ptr to copy";
            return DG_ERR_INVALID_PARAM;
        }
        if(bytes < 0) {
            LOG(ERROR) << "Copy bytes: " << bytes;
            return DG_ERR_INVALID_PARAM;
        }
        if(bytes == 0) return DG_OK;

        aclrtMemcpyKind kind;
        if(option == VEGA_MEMCPY_DEVICE_TO_HOST) {
            kind = ACL_MEMCPY_DEVICE_TO_HOST;
        } else if(option == VEGA_MEMCPY_DEVICE_TO_DEVICE) {
            kind = ACL_MEMCPY_DEVICE_TO_DEVICE;
        } else if(option == VEGA_MEMCPY_HOST_TO_DEVICE) {
            kind = ACL_MEMCPY_HOST_TO_DEVICE;
        } else if(option == VEGA_MEMCPY_HOST_TO_HOST){
            kind = ACL_MEMCPY_HOST_TO_HOST;
        } else {
            LOG(ERROR)<<"copy option  is erro, option "<<option;
            return DG_ERR_INVALID_PARAM;
        }
        auto ret= acl::AclMemDvpp::AclMemcpy(dst, bytes, src, bytes, kind);
        if(ret != DG_OK){
            LOG(ERROR)<<"Fail to AclMemcpy!";
            return DG_ERR_HW_FAILURE;
        }
        return DG_OK;
    }
#endif
    DgError vegaGetDeviceMarginMemSize(long long &length) {
        size_t free;
        size_t total;
        vegaSetCurrentContext();
        auto ret = aclrtGetMemInfo(ACL_DDR_MEM, &free, &total);
        if (ret != ACL_SUCCESS) {
            LOG(ERROR) << "Fail to aclrtGetMemInfo,error code is " << ret;
            return DG_ERR_HW_FAILURE;
        }
        length = free >> 20;
        return DG_OK;
    }
    DgError vegaCreateImageEnginePlatform(std::shared_ptr<ImageEngineMgr> mgr) {
        int use_dvpp_engine = sdk_config::get_cfg<int>("use_dvpp_engine", 1, sdk_config::acl);
        if (use_dvpp_engine) {
            LOG(ERROR) << "use_dvpp_engine enable";
#if CANN50 && ACL_MEDIA_V2
            std::string acl_soc_name = "Ascend310";
            bool force_dvpp_v1 = (bool)sdk_config::get_cfg<bool>("force_dvpp_v1", false, sdk_config::acl);
            acl_soc_name = aclrtGetSocName();
            if ((acl_soc_name.compare("Ascend710") == 0 || acl_soc_name.compare("Ascend310P3") == 0
                 || acl_soc_name.compare("Ascend310P1") == 0)
                && !force_dvpp_v1) {
                auto aclDvppEngine = std::make_shared<acl::DvppEngineV2>();
                mgr->push(aclDvppEngine, 0);
            } else {
                auto aclDvppEngine = std::make_shared<acl::DvppEngine>();
                mgr->push(aclDvppEngine, 0);
            }
#else
            auto aclDvppEngine = std::make_shared<acl::DvppEngine>();
            mgr->push(aclDvppEngine, 0);
#endif
        } else {
            LOG(ERROR) << "use_dvpp_engine disable";
        }

#if CANN50
        if(!isAclSoc()){
            int use_aicpu_op_engine=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
            if(use_aicpu_op_engine){
                LOG(ERROR)<<"use_aicpu_op_engine: enable";
                auto aclOpEngine = std::make_shared<acl::AclOpEngine>();
                mgr->push(aclOpEngine, 1);
            }
            int use_aicore_op_engine=sdk_config::get_cfg<int>("use_aicore_op_engine", 0, sdk_config::acl);
            if(use_aicore_op_engine){
                LOG(ERROR)<<"use_aicore_op_engine: enable";
                auto aicoreOpEngine = std::make_shared<acl::AicoreOpEngine>();
                mgr->push(aicoreOpEngine, 0);
            }
        }
#endif
        return DG_OK;;
    }
#if CANN50
    BlockQueue<std::shared_ptr<acl::AclOpExecute>> acl::AclOpExecute::AclOpExecuteQ_;
#endif
}

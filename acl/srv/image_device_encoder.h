#ifndef VEGA_ACL_IMAGE_DEVICE_ENCODER_H
#define VEGA_ACL_IMAGE_DEVICE_ENCODER_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "dvpp_common.h"
#include "rpc/rpc_processor.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"

namespace vega {
    namespace acl {

        class AclImageEncoder: public AclImageEncoderBase{
        public:
            AclImageEncoder();
            ~AclImageEncoder();
            AclImageEncoder &operator=(const AclImageEncoder &) = delete;

            DgError JpegEncode(MatrixSP &input, MatrixSP &output, int quality);

        protected:
            int CropImage(const DvppDataInfo &inData, DvppDataInfo &outData,AclMemDvppSP &cropDataSP, cv::Rect &rect, \
                          const aclrtStream &aclStream, acldvppChannelDesc *dvppChnDesc);

            int VpcCrop(const DvppDataInfo &inData, DvppDataInfo &outData, cv::Rect &rect, \
                        const aclrtStream &aclStream, acldvppChannelDesc *dvppChnDesc);

            void SetDvppPicDesc(const DvppDataInfo &info, acldvppPicDesc *picDesc);

            DgError CombineJpegeProcess(acldvppChannelDesc *dvppDesc,
                                        acldvppChannelDesc *jpgDesc, \
                                        const aclrtStream &aclStream, \
                                        const MatrixSP &input, \
                                        MatrixSP &output, int quality);
        private:
            acldvppPixelFormat format_ = PIXEL_FORMAT_YUV_SEMIPLANAR_420;
            typedef struct {
                acldvppChannelDesc *dvppDesc;
                acldvppChannelDesc *jpgDesc;
                aclrtStream stream;
            }JPG_ENC_CHANNEL;
            BlockQueue<std::shared_ptr<JPG_ENC_CHANNEL>> jpgEncChannlQ_;
        };
    }
}















#endif
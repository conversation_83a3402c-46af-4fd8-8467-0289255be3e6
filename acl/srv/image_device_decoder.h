#ifndef VEGA_ACL_IMAGE_DEVICE_DECODER_H
#define VEGA_ACL_IMAGE_DEVICE_DECODER_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "dvpp_common.h"
#include "rpc/rpc_processor.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"

namespace vega {
        namespace acl {
        
        class AclImageDecoder : public AclImageDecoderBase{
        public:
            AclImageDecoder();
            ~AclImageDecoder();
            AclImageDecoder &operator=(const AclImageDecoder &) = delete;

            DgError JpegDecode(const MatrixSP &input, MatrixSP &output, MatrixType outputType);

        protected:
            int DvppJpegDec(DvppDataInfoSP &input, DvppDataInfoSP &output, acldvppChannelDesc *desc,
                const aclrtStream &aclStream);
            DgError CombineJpegDecProcess(const MatrixSP &input, MatrixSP &output,acldvppChannelDesc *desc,
             const aclrtStream &aclStream);

        private:
            typedef struct {
                acldvppChannelDesc *desc;
                aclrtStream stream;
            }JPG_DEC_CHANNEL;
            acldvppPixelFormat format_ = PIXEL_FORMAT_YUV_SEMIPLANAR_420;
            BlockQueue<std::shared_ptr<JPG_DEC_CHANNEL>> jpgDecChannlQ_;
        };
    }
}

#endif

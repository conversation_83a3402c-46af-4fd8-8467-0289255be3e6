#ifndef VEGA_ACL_VIDEO_DEVICE_DECODER_H
#define VEGA_ACL_VIDEO_DEVICE_DECODER_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <atomic>
#include "glog/logging.h"
#include "error.h"
#include "dg_base_types.h"
#include "vega_matrix.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "dvpp_common.h"
#include "common/Platform/processor/video_decoder_buffer.h"
#include "utils/FramebufferPool.h"
#include "common/Platform/utils/mem_heap.h"
namespace vega {
    namespace acl {

        class AclVDecProc : public AclVDecProcBase{
#define ERR_DECODE_NOPIC 0x20000 //隔行码流场景下使用，隔行码流每帧发送两场，解码时其中一块无图像输出，属于正常现象，会返回该错误码。
        public:
            typedef struct tagVdecConfig {
                cv::Size size;
                cv::Size stride;
                uint32_t dataSize;
                acldvppStreamFormat inFormat = H264_MAIN_LEVEL;
                acldvppPixelFormat outFormat = PIXEL_FORMAT_YUV_SEMIPLANAR_420;
                uint32_t channelId = 0;
                pthread_t threadId = 0;
                DiscardInterval discardInterval ;
                aclvdecCallback callback = {0};
                bool bSet=false;
                bool fast_mode=false;
            } VdecConfig;

            typedef struct tagPackCbSeting {
                FRAME_DISCARD_MODE bIgnore = KEEP_FRAME;
                bool memFull = false;
                DecVideoCallback cb = nullptr;
                void *parent = nullptr;
                uint64 sn = 0;
                std::shared_ptr<AclMemDvpp> int_mem = nullptr;
                std::shared_ptr<AclMemDvpp> out_mem = nullptr;
                std::shared_ptr<acldvppStreamDesc> streamInputDesc = nullptr;
                std::shared_ptr<acldvppPicDesc> picOutputDesc = nullptr;
            } PackCbSeting;

            AclVDecProc() {
                repThreadId_=0;
                bRegAclFramePoolMap=false;
                last_ch_erro_=0;
                creat_dvpp_stream_num_.store(0);
            }
            ~AclVDecProc() {
                Deinit();
            }

            AclVDecProc &operator=(const AclVDecProc &) = delete;

            DgError Init(const aclrtStream &stream, uint32_t channelId);
            DgError Deinit(void);
            DgError DecodeVideo(MatrixSP input, MatrixType outputType, DecVideoCallback cb, \
                                const VideoDecParamSP &vdecParam, bool bEos);
            std::string get_run_status(){ return run_status_;};

        protected:
            static void vdecCallback(acldvppStreamDesc *input, acldvppPicDesc *output, void *userdata);
            static void *AclReportThread_(void *arg);
            void *AclReportThread(void);

            DgError InitVdec(const VdecConfig &config);
            DgError DeInitVdec(void);

            DgError SetDvppPicDescData(const DvppDataInfo &info, acldvppPicDesc &picDesc);
            acldvppPicDesc *CreateOutputPicDesc(uint8_t *picOutBufferDev,unsigned int size);

            DgError CombineVdecProcess(const DvppDataInfoSP &info, PackCbSeting *packCbSeting);

            DgError VdecSendEosFrame(void);

            void DecodeDone(DgError error,cv::Size size,cv::Size strideSize,MatrixType type, const PackCbSeting *packCbSeting);

        private:
            bool bQuit_ = true;
            uint32_t channelId_ = 0;
            uint32_t videoWidth_ = 1280;
            uint32_t videoHeight_ = 720;
            StreamId streamid_ = 0;
            pthread_t repThreadId_ = 0;
            VdecConfig vdecConf_;
            const unsigned int maxResolution_=4096;
            const unsigned int MaxOutMemSize_=4096*4096*3/2;
            aclrtStream aclStream_ = nullptr;
            acldvppStreamFormat videoFormat_ = H264_MAIN_LEVEL;
            acldvppPixelFormat outFormat_ = PIXEL_FORMAT_YUV_SEMIPLANAR_420;
            aclvdecChannelDesc *vdecChannelDesc_ = nullptr;
            uint64 frame_count_=0;
            bool bRegAclFramePoolMap=false;
            std::atomic<unsigned int> creat_dvpp_stream_num_;
            uint64 packet_sn_=0;
            unsigned int last_ch_erro_=0;
            std::string run_status_;
            std::shared_ptr<MemHeap> videoInDeviceMemHeap_;
        };
    }
}
#endif // VEGA_ACL_VIDEO_DEVICE_DECODER_H

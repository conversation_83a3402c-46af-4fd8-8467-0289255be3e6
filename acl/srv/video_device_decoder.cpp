#include "video_device_decoder.h"
#include <sys/time.h>
#include <sys/prctl.h>
#include <stdio.h>
namespace vega {
    namespace acl {
        class AclChannelManage{
                enum aclVecChannelStatus{
                free,
                occupy,
                bad
            };
            typedef struct 
            {
                aclVecChannelStatus status;
                unsigned int last_erro;
            }AclVedcChannel;
        public:
            AclChannelManage(){
                bInit_=false;
            }
            ~AclChannelManage(){
            }
            void init(){
                channel_.resize(32);
#if CANN50
                const char *cName=aclrtGetSocName();
                if(cName==nullptr){
                    LOG(ERROR)<<"Fail to aclrtGetSocName";
                }else{
                    std::string acl_soc_name=aclrtGetSocName();
                    if(acl_soc_name.compare("Ascend710")==0 || acl_soc_name.compare("Ascend310P3")==0
                    || acl_soc_name.compare("Ascend310P1")==0){
                        channel_.resize(256);
                    }
                }
#endif
                for(unsigned int i=0;i<channel_.size();i++){
                    channel_[i].status=free;
                    channel_[i].last_erro=0;
                }
                bInit_=true;
            }
            int GetChannel(){
                std::unique_lock<std::mutex> mlock(lck_);
                if(!bInit_)
                    init();
                int ch=-1;
                 for(unsigned int i=0;i<channel_.size();i++){
                    if(channel_[i].status==free){
                        ch=i;
                        channel_[i].status=occupy;
                        channel_[i].last_erro=0;
                        break;
                    }
                }
                if(ch==-1){
                    std::stringstream log;
                    log<<"acl video decoder channel hasn't free;";
                    for(unsigned int i=0;i<channel_.size();i++){
                        log<<"\nch"<<i;
                        if(channel_[i].status==free){
                            log<<",free";
                        }else if(channel_[i].status==occupy){
                            log<<",in useing";
                        }else if(channel_[i].status==bad){
                            log<<", bad,last erro "<<channel_[i].last_erro;
                        }
                    }
                    LOG(FATAL)<<log.str();
                }
                return ch;
            }

            void PushChannel(int id,unsigned int last_erro){
                std::unique_lock<std::mutex> mlock(lck_);
                if(!bInit_)
                    return;
                if(id>=0&&(unsigned int)id<channel_.size()){
                        if(last_erro!=0){
                            channel_[id].status=bad;
                            channel_[id].last_erro=last_erro;
                            LOG(ERROR)<<"vdec channel "<<id <<" is bad ,erro "<<last_erro;
                        }else {
                            channel_[id].status=free;
                        }
                }else{
                    LOG(ERROR)<<"invalid channel "<<id;
                }
            }
        private:
            std::mutex lck_;
            bool bInit_=false;
            std::vector<AclVedcChannel> channel_; 
        };
        static AclChannelManage aclChannelManage;
        DgError AclVDecProc::Init(const aclrtStream &stream, uint32_t channelId) {
            aclStream_ = stream;
            int ch_id=aclChannelManage.GetChannel();
            if(ch_id<0){
                LOG(FATAL)<<"invalid channel "<<ch_id;
                return DG_ERR_INIT_FAIL;
            }
            channelId_=ch_id;
            bQuit_ = false;
            vdecConf_.bSet=false;
            frame_count_=0;
            // create acl report threadId
            if (pthread_create(&repThreadId_, nullptr, AclReportThread_, this) != 0) {
                LOG(ERROR) << "Failed to create ACL report thread !!!";
                repThreadId_=0;
                aclChannelManage.PushChannel(channelId_,last_ch_erro_);
                return DG_ERR_INIT_FAIL;
            }
            (void)aclrtSubscribeReport(static_cast<uint64_t>(repThreadId_), aclStream_);
            return DG_OK;
        }

        DgError AclVDecProc::Deinit(void) {
            DeInitVdec();
            aclChannelManage.PushChannel(channelId_,last_ch_erro_);
            if(repThreadId_==0)
                return DG_OK;
            (void)aclrtUnSubscribeReport(static_cast<uint64_t>(repThreadId_), aclStream_);
            bQuit_ = true;
            pthread_join(repThreadId_, NULL);
            repThreadId_=0;
            return  DG_OK;
        }

        void *AclVDecProc::AclReportThread_(void *arg) {
            prctl(PR_SET_NAME, "AclVDecReportThread");
            auto aclVdec = static_cast<AclVDecProc*>(arg);
            if (aclVdec != nullptr) {
                return aclVdec->AclReportThread();
            }
            return nullptr;
        }

        void *AclVDecProc::AclReportThread(void) {
            vegaSetCurrentContext();
            while (!bQuit_) {
                aclrtProcessReport(30);  // 30ms
            }
            return nullptr;
        }


        void AclVDecProc::vdecCallback(acldvppStreamDesc *input, acldvppPicDesc *output, void *userdata) {
            std::shared_ptr<PackCbSeting> packCbSeting = std::shared_ptr<PackCbSeting>((PackCbSeting*)userdata);
            DgError dec_error=DG_OK;
            if(packCbSeting) {
                if(packCbSeting->memFull){
                    dec_error=DG_ERR_FULL;
                }
                cv::Size whSize;
                cv::Size stride;
                MatrixType type = MatrixType::Undefined;
                auto parent = (AclVDecProc *) packCbSeting->parent;
                if(!parent){
                   LOG(FATAL)<<"packCbSeting->parent == nullptr";
                }
                bool discard_frame=false;
                if(packCbSeting->bIgnore==NOCARE_FRAME) {
                    discard_frame = VideoBuffer::isDiscard(parent->vdecConf_.discardInterval, parent->frame_count_);
                }
                if(discard_frame){
                    packCbSeting->out_mem.reset();
                    packCbSeting->out_mem= nullptr;
                    dec_error=DG_OK;
                }
                if(packCbSeting->out_mem) {
                    if (dec_error == DG_OK) {
                        auto ret = acldvppGetPicDescRetCode(output);
                        if(ret == ERR_DECODE_NOPIC){
                           dec_error=DG_ERR_VDEC_NOPIC;
                        }else if (ret != ACL_SUCCESS) {
                            LOG(ERROR) << " Failed to acldvppGetPicDescRetCode,ret=" << ret << " .";
                            dec_error = DG_ERR_DECODE_FAIL;
                        }
                    }
                    if (dec_error == DG_OK) {
                        auto pixelFormat = acldvppGetPicDescFormat(output);
                        if (pixelFormat == PIXEL_FORMAT_YUV_SEMIPLANAR_420) {
                            type = MatrixType::NV12;
                        } else if (pixelFormat == PIXEL_FORMAT_YVU_SEMIPLANAR_420) {
                            type = MatrixType::NV21;
                        } else {
                            LOG(ERROR) << " Don't support the  pixelFormat " << pixelFormat << " ;";
                            dec_error = DG_ERR_DECODE_FAIL;
                        }
                    }
                    if(dec_error == DG_OK) {
                        whSize.width = acldvppGetPicDescWidth(output);
                        whSize.height = acldvppGetPicDescHeight(output);
                        stride.width = acldvppGetPicDescWidthStride(output);
                        stride.height = acldvppGetPicDescHeightStride(output);
                        if(!parent->vdecConf_.dataSize)
                            parent->vdecConf_.dataSize = stride.area() * YUV_BGR_SIZE_CONVERT_3 / YUV_BGR_SIZE_CONVERT_2;
                    }
                }
                parent->DecodeDone(dec_error, whSize, stride, type, packCbSeting.get());
            } else {
                LOG(ERROR) << "userdata is nullptr.";
            }
        }

        void AclVDecProc::DecodeDone(DgError error,cv::Size whSize,cv::Size stride,MatrixType type, const PackCbSeting *packCbSeting) {
            frame_count_++;
            if(error != DG_OK) {
                if(error == DG_ERR_DECODE_FAIL){
                   LOG(ERROR) <<"streamid "<<streamid_<<" packet_sn "<<packCbSeting->sn<<" hardware decode fail."<<" packet_len="<<packCbSeting->int_mem->size();
                }
                packCbSeting->cb(nullptr, error);
                return;
            }
            if(!packCbSeting->out_mem){
                packCbSeting->cb(nullptr, DG_OK);
            } else {
                auto output = std::make_shared<AclImage>();
                auto err = output->create(type, whSize, packCbSeting->out_mem, 1,stride.width,stride.height);
                if (err != DG_OK) {
                    LOG(ERROR) << "Failed to create output frame for video";
                    packCbSeting->cb(nullptr, DG_OK);
                } else {
                    output->setStreamId(streamid_);
                    packCbSeting->cb(output, DG_OK);
                }
            }
        }

        DgError AclVDecProc::DecodeVideo(MatrixSP input, MatrixType outputType, DecVideoCallback cb, \
                                        const VideoDecParamSP &vdecParam, bool bEos) {
            run_status_="start!";
            if (bEos == true) {
                LOG(ERROR) << "Recv eos of stream[" << streamid_ << "], send eos to device ...";
                VdecSendEosFrame();
                DeInitVdec();
                LOG(ERROR) << "Destroy stream[" << streamid_ << "] finished !!!";
                return DG_OK;
            }

            if (nullptr == input || nullptr == vdecParam) {
                LOG(ERROR) << "Invalid input matrix or param";
                return DG_ERR_INVALID_PARAM;
            }

            streamid_ = input->streamId();
            auto size = input->memory()->size();
            auto data = input->memory()->blob();

            switch (input->type()) {
                case MatrixType::H264:
                    videoFormat_ = H264_MAIN_LEVEL;
                    break;
                case MatrixType::H265:
                    videoFormat_ = H265_MAIN_LEVEL;
                    break;
                default:
                    LOG(ERROR) << "Invalid input video type " << (int)input->type() << " not support !!!";
                    return DG_ERR_INVALID_PARAM;
            }

            if (outputType != MatrixType::NV12) {
                LOG(ERROR) << "Invalid output video type " << (int)outputType << " not support !!!";
                return DG_ERR_INVALID_PARAM;
            }

            if (videoWidth_ && videoWidth_ && (videoWidth_ < 128 || videoWidth_ > maxResolution_||
                videoHeight_ < 128 || videoHeight_ > maxResolution_)) {
                LOG(ERROR) << "Input video width:" << videoWidth_  << ", height:" << videoHeight_ \
                           << ", VDEC support resolution form 128x128 to "<<maxResolution_<<"x"<<maxResolution_<<" .";
                return DG_ERR_INVALID_PARAM;
            }

            if (!vdecConf_.bSet) {
                videoWidth_ = vdecParam->videoWidth;
                videoHeight_ = vdecParam->videoHeight;
                if(videoWidth_!=0)
                {
                    vdecConf_.size.width = videoWidth_;
                    vdecConf_.size.height = videoHeight_;
                    vdecConf_.stride.width = (videoWidth_< VDEC_SPECIAL_WIDTH) ? VDEC_SPECIAL_STRIDE : DVPP_ALIGN_UP(videoWidth_, VDEC_STRIDE_WIDTH);
                    vdecConf_.stride.height = DVPP_ALIGN_UP(videoHeight_, VDEC_STRIDE_HEIGHT);
                    vdecConf_.dataSize = vdecConf_.stride.area()* YUV_BGR_SIZE_CONVERT_3 / YUV_BGR_SIZE_CONVERT_2;
                }else{
                    vdecConf_.size.width=0;
                    vdecConf_.size.height=0;
                    vdecConf_.stride.width=0;
                    vdecConf_.stride.height=0;
                    vdecConf_.dataSize= 0;
                    LOG(ERROR)<<"You should input the correct width and height of the video in order to allocate the appropriate memory ";
                }
                vdecConf_.outFormat   = outFormat_;
                vdecConf_.channelId   = channelId_;
                vdecConf_.threadId    = repThreadId_;
                vdecConf_.callback    = vdecCallback;
                vdecConf_.inFormat    = videoFormat_;
                vdecConf_.discardInterval = vdecParam->discardInterval;
                vdecConf_.fast_mode   = vdecParam->fast_mode;
                run_status_="InitVdec start!";
                if (DG_OK != InitVdec(vdecConf_)) {
                    LOG(ERROR) << "InitVdec fail.";
                    run_status_="InitVdec fail!";
                    DeInitVdec();
                    last_ch_erro_=DG_ERR_DECODE_FATAL;
                    return DG_ERR_DECODE_FATAL;
                }
                run_status_="InitVdec end!";
                vdecConf_.bSet=true;
                packet_sn_=0;
            }
            if(!bRegAclFramePoolMap){
               if(vdecConf_.dataSize!=0){
                 auto &aclFramePoolMap=AclFramePoolMap::getInstance();
                 if(DG_OK!=aclFramePoolMap.RegStreamId(vdecConf_.dataSize,streamid_)){
                    LOG(ERROR) <<"Fail to aclFramePoolMap.RegStreamId! mem_size "<<vdecConf_.dataSize<<" streamid_ "<<streamid_;
                 }
                 bRegAclFramePoolMap=true;
               }
            }

            PackCbSeting *packCbSeting = new PackCbSeting;
            if (packCbSeting == nullptr) {
                return DG_ERR_VDEC_FAIL;
            }
            // memset(packCbSeting,0,sizeof(PackCbSeting));
            packCbSeting->cb = cb;
            packCbSeting->parent = this;
            packCbSeting->bIgnore = vdecParam->bIgnore;
            packCbSeting->sn=packet_sn_;
            packet_sn_++;
            // video decode task
            std::shared_ptr<DvppDataInfo> vdecData = std::make_shared<DvppDataInfo>();
            vdecData->dataSize = size;
            vdecData->data = (uint8_t *)data;
            run_status_="CombineVdecProcess!";
            auto ret=CombineVdecProcess(vdecData, packCbSeting);
            if (ret != DG_OK) {
                run_status_="CombineVdecProcess fail!";
                delete packCbSeting;
                packCbSeting = nullptr;
                return ret;
            }
            run_status_="CombineVdecProcess done!";
            return DG_OK;
        }

        DgError AclVDecProc::InitVdec(const VdecConfig &config) {
            run_status_="initializing video decoder!";
            vdecChannelDesc_ = aclvdecCreateChannelDesc();
            if (vdecChannelDesc_ == nullptr) {
                LOG(ERROR) << "Failed to create vdec channel description.";
                return DG_ERR_INIT_FAIL;
            }

            // channelId: 0-15
            aclError ret = aclvdecSetChannelDescChannelId(vdecChannelDesc_, config.channelId);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Failed to set vdec channel id, ret = " << ret << ".";
                return DG_ERR_INIT_FAIL;
            }

            ret = aclvdecSetChannelDescThreadId(vdecChannelDesc_, config.threadId);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Failed to set thread id, ret = " << ret << ".";
                return DG_ERR_INIT_FAIL;
            }

            ret = aclvdecSetChannelDescCallback(vdecChannelDesc_, config.callback);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Failed to set vdec callback function, ret = " << ret << ".";
                return DG_ERR_INIT_FAIL;
            }

            ret = aclvdecSetChannelDescEnType(vdecChannelDesc_, config.inFormat);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Failed to set encoded type of input video, ret = " << ret << ".";
                return DG_ERR_INIT_FAIL;
            }

            ret = aclvdecSetChannelDescOutPicFormat(vdecChannelDesc_, config.outFormat);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Failed to set vdec output format, ret = " << ret << ".";
                return DG_ERR_INIT_FAIL;
            }
            if(config.fast_mode) {
                ret=aclvdecSetChannelDescOutMode(vdecChannelDesc_, 1);
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR) << "Failed to aclvdecSetChannelDescOutMode, ret = " << ret << ".";
                    return DG_ERR_INIT_FAIL;
                }else {
                   LOG(ERROR) << "change video decoder to  fast mode! ";
                }

            }

            ret = aclvdecCreateChannel(vdecChannelDesc_);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Failed to create vdec channel, ret = " << ret << ".";
                return DG_ERR_INIT_FAIL;
            }
            AclMemDvppSP acl_mem=std::make_shared<AclMemDvpp>();
            uint32_t block_size=32*1024-1;//64*32*1024=2M=1hugepage;调用acldvppMalloc接口申请内存时，会对用户输入的size按向上对齐成32整数倍后，再多加32字节。
            uint32_t block_count=64;
            auto vega_ret=acl_mem->create(block_size*block_count);
            if(vega_ret!=DG_OK){
                LOG(ERROR)<<"Fail to create acl Mem,size "<<block_size*block_count;
            }else{
                videoInDeviceMemHeap_=std::shared_ptr<MemHeap>(new MemHeap(block_size,block_count,acl_mem->blob()),[=](void *p){
                auto mem=acl_mem;
                mem.reset();
                MemHeap *heap=(MemHeap *)p;
                delete heap;
            });
    }
            return DG_OK;
        }

        DgError AclVDecProc::DeInitVdec(void) {
            if (vdecChannelDesc_ != nullptr) {
                 run_status_="DeInitVdec:aclvdecDestroyChannel start!";
                auto ret = aclvdecDestroyChannel(vdecChannelDesc_);
                run_status_="DeInitVdec:aclvdecDestroyChannel done!";
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR) << "Failed to destory dvpp channel, ret = " << ret;
                }
                aclvdecDestroyChannelDesc(vdecChannelDesc_);
                vdecChannelDesc_ = nullptr;
            }
            run_status_="DeInitVdec:aclvdecDestroyChannelDesc done!";
            if(bRegAclFramePoolMap) {
                auto &aclFramePoolMap = AclFramePoolMap::getInstance();
                if (DG_OK != aclFramePoolMap.UnRegStreamId(vdecConf_.dataSize, streamid_)) {
                    LOG(ERROR) << "Fail to aclFramePoolMap.UnRegStreamId! mem_size " << vdecConf_.dataSize
                               << " streamid_ " << streamid_;
                }
                bRegAclFramePoolMap=false;
            }
            streamid_ = 0;
            vdecConf_.bSet=false;
            run_status_="DeInitVdec:aclFramePoolMap.UnRegStreamId done!";

            if(videoInDeviceMemHeap_){
                videoInDeviceMemHeap_.reset();
            }

            return DG_OK;
        }

        DgError AclVDecProc::SetDvppPicDescData(const DvppDataInfo &info, acldvppPicDesc &picDesc) {
            aclError ret = acldvppSetPicDescData(&picDesc, info.data);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescData, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppSetPicDescSize(&picDesc, info.dataSize);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescSize, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppSetPicDescFormat(&picDesc, info.format);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescFormat, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppSetPicDescWidth(&picDesc, info.width);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescWidth, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppSetPicDescHeight(&picDesc, info.height);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescHeight, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppSetPicDescWidthStride(&picDesc, info.strideW);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescWidthStride, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppSetPicDescHeightStride(&picDesc, info.strideH);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetPicDescHeightStride, ret = " << ret;
                return DG_ERR_VDEC_FAIL;
            }

            return DG_OK;
        }

        acldvppPicDesc* AclVDecProc::CreateOutputPicDesc(uint8_t *picOutBufferDev,unsigned int size) {
            if (vdecConf_.outFormat != PIXEL_FORMAT_YUV_SEMIPLANAR_420 && \
                vdecConf_.outFormat != PIXEL_FORMAT_YVU_SEMIPLANAR_420) {
                LOG(ERROR) << "Decode format[" << vdecConf_.outFormat << "] for VPC is not supported, just support NV12 or NV21.";
                return nullptr;
            }

            DvppDataInfo picInfo;
            picInfo.width    = vdecConf_.size.width;
            picInfo.height   = vdecConf_.size.height;
            picInfo.strideW  = vdecConf_.stride.width;
            picInfo.strideH  = vdecConf_.stride.height;
            picInfo.dataSize = size;
            picInfo.data = picOutBufferDev;

            // picOutputDesc will be destoryed in vdec callback function
            acldvppPicDesc *picOutputDesc = acldvppCreatePicDesc();
            if (picOutputDesc == nullptr) {
                LOG(ERROR) << "Failed to acldvppCreatePicDesc";
                return nullptr;
            }

            if (SetDvppPicDescData(picInfo, *picOutputDesc) != DG_OK) {
                acldvppDestroyPicDesc(picOutputDesc);
                picOutputDesc = nullptr;
                return nullptr;
            }

            return picOutputDesc;
        }

        DgError AclVDecProc::CombineVdecProcess(const DvppDataInfoSP &info, PackCbSeting *packCbSeting) {
            std::shared_ptr<MemBase> inMemHost=std::make_shared<MemBase>();
            RawStream data=RawStream((unsigned char *) info->data,[=](void *p) {});
            auto ret=inMemHost->create(data,info->dataSize);
            if(ret!=DG_OK){
                LOG(ERROR)<<"Fail to creat MemBase!";
                return DG_ERR_VDEC_FAIL;
            }
            auto inMemDev = std::make_shared<AclMemDvpp>();
            VegaTmPnt tps,tpd;
            tps.mark();
            run_status_="copy packet from host to device";
            if(videoInDeviceMemHeap_){
                uint32_t size =inMemHost->size();
                unsigned char *ptr=(unsigned char *)videoInDeviceMemHeap_->alloc(size);
                if(ptr){
                    RawStream data = RawStream(ptr, [=](void *data) {
                        if(videoInDeviceMemHeap_){
                            videoInDeviceMemHeap_->free(ptr,size);
                        }
                    });
                    if(DG_OK!=inMemDev->create(data, size, 1)){
                        LOG(ERROR)<<"Fail to creat acl memory !";
                    }
                }else{
                    LOG(WARNING)<<"Fail to allocate memory of size "<<size<<" from video device memory heap!";
                }
            }else{
                LOG(ERROR)<<"video device memory heap has not been initialized.";
            }
            ret=inMemDev->fromHost(inMemHost);
            if(ret!=DG_OK){
                LOG(ERROR)<<"Fail to acl mem copy from host!";
                return DG_ERR_VDEC_FAIL;
            }
            tpd.mark();
            auto timecost=tpd-tps;
            if(timecost>20){
                LOGFULL<<"streamid "<<streamid_<<" cp packet to devic timecost is"<<timecost<<",packetLen "<<info->dataSize;
            }

            run_status_="acldvppCreateStreamDesc";
            std::shared_ptr<acldvppStreamDesc> streamInputDesc=std::shared_ptr<acldvppStreamDesc>(acldvppCreateStreamDesc(),
                    [=](acldvppStreamDesc *desc ){
                            if(desc!= nullptr) {
                                acldvppDestroyStreamDesc(desc);
                                creat_dvpp_stream_num_--;
                            }
                });
            if (streamInputDesc == nullptr) {
                LOG(ERROR) << "Failed to create input stream description.creat_dvpp_stream_num_ "<<creat_dvpp_stream_num_.load();
                return DG_ERR_VDEC_FAIL;
            }
            creat_dvpp_stream_num_++;
            if(creat_dvpp_stream_num_.load()>20){
               LOG(ERROR)<<"creat_dvpp_stream_num_ "<<creat_dvpp_stream_num_.load();
            }
            run_status_="acldvppSetStreamDescData";
            auto erro = acldvppSetStreamDescData(streamInputDesc.get(), inMemDev->blob());
            if(erro!=ACL_SUCCESS) {
                LOG(ERROR) << "Failed to set acldvppSetStreamDescData, ret = " << erro;
                return DG_ERR_VDEC_FAIL;
            }
            run_status_="acldvppSetStreamDescSize";
            erro = acldvppSetStreamDescSize(streamInputDesc.get(), info->dataSize);
            if (erro != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to acldvppSetStreamDescSize,  erro = " << erro;
                return DG_ERR_VDEC_FAIL;
            }
            packCbSeting->int_mem=inMemDev;
            packCbSeting->streamInputDesc=streamInputDesc;
            bool sendSkip=false;
            if(packCbSeting->bIgnore==DSICARD_FRAME) {
                sendSkip=true;
            }
            std::shared_ptr<acldvppPicDesc> picOutputDesc;
            if(!sendSkip) {
                uint8_t *outMemPtr = nullptr;
                unsigned int outMemSize = 0;
                std::shared_ptr<AclMemDvpp> outMem = nullptr;
                if (vdecConf_.dataSize <= 0 || vdecConf_.dataSize > MaxOutMemSize_) {
                    LOG(ERROR) << "vdecConf_.dataSize " << vdecConf_.dataSize << " is illegal; " << "use outMemRsv!";
                    auto outMemRsv = std::make_shared<AclMemDvpp>();
                    run_status_="creat outMem!";
                    if (DG_OK != outMemRsv->create(MaxOutMemSize_)) {
                        LOG(ERROR) << "Fail to outMemRsv->create!";
                        outMemRsv.reset();
                        sendSkip=true;
                        packCbSeting->memFull=true;
                    } else {
                        outMemPtr = (uint8_t *) outMemRsv->blob();
                        outMemSize = outMemRsv->size();
                        outMem = outMemRsv;
                    }
                } else {
                    auto &aclFramePoolMap = AclFramePoolMap::getInstance();
                    run_status_="PopFrame!";
                    std::shared_ptr<AclFrameBuffer> framebuf = aclFramePoolMap.PopFrame(vdecConf_.dataSize);
                    run_status_="PopFrame,done!";
                    if (!framebuf) {
                        LOG_EVERY_N(ERROR, 500) << "Fail to pop buffer from frame pool!";
                        sendSkip=true;
                        packCbSeting->memFull=true;
                    } else {
                        outMem = std::shared_ptr<AclMemDvpp>(framebuf->getMem().get(), [=](void *) {
                            auto &aclFramePoolMap1 = AclFramePoolMap::getInstance();
                            aclFramePoolMap1.PushFrame(framebuf);
                        });
                        outMemPtr = (uint8_t *) outMem->blob();
                        outMemSize = outMem->size();
                    }
                }
               if(!sendSkip) {
                   run_status_="CreateOutputPicDesc!";
                   picOutputDesc = std::shared_ptr<acldvppPicDesc>
                           (CreateOutputPicDesc(outMemPtr, outMemSize),
                            [=](acldvppPicDesc *desc) {
                                if (desc != nullptr)
                                    acldvppDestroyPicDesc(desc);
                            });
                   if (picOutputDesc == nullptr) {
                       LOG(ERROR) << "Fail to creat CreateOutputPicDesc! ";
                       sendSkip=true;
                   } else{
                       packCbSeting->out_mem=outMem;
                       packCbSeting->picOutputDesc=picOutputDesc;
                   }
               }
            }
            tps.mark();
            if(sendSkip) {
                run_status_="aclvdecSendSkippedFrame!";
                erro=aclvdecSendSkippedFrame(vdecChannelDesc_, streamInputDesc.get(), nullptr,packCbSeting);
                run_status_="aclvdecSendSkippedFrame done!";
            }else {
                run_status_="aclvdecSendFrame!";
                erro = aclvdecSendFrame(vdecChannelDesc_, streamInputDesc.get(), picOutputDesc.get(), nullptr,
                                        packCbSeting);
                run_status_="aclvdecSendFrame done!";
            }
            if (erro != ACL_SUCCESS) {
                std::string action=sendSkip?"aclvdecSendSkippedFrame":"aclvdecSendFrame";
                if (erro == ACL_ERROR_RT_AICPU_TIMEOUT || erro == ACL_ERROR_RT_AICPU_EXCEPTION) {
                    LOG(ERROR)<<action<<" encounter fatal erro "<<erro<<" , ACL_ERROR_RT_AICPU_TIMEOUT or ACL_ERROR_RT_AICPU_EXCEPTION !";
                    last_ch_erro_=erro;
                    return DG_ERR_DECODE_FATAL;
                }else {
                    LOG(ERROR) << "Failed to "<<action<<", erro = " << erro;
                }
                return DG_ERR_VDEC_FAIL;
            }
            tpd.mark();
            timecost=tpd-tps;
            if(timecost>40){
                if(sendSkip)
                    LOGFULL <<"stream "<<streamid_<<" time cost of aclvdecSendSkippedFrame is "<<timecost<<" ms!"
                    <<"paketLen "<<info->dataSize;
                else
                    LOGFULL <<"stream "<<streamid_<<" time cost of aclvdecSendFrame is "<<timecost<<" ms!"<<"paketLen "
                    <<info->dataSize;
            }
            return DG_OK;
        }

        DgError AclVDecProc::VdecSendEosFrame(void) {
            acldvppStreamDesc *eosStreamDesc = acldvppCreateStreamDesc();
            if (eosStreamDesc == nullptr) {
                LOG(ERROR) << "Fail to acldvppCreateStreamDesc for eos.";
                return DG_ERR_VDEC_FAIL;
            }
            aclError ret = acldvppSetStreamDescEos(eosStreamDesc, true);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Fail to acldvppSetStreamDescEos, ret = " << ret << ".";
                acldvppDestroyStreamDesc(eosStreamDesc);
                return DG_ERR_VDEC_FAIL;
            }
            ret = aclvdecSendFrame(vdecChannelDesc_, eosStreamDesc, nullptr, nullptr, nullptr);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Fail to aclvdecSendFrame, ret = " << ret << ".";
                acldvppDestroyStreamDesc(eosStreamDesc);
                return DG_ERR_VDEC_FAIL;
            }
            ret = acldvppDestroyStreamDesc(eosStreamDesc);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Fail to acldvppDestroyStreamDesc, ret = " << ret << ".";
                return DG_ERR_VDEC_FAIL;
            }
            return DG_OK;
        }
        std::shared_ptr<AclFramePoolMap> AclFramePoolMap::me_;
        std::map<unsigned int,std::shared_ptr<AclMemBlock>> AclFrameBuffer::mMemBlock_;


    }
}

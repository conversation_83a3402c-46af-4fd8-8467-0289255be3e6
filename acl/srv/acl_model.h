#ifndef VEGA_ACL_MODEL_H_
#define VEGA_ACL_MODEL_H_

#include "zfz/zfz_event.hpp"
#include "vega_model.h"
#include "model_config.h"
#include "acl/acl.h"
#include "vega_sdk_config.h"
#include "utils/acl_memory.h"
#include "utils/inc/aipp.h"
#include "include/station/block_queue.h"
#include "utils/file/dir.h"

namespace vega {
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        class AclEngine : public VegaEngine {
        public:
            AclEngine(const std::string &model) : model_name_(model){
                modelId_=0;
            }
            ~AclEngine() override {
                unload();
            };

        public:
            /**
             * Create an engine
             *
             * @param mem Model file data
             * @param config Model config
             * @return DG_OK if creation suceeeds, fail otherwise
             */
            DgError create(MemBlock &mem, std::shared_ptr<ModelConfig> config) override {
                aclrtRunMode runMode;
                aclrtGetRunMode(&runMode);
                config_ = config;
                total_mem_=0;
                batch_size_ = config_->batch_size_;
#if CANN50
                sdk_config::Platform  platform=vegaGetPlatform();
                std::string strModelInferNum=model_name_+"_infer_num";
                workNum_=sdk_config::get_cfg<int>(strModelInferNum, 0, platform);
                if(workNum_ >0){
                    LOG(ERROR)<<strModelInferNum<<" is "<<workNum_<<" .";
                }else{
                    workNum_=sdk_config::get_cfg<int>("infer_num", 1, platform);
                }
                if(config_->output_infos_.size()){
                    workNum_ = 2;
                }
                if(workNum_>1){
                    size_t workSize=0;
                    size_t weightSize=0;
                   auto ret=aclmdlQuerySizeFromMem(mem.ptr, mem.size_, &workSize, &weightSize);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR)<<"Fail to aclmdlQuerySizeFromMem ,ret "<<ret;
                        return DG_ERR_ENGINE_CREATE_FAIL;
                    }
                    uint32_t modelId=0;
               
                    aclSocName_ = aclrtGetSocName();
                    useHugePages_ =(bool)sdk_config::get_cfg<bool>("model_space_use_hupge_page", true , sdk_config::acl);
                    if ((aclSocName_.compare("Ascend710") == 0 || aclSocName_.compare("Ascend310P3") == 0
                         || aclSocName_.compare("Ascend310P1") == 0)
                        && useHugePages_) {
                        workMem_ = std::make_shared<AclMemDvpp>();
                        weightMem_ = std::make_shared<AclMemDvpp>();
                    } else {
                        workMem_ = std::make_shared<AclMem>();
                        weightMem_ = std::make_shared<AclMem>();
                    }

                    workMem_->create(workSize*workNum_, sizeof(float));
                    weightMem_->create(weightSize, sizeof(float));
                    LOG(ERROR)<<model_name_<<" infer_num "<<workNum_<<", weightSize "<<weightSize <<",workSize "<<workSize*workNum_;
                    for(int i=0;i<workNum_;i++){
                         ret=aclmdlLoadFromMemWithMem(mem.ptr, mem.size_, &modelId, workMem_->blob()+i*workSize, workSize, weightMem_->blob(), weightSize);
                        if (ret != ACL_ERROR_NONE) {
                            LOG(ERROR)<<"Fail to aclmdlLoadFromMemWithMem ,ret "<<ret;
                            return DG_ERR_ENGINE_CREATE_FAIL;
                        }
                        VegaStream stream0;
                        vegaCreateStream(&stream0);
                        modelIdQ_.push(std::pair<uint32_t,VegaStream>(modelId,stream0));
                    }
                    modelId_=modelId;
                }
#else
                workNum_=1;
#endif          
                if(workNum_==1){
                    auto ret = aclmdlLoadFromMem(mem.ptr, mem.size_, &modelId_);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "load model failed,  errorCode is " << static_cast<int32_t>(ret);
                    return DG_ERR_ENGINE_CREATE_FAIL;
                    }
                }
                //create model description
                modelDesc_ = aclmdlCreateDesc();
                if (modelDesc_ == nullptr) {
                    LOG(ERROR) << "create model description failed";
                    return DG_ERR_ENGINE_CREATE_FAIL;
                }
                auto ret = aclmdlGetDesc(modelDesc_, modelId_);
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR) << "get model description failed";
                    return DG_ERR_ENGINE_CREATE_FAIL;
                }
                input_num_ = aclmdlGetNumInputs(modelDesc_);
                output_num_ = aclmdlGetNumOutputs(modelDesc_);
                if (input_num_ != config_->input_infos_.size()) {
                    CHECK_EQ(input_num_, config_->input_infos_.size()*2);
                    isDynamicAipp_ = true;
                    input_num_ /= 2;
                }else{
                    CHECK_EQ(input_num_, config_->input_infos_.size());
                }
                CHECK_GE(output_num_,config_->output_layers_.size());
                total_mem_ = 0;
                auto loopNum = isDynamicAipp_ ? input_num_*2 : input_num_;
                auto step = isDynamicAipp_ ? 2 : 1;
                for(auto i = 0u ; i < loopNum ;i+=step){
                    LayerProp lp;
                    //get layer_name ,size ,dims
                    lp.input_ = true;
                    lp.layer_ = aclmdlGetInputNameByIndex(modelDesc_, i);
                    lp.mem_size_ = aclmdlGetInputSizeByIndex(modelDesc_, i);
                    aclDataType acl_DataType=aclmdlGetInputDataType(modelDesc_, i);
                    lp.data_type_ = VegaDataType::CHAR;
                    if(acl_DataType==ACL_FLOAT||acl_DataType==ACL_FLOAT16||acl_DataType==ACL_DOUBLE){
                        lp.data_type_ = VegaDataType::FLOAT;
                    }
                    aclmdlIODims dims;
                    ret = aclmdlGetInputDims(modelDesc_, i , &dims);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "get input dims failed,  errorCode is  " << static_cast<int32_t>(ret);
                        return DG_ERR_ENGINE_CREATE_FAIL;
                    }
                    if(!i%2 && isDynamicAipp_){
                        lp.mem_size_aipp_ = aclmdlGetInputSizeByIndex(modelDesc_, i+1);
                        total_aipp_mem_ += lp.mem_size_aipp_;
                    }
                    CHECK_EQ(batch_size_,dims.dims[0]);
                    lp.dims_.push_back(dims.dims[1]);//h
                    lp.dims_.push_back(dims.dims[2]);//w
                    lp.dims_.push_back(dims.dims[3]);//c
                    if(dims.dims[1]*dims.dims[2]*dims.dims[3]<0) lp.isDynamicIO_ = true;
                    total_mem_ += lp.mem_size_;
                    total_input_mem_ += lp.mem_size_;
                    lp.mem_size_/=batch_size_;
                    layers_.push_back(lp);
                    bindings_[lp.layer_] = std::pair<int, int>(i, lp.mem_size_);
                    // lp.info(config_->model_name_);
                }
                for(auto i =0u; i < output_num_; i++){
                    LayerProp lp;
                    //get layer_name ,size ,dims
                    lp.input_ = false;
                    std::string layer = aclmdlGetOutputNameByIndex(modelDesc_, i);
                    size_t pos = layer.find(':');
                    if(pos != std::string::npos ){
                        lp.layer_ = layer.substr(0,pos);
                    }else{ // *_aarch64.om
                        lp.layer_ = layer;
                    }
                    aclDataType acl_DataType=aclmdlGetOutputDataType(modelDesc_, i);
                    lp.data_type_ = VegaDataType::FLOAT;
                    if(acl_DataType!=ACL_FLOAT && acl_DataType!=ACL_FLOAT16 && acl_DataType!=ACL_DOUBLE){
                        lp.data_type_ = VegaDataType::CHAR;
                    }
                    lp.mem_size_ = aclmdlGetOutputSizeByIndex(modelDesc_, i);
                    if(!lp.mem_size_){
                        lp.isDynamicIO_ = true;
                        if(config_->getModelMaxOutputByName(lp.layer_,lp.mem_size_) != DG_OK){
                            LOG(ERROR) << "Faild to get model max output length in dynamic output";
                            return DG_ERR_ENGINE_CREATE_FAIL;
                        }
                        // 通过名字取而不是索引
                        lp.mem_size_ *= (int)lp.data_type_;
                    }

                    aclmdlIODims dims;
                    ret = aclmdlGetOutputDims(modelDesc_, i , &dims);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "get input dims failed,  errorCode is " << static_cast<int32_t>(ret);
                        return DG_ERR_ENGINE_CREATE_FAIL;
                    }
                    total_mem_ += lp.mem_size_;
                    total_output_mem_ += lp.mem_size_;
                    lp.mem_size_/=batch_size_;
                    if((lp.mem_size_/sizeof(float))!=(unsigned)(dims.dims[1]*dims.dims[2]*dims.dims[3])){
                        LOG(INFO)<<"source output dims is "<<" "<<dims.dims[0]<<" "<<dims.dims[1]<<" "<<dims.dims[2]<<" "<<dims.dims[3]
                        <<", mem_size_="<<lp.mem_size_;
                        dims.dims[1] = lp.mem_size_/sizeof(float);
                        dims.dims[2]=1;
                        dims.dims[3]=1;
                        LOG(INFO)<<"change output dims to "<<" "<<dims.dims[0]<<" "<<dims.dims[1]<<" "<<dims.dims[2]<<" "<<dims.dims[3];
                    }

                    lp.dims_.push_back(dims.dims[1]);
                    lp.dims_.push_back(dims.dims[2]);
                    lp.dims_.push_back(dims.dims[3]);
                    layers_.push_back(lp);
                    bindings_[lp.layer_] = std::pair<int, int>(input_num_ +i, lp.mem_size_);
                    // lp.info(config_->model_name_);
                }
                if(!poolQ_ ) {
                    poolQ_ = std::make_shared<BlockQueue<MemBaseSP>>();
                    sdk_config::Platform platform=vegaGetPlatform();
                    int model_mem_pool_num = sdk_config::get_cfg<int>("model_mem_pool_num",4, platform);
                    uint memSize=getInferMemSize()+total_aipp_mem_;
                    if(memSize<0x200000){
                        model_mem_pool_num=20;
                    }
                    LOG(WARNING) << "model_mem_pool_num :" << model_mem_pool_num << "," << model_name_ << " prepare mem size "
                                 << model_mem_pool_num * memSize;
                    uint memSizeAlign=(memSize+127)/128*128;
                    ulong totalPoolMemSize=memSizeAlign*model_mem_pool_num;
                    layerMem_=std::make_shared<acl::AclMemDvpp>();
                    if(DG_OK!=layerMem_->create(totalPoolMemSize)){
                        LOG(ERROR) << "Fail to create mem for model  size " << totalPoolMemSize;
                    }else {
                        DG_U8 *addr=(DG_U8 *)layerMem_->blob();
                        for (int i = 0; i < model_mem_pool_num; i++) {
                            auto pool = layerMem_->crop(addr, memSize,4);
                            addr+=memSizeAlign;
                            if (!pool) {
                                LOG(ERROR) << "Create mem for layermap fail, size " << memSize;
                            } else {
                                poolQ_->push(pool);
                            }
                       }
                    }
                }
                return DG_OK;

            }

            // create a context specified for each instance
            DgError createContext(void * &context) override {
                return DG_OK;
            }
            DgError getInputLayer(std::vector<std::string> &vecLayers) override {
                vecLayers.clear();
                for(unsigned i = 0; i < input_num_; i ++) {
                    vecLayers.push_back(layers_[i].layer_);
                }
                return DG_OK;
            }
            DgError getOutputLayer(std::vector<std::string> &vecLayers) override {
                vecLayers.clear();
                for(unsigned int i = input_num_; i < layers_.size(); i++) {
                    vecLayers.push_back(layers_[i].layer_);
                }
                return DG_OK;
            }
            int getAlignment(const std::string &layer) override {
                return sizeof(float);
            }

            void getOutputLayerDim(const std::string &layer,std::vector<int> &Dim) override {
                if (bindings_.find(layer) != bindings_.end()) {
                    LayerProp layerProp = getProp(layer);
                    Dim=layerProp.dims_;
                } else {
                    LOG(ERROR) << "while get layer dims, but can not find the " << layer << " layer!";
                }
            }
            /**
             * Get binding index of layer.
             * Binding index starts from 0, normally it starts from input layer.
             *
             * @param layer layer name
             * @return layer binding index
             */
            int getBindingIndex(const std::string &layer) override {
                if(layerExist(layer)) return bindings_[layer].first;
                LOG(ERROR) << "Layer not exist" << layer;
                return -1;
            }
            /**
             * Get layer name by binding index
             * @param bindingIdx
             * @return
             */
            std::string getLayer(int bindingIdx) override {
                for(auto it = bindings_.begin(); it != bindings_.end(); ++it) {
                    if(it->second.first == bindingIdx) {
                        return it->first;
                    }
                }

                LOG(ERROR) << "Binding " << bindingIdx << " not exist";
                return "";
            }

            int getMaxBatchSize() override {
                return config_->batch_size_;
            }
            /*
             * how many bytes required for this layer
             * @return
             */
            int getLayerLen(const std::string &layer) override {
                if(layerExist(layer)) {
                    return getProp(layer).mem_size_;
                }
                CHECK(false) << "Layer not exist" << layer;
                return -1;
            }

            LayerDataType getDataType(const std::string & layer) override {
                if(layerExist(layer)) {
                    return getProp(layer).data_type_;
                }
                CHECK(false) << "Layer not exist" << layer;
                return LayerDataType ::FLOAT;

            }
            DgError inference(int batchSize,
                                      std::vector<void *> & bindings,
                                      void *context,
                                      DynamicInfo &dynamicInfo,
                                      VegaStream stream = VEGA_INVALID_STREAM
                                      ) override {
                return inference(batchSize, bindings, context, ACLAippInfo(), dynamicInfo, stream);
            }
            DgError inference(int batchSize,
                                      std::vector<void *> & bindings,
                                      void *context,
                                      ACLAippInfo info,
                                      DynamicInfo &dynamicInfo,
                                      VegaStream stream = VEGA_INVALID_STREAM
                                      ) {
                AIPPConfig aippConfig = AIPPConfig();
                if(isDynamicAipp_){
                    //其中tv代表：tv表示 limited，即narrow range。pc代表：pc表示full，即wide range
                    AIPPConfig::ColorPair pair = AIPPConfig::voteColorPair(info.colorRange,info.colorSpace);
                    MatrixType srcType = info.matrixType;
                    MatrixType dstType = getModelInput(config_->input_infos_[0].model_input_);
                    auto err = AIPPConfig::getAIPPConfigByColorPair(pair,
                                                        srcType,
                                                        dstType,
                                                        info.size[0],
                                                        cv::Size(config_->input_infos_[0].src_width_no_align, config_->input_infos_[0].src_height_no_align),
                                                        aippConfig);
                    if(err != DG_OK){
                        LOG(ERROR) << "getAIPPConfigByColorPair failed " << err;
                        return err;
                    }
                    aippConfig.var = 1/config_->input_infos_[0].scale_;
                    auto meanSize = config_->input_infos_[0].mean_.size();
                    aippConfig.mean.resize(meanSize);
                    for (auto i =0u;i<meanSize;i++){
                        aippConfig.mean[i] = config_->input_infos_[0].mean_[i];
                    }
                }
                if(batchSize > batch_size_) {
                    LOG(ERROR) << "Model " << config_->model_name_ << "Batch size " << batchSize << " > " << batch_size_;
                    return DG_ERR_OVERFLOW;
                }
                // create input dataset and databuffer
                aclmdlDataset *input = aclmdlCreateDataset();
                if (input == nullptr) {
                    LOG(ERROR) << "can't create dataset, create input failed";
                    return DG_ERR_HW_FAILURE;
                };

                DG_U8 *addr = (DG_U8 *)bindings[0] + getInferMemSize();
                for(auto i=0u;i< input_num_;i++) {
                    aclDataBuffer* inputData = aclCreateDataBuffer(bindings[i], layers_[i].mem_size_*batch_size_);
                    //dumpBlob(bindings[i],layers_[i].mem_size_,layers_[i].layer_+"_input.txt");
                    if (inputData == nullptr) {
                        LOG(ERROR) << "can't create data buffer, create input failed";
                        return DG_ERR_HW_FAILURE;
                    }
                    auto ret = aclmdlAddDatasetBuffer(input, inputData);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "can't add data buffer, create input failed,  errorCode is  " << static_cast<int32_t>(ret);
                        aclDestroyDataBuffer(inputData);
                        inputData = nullptr;
                        return DG_ERR_HW_FAILURE;
                    }
                    if(isDynamicAipp_){
                        AddDatasetBuffer(input,addr + i*layers_[i].mem_size_aipp_, layers_[i].mem_size_aipp_);
                        // aippConfig.print();
                        aclmdlAIPP* aippParamTensor = SetAIPPTensor(aippConfig,i,batch_size_);
                        aclError aclret;
                        aclret = aclmdlSetInputAIPP(modelId_, input, (i*2)+1, aippParamTensor);
                        if (aclret != ACL_SUCCESS) {
                            LOG(ERROR) << "call aclmdlSetInputAIPP failed " << aclret << " modelId " << modelId_;
                            return DG_ERR_HW_FAILURE;
                        }

                        aclret = aclmdlDestroyAIPP(aippParamTensor);
                        if (aclret != ACL_SUCCESS) {
                            LOG(ERROR) << "call aclmdlDestroyAIPP failed";
                            return DG_ERR_HW_FAILURE;
                        }
                    }

                }

                // 设置输入
                aclError ret;
                int64_t *dims = new int64_t[4]; 
                for(auto i=0u;i<input_num_;i++) {
                    auto layer = layers_[i];
                    if(layer.isDynamicIO_){
                        dims[0] = batchSize;
                        dims[1] = dynamicInfo.dynamic_dim_sz_;
                        dims[2] = layer.dims_[1];
                        dims[3] = layer.dims_[2];               
                        aclTensorDesc * desc = aclCreateTensorDesc(ACL_FLOAT,4,dims,ACL_FORMAT_NCHW);
                        ret = aclmdlSetDatasetTensorDesc(input,desc,i);
                        if (ret != ACL_ERROR_NONE) {
                            LOG(ERROR) << "aclmdlSetDatasetTensorDesc failed, modelId is " << modelId_ <<",  errorCode is  " << static_cast<int32_t>(ret);
                            return DG_ERR_HW_FAILURE;
                        }
                    }
                }

                //create output dataset and databuffer
                aclmdlDataset *output = aclmdlCreateDataset();
                if (output == nullptr) {
                    LOG(ERROR) << "can't create dataset, create output failed";
                    return DG_ERR_HW_FAILURE;
                };

                for(auto i=0u;i<output_num_;i++) {

                    aclDataBuffer* outputData = aclCreateDataBuffer(bindings[i+input_num_], layers_[i+input_num_].mem_size_*batch_size_);
                    //dumpBlob(bindings[i+input_num_],layers_[i+input_num_].mem_size_,layers_[i].layer_+"_output.txt");
                    auto ret = aclmdlAddDatasetBuffer(output, outputData);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "can't add data buffer, create input failed,  errorCode is  " << static_cast<int32_t>(ret);
                        aclDestroyDataBuffer(outputData);
                        outputData = nullptr;
                        return DG_ERR_HW_FAILURE;
                    }
                }
                if(workNum_==1){
                    std::unique_lock<std::mutex> infer_lock(inference_mtx_);
                    auto ret = aclmdlExecute(modelId_, input, output); //one model can't execute in multiple threads;
                    infer_lock.unlock();
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "execute model failed, modelId is " << modelId_ <<",  errorCode is  " << static_cast<int32_t>(ret);
                        return DG_ERR_HW_FAILURE;
                    }
                }else{
                    auto model=modelIdQ_.pop();
                    auto ret = aclmdlExecuteAsync(model.first, input, output,model.second);
                    if (ret != ACL_SUCCESS) {
                        LOG(ERROR)<<" Failed to aclmdlExecuteAsync, ret "<<ret;
                        modelIdQ_.push(model);
                        return DG_ERR_HW_FAILURE;
                    }
                    ret = aclrtSynchronizeStream(model.second);
                    modelIdQ_.push(model);
                    if (ret != ACL_SUCCESS){
                        LOG(ERROR) << " Failed to aclrtSynchronizeStream ,ret "<<ret;
                            return DG_ERR_HW_FAILURE;
                    }
                }
                for(auto i=0u;i<output_num_;i++) {
                    auto layer = layers_[i+input_num_];
                    if(layer.isDynamicIO_){
                        aclTensorDesc *desc = aclmdlGetDatasetTensorDesc(output,i);
                        size_t outputNum = aclGetTensorDescElementCount(desc);
                        dynamicInfo.real_output_info_[layer.layer_] = outputNum;
                    }
                }
                for (auto i = 0u; i < aclmdlGetDatasetNumBuffers(input); ++i) {
                    aclDataBuffer *dataBuffer = aclmdlGetDatasetBuffer(input, i);
                    auto ret = aclDestroyDataBuffer(dataBuffer);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "destroy datebuffer input " << i << " failed,  errorCode is  " << static_cast<int32_t>(ret);
                        return DG_ERR_HW_FAILURE;
                    }
                }
                ret = aclmdlDestroyDataset(input);
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR) << "destroy dateset input failed,  errorCode is  " << static_cast<int32_t>(ret);
                    return DG_ERR_HW_FAILURE;
                }

                //destory output databuffer and dataset
                for (auto i = 0u; i < aclmdlGetDatasetNumBuffers(output); ++i) {
                    aclDataBuffer *dataBuffer = aclmdlGetDatasetBuffer(output, i);
                    ret = aclDestroyDataBuffer(dataBuffer);
                    if (ret != ACL_ERROR_NONE) {
                        LOG(ERROR) << "destroy datebuffer output " << i << " failed,  errorCode is  " << static_cast<int32_t>(ret);
                        return DG_ERR_HW_FAILURE;
                    }
                }
                ret = aclmdlDestroyDataset(output);
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR) << "destroy dateset output failed,  errorCode is  " << static_cast<int32_t>(ret);
                    return DG_ERR_HW_FAILURE;
                }
                delete[] dims;
                return DG_OK;
            }
            

            void setInputFmt(const std::vector<MatrixType> &inputFmts) {
                model_inputs_.clear();
                for(auto i = 0u; i < inputFmts.size(); i++){
                    model_inputs_.push_back(inputFmts[i]);
                }
                mps_.resize(inputFmts.size());
            }
            MatrixProperty & getInputMatrixProperty(int indexOfInputs) {
                CHECK((unsigned int)indexOfInputs < model_inputs_.size());
                CHECK((unsigned int)indexOfInputs < config_->input_infos_.size());
                MatrixProperty &mp = mps_[indexOfInputs];
                if(!mp.good()) {
                    MatrixAlign ma;
                    ma.mem_ = sizeof(char);
                    ma.mode_ = MatrixAlignMode::PIXEL;
                    ma.stride_ = cv::Size(1, 1);
                    mp.setDataType(VegaDataType::CHAR);
                    std::string layer=getLayer(indexOfInputs);
                    if(layer==""){
                       LOG(ERROR) <<"Fail to getLayer "<<indexOfInputs;
                    }else {
                        mp.setDataType(getDataType(layer));
                    }
                    cv::Size size(config_->input_infos_[indexOfInputs].width_,config_->input_infos_[indexOfInputs].height_);
                    auto err = mp.create(model_inputs_[indexOfInputs], size, ma);
                    CHECK(err == DG_OK);
                }
                return mp;
            }

            int getInferMemSize() {return total_mem_;}

            void unload(){
                //destroy model description
                if (modelDesc_ != nullptr) {
                    aclmdlDestroyDesc(modelDesc_);
                    modelDesc_ = nullptr;
                }
#if !CANN50
                if(modelId_!=0)
                    aclmdlUnload(modelId_);
#endif
                while(!modelIdQ_.empty()){
                    auto model=modelIdQ_.pop();
                    aclmdlUnload(model.first);
                    vegaDestroyStream(model.second);
                }
            }
            void dumpBlob(void *addr,size_t size,const std::string &path){

                MemBase mb;
                CHECK(DG_OK == mb.create(size));
                CHECK(ACL_ERROR_NONE ==  aclrtMemcpy(mb.blob(), mb.size(), addr, size,ACL_MEMCPY_DEVICE_TO_HOST));

                std::ofstream ofs;
                ofs.open(path, std::ios::out | std::ios::binary | std::ios::trunc);
                CHECK(ofs.is_open());
                ofs.write((const char *)mb.blob(), size);
                ofs.close();

                std::ofstream ofs1;
                ofs1.open(path+"1", std::ios::out | std::ios::binary | std::ios::trunc);

                float *data = (float *)mb.blob();
                CHECK(mb.size() % sizeof(float) == 0);
                for(auto i = 0u; i < mb.size() / sizeof(float); i++) {
                    ofs1 << "\n " << data[i];
                }
                ofs1.close();

            }
            inline int getInputNum() {return input_num_;}
            inline int getInputMemSize() {return total_input_mem_;}
            inline int getOutputMemSize() {return total_output_mem_;}
            inline int totalLayerNum(){return layers_.size();}
            inline std::vector<LayerProp> getLayer(){return layers_;}

        private:

            inline bool layerExist(const std::string &layer) {
                return bindings_.find(layer) != bindings_.end();
            }
            inline LayerProp &getProp(const std::string &layer) {
                return layers_[bindings_[layer].first];
            }
            DgError AddDatasetBuffer(aclmdlDataset *dataset,
                                      void* buffer, uint32_t bufferSize)
            {
                aclDataBuffer* dataBuf = aclCreateDataBuffer(buffer, bufferSize);
                if (dataBuf == nullptr) {
                    LOG(ERROR) << "Create data buffer error";
                    return DG_ERR_HW_FAILURE;
                }

                aclError ret = aclmdlAddDatasetBuffer(dataset, dataBuf);
                if (ret != ACL_SUCCESS) {
                    LOG(ERROR) << "Add dataset buffer error " <<  ret;
                    aclDestroyDataBuffer(dataBuf);
                    return DG_ERR_HW_FAILURE;
                }

                return DG_OK;
            }

            aclmdlAIPP *SetAIPPTensor(AIPPConfig aippconfig,int idx,uint64_t batch_size) {
                aclmdlAIPP *aippParamTensor = aclmdlCreateAIPP(batch_size);
                if (aippParamTensor == nullptr) {
                    LOG(ERROR) << "call aclmdlCreateAIPP failed";
                }

                aclError ret = aclmdlSetAIPPInputFormat(aippParamTensor, aippconfig.input_format);
                if (ret != 0) {
                    LOG(ERROR) << "call aclmdlSetAIPPInputFormat failed";
                }

                ret = aclmdlSetAIPPCscParams(aippParamTensor, aippconfig.csc_switch, 
                aippconfig.matrix_r0c0, aippconfig.matrix_r0c1, aippconfig.matrix_r0c2,
                aippconfig.matrix_r1c0,aippconfig.matrix_r1c1,aippconfig.matrix_r1c2,
                aippconfig.matrix_r2c0,aippconfig.matrix_r2c1,aippconfig.matrix_r2c2,
                aippconfig.output_bias_0,aippconfig.output_bias_1,aippconfig.output_bias_2,
                aippconfig.input_bias_0,aippconfig.input_bias_1,aippconfig.input_bias_2);
                if (ret != 0) {
                    LOG(ERROR) << "call aclmdlSetAIPPCscParams failed";
                }

                ret = aclmdlSetAIPPRbuvSwapSwitch(aippParamTensor, aippconfig.rbuv_swap_switch);
                if (ret != 0) {
                    LOG(ERROR) << "call aclmdlSetAIPPRbuvSwapSwitch failed";
                }

                ret = aclmdlSetAIPPAxSwapSwitch(aippParamTensor, 0);
                if (ret != 0) {
                    LOG(ERROR) << "call aclmdlSetAIPPAxSwapSwitch failed";
                }

                ret = aclmdlSetAIPPSrcImageSize(aippParamTensor, aippconfig.srcImageSizeW, aippconfig.srcImageSizeH);
                if (ret != 0) {
                    LOG(ERROR) << "call aclmdlSetAIPPSrcImageSize failed";
                }

                for (auto i = 0; i < batch_size_; i++) {
                    ret = aclmdlSetAIPPScfParams(aippParamTensor, 0, 0, 0, 0, 0, i);
                    if (ret != 0) {
                        LOG(ERROR) << "call aclmdlSetAIPPScfParams failed";
                    }

                    ret = aclmdlSetAIPPCropParams(aippParamTensor,aippconfig.cropSwitch,
                    aippconfig.cropStartPosW,aippconfig.cropStartPosH,
                    aippconfig.cropSizeW,aippconfig.cropSizeH, i);
                    if (ret != 0) {
                        LOG(ERROR) << "call aclmdlSetAIPPCropParams failed";
                    }

                    ret = aclmdlSetAIPPPaddingParams(aippParamTensor, 0, 0, 0, 0, 0, i);
                    if (ret != 0) {
                        LOG(ERROR) << "call aclmdlSetAIPPPaddingParams failed";
                    }

                    ret = aclmdlSetAIPPDtcPixelMean(aippParamTensor, aippconfig.mean[0], aippconfig.mean[1], aippconfig.mean[2], 0, i);
                    if (ret != 0) {
                        LOG(ERROR) << "call aclmdlSetAIPPDtcPixelMean failed";
                    }

                    ret = aclmdlSetAIPPDtcPixelMin(aippParamTensor, 0.0, 0.0, 0.0, 0.0, i);
                    if (ret != 0) {
                        LOG(ERROR) << "call aclmdlSetAIPPDtcPixelMin failed";
                    }

                    ret = aclmdlSetAIPPPixelVarReci(aippParamTensor, aippconfig.var, aippconfig.var, aippconfig.var, 1.0, i);
                    if (ret != 0) {
                        LOG(ERROR) << "call aclmdlSetAIPPPixelVarReci failed";
                    }
                }

                return aippParamTensor;
            }
        public:
            std::string modelName() {
                return model_name_;
            }
            std::shared_ptr<BlockQueue<MemBaseSP>> poolQ_ = nullptr;
        protected:
            bool isDynamicAipp_ = false;
            bool isDynamicInput_ = false;
            std::string model_name_;
            std::shared_ptr<ModelConfig> config_;
            int batch_size_ = -1;
            std::vector<LayerProp> layers_;
            std::map<std::string, std::pair<int, int>> bindings_;
            std::vector<MatrixType> model_inputs_;
            std::vector<MatrixProperty> mps_;
            int total_mem_;
            int total_input_mem_  = 0;
            int total_aipp_mem_  = 0;
            int total_output_mem_ = 0;
            uint32_t modelId_;
            std::mutex inference_mtx_;
            BlockQueue<std::pair<uint32_t,VegaStream>> modelIdQ_;
            std::shared_ptr<AclMemDvpp> workMem_;
            std::shared_ptr<AclMemDvpp> weightMem_;
            bool useHugePages_ = false;
            std::string aclSocName_;
            AclMemDvppSP layerMem_;
            int  workNum_;
            size_t input_num_ = 0;
            size_t output_num_ = 0;
            aclmdlDesc *modelDesc_ = nullptr;

        };

        template <typename Tp>
        class AclLayerMap : public LayerMap {
        public:
            explicit AclLayerMap(std::shared_ptr<LayerGroup> &group,
                                  std::shared_ptr<ModelConfig> config,
                                  std::shared_ptr<VegaEngine> engine) : LayerMap(group, config) {
                engine_ = std::dynamic_pointer_cast<Tp>(engine);
                is_copy_to_host_=config->is_copy_to_host_;
                std::string acl_soc_name=aclrtGetSocName();
                if(acl_soc_name.compare("Ascend310P1")==0){
                    is_acl_soc_=true;
                }
            }
            ~AclLayerMap() override {
            }
            void forMatrix(MatrixSPV vsrc,MatrixSPV vdst) override{
                info_.colorRange.resize(vdst.size());
                info_.colorSpace.resize(vdst.size());
                info_.size.resize(vdst.size());

                for(auto i = 0u;i<vdst.size();i++){
                    info_.colorRange[i] = vsrc[i]->colorRange();
                    info_.colorSpace[i] = vsrc[i]->colorSpace();
                    info_.size[i]      = vdst[i]->size();
                }   
                info_.matrixType = vdst[0]->type();
            }
            ACLAippInfo getACLAippInfo(){return info_;}
            std::shared_ptr<LayerMap> clone() override {
                auto me = std::make_shared<AclLayerMap>(group_, config_, engine_.lock());
                *me = *this;
                return me;
            }
        protected:
            MatrixSP createMatrixTemplate(int indexOfInputs) override {
                auto sp = engine_.lock();
                auto engine = std::dynamic_pointer_cast<Tp>(sp);
                CHECK(engine != nullptr);

                auto &prop = engine->getInputMatrixProperty(indexOfInputs);
                auto matrix = std::make_shared<AclImage>();
                auto ret = matrix->createEmpty(prop);
                if(ret != DG_OK) {
                    LOG(ERROR) << "Create input matrix failed";
                    return nullptr;
                }
                return matrix;
            }
            float * copyOutputToHost(VegaStream stream) override {
                if(output_size_ % sizeof(float) != 0) {
                    LOG(ERROR) << "Output size " << output_size_ << " not multiple of floats";
                    return nullptr;
                }
                if(!is_copy_to_host_)
                    return (float *)output_start_;
                if(is_acl_soc_)
                    return (float *)output_start_;
                host_=std::make_shared<MemBase>();
                host_->create((size_t)output_size_,ACL_MM_ALIGN);
                if(!host_->good()){
                    return nullptr;
                }
                auto err = vegaMemcpy((void *)host_->blob(),
                                      (void *)output_start_,
                                      output_size_,
                                      VEGA_MEMCPY_DEVICE_TO_HOST,
                                      stream);
                if(err != DG_OK) {
                    LOG(ERROR) << "Copy device output to host failed";
                    return nullptr;
                }
                return (float *)host_->blob();
            }
            void getMemFromPoolQ(MemBaseSP &pool, std::shared_ptr<BlockQueue<MemBaseSP>> poolQ){
                auto mem=poolQ->pop();
                mem->resetOffset();
                pool = std::shared_ptr<MemBase>(mem.get(),[=](void *ptr){
                    auto mem1=mem;
                    auto engine1 = engine_.lock();
                    if(!engine1) {
                        LOG(ERROR) << "Impossible, engine lost";
                        }else {
                            engine1->poolQ_->push(mem1);
                        }
                });
            }
        public:
            DgError prepareMemory(MemBaseSP pool) override {
                if(!pool) {
                    auto engine = engine_.lock();
                    if (!engine) {
                        LOG(ERROR) << "Impossible, engine lost";
                        return DG_ERR_ENGINE_CREATE_FAIL;
                    }
                    if(engine->poolQ_){
                        getMemFromPoolQ(pool,engine->poolQ_);
                    }else{
                        pool     = std::make_shared<acl::AclMemDvpp>();
                        auto size = engine->getInferMemSize();
                        auto err = pool->create(size, sizeof(float));
                        if (err != DG_OK) {
                            LOG(ERROR) << "Create mem for layermap fail, size " << size;
                            return err;
                        }
                    }

                }
                auto ret = LayerMap::prepareMemory(pool);
                return ret;
            }
            DgError prepareMemory(MemBaseSP pool, int size) {
                if (!pool) {
                    pool     = std::make_shared<acl::AclMemDvpp>();
                    auto err = pool->create(size, sizeof(float));
                    if (err != DG_OK) {
                        LOG(ERROR) << "Create mem for layermap fail, size " << size;
                        return err;
                    }
                }
                return LayerMap::prepareMemory(pool);
            }
        protected:
            std::weak_ptr<Tp> engine_;
            HostMemSP host_;
            ACLAippInfo info_;
            bool is_acl_soc_=false;
        };
        class CombinedAclEngine : public CombinedEngine<AclEngine, AclLayerMap, AclMemDvpp> {
        public:
            std::shared_ptr<BlockQueue<MemBaseSP>> poolQ_;
        };
        class ExternalAclEngine : public ExternalEngine<AclEngine,AclLayerMap,AclMemDvpp,CombinedAclEngine> {
        public:
            std::shared_ptr<BlockQueue<MemBaseSP>> poolQ_;
        };
        class AclModel : public VegaModel {
        public:
            explicit AclModel(std::shared_ptr<ModelConfig> config) : VegaModel(config) {

            }
            ~AclModel() override = default;
        DgError inference(int batchSize,
                                    std::shared_ptr<LayerMap> &layerMap,
                                    void *context,
                                    DynamicInfo &dynamicInfo,
                                    VegaStream stream = VEGA_INVALID_STREAM) override{
            DgError ret = DG_OK;
            if(!config_) {
                LOG(ERROR) << "Config not loaded in model";
                return DG_ERR_INIT_FAIL;
            }

            if(batchSize > config_->batch_size_) {
                LOG(ERROR) << "Requested inference batch size " << batchSize << " exceeds expected " << config_->batch_size_;
                return DG_ERR_INVALID_PARAM;
            }

            std::vector<void *> bindings;
            bindings.resize(config_->input_infos_.size() + config_->output_layers_.size());

            for(auto &inputInfo : config_->input_infos_) {
                auto &layer = inputInfo.layer_;
                auto idx = engine_->getBindingIndex(layer);
                bindings[idx] = (void *)layerMap->device(layer, 0);
            }

            for(auto &layer : config_->output_layers_) {
                auto idx = engine_->getBindingIndex(layer);
                bindings[idx] = (void *)layerMap->device(layer, 0);
            }

            switch (engine_type_) {
                case ENGINE_TYPE::BASE:{
                    std::shared_ptr<AclLayerMap<AclEngine>> lmp = std::dynamic_pointer_cast<AclLayerMap<AclEngine>>(layerMap);
                    auto info = lmp->getACLAippInfo();
                    ret = std::dynamic_pointer_cast<AclEngine>(engine_)->inference(batchSize, bindings, context,info, dynamicInfo, stream);
                    break;
                }
                case ENGINE_TYPE::COMBINE:{
                    ret = std::dynamic_pointer_cast<CombinedAclEngine>(engine_)->inference(batchSize, bindings, context, dynamicInfo, stream);
                    break;
                }
                case ENGINE_TYPE::EXTRA:{
                    ret = std::dynamic_pointer_cast<ExternalAclEngine>(engine_)->inference(batchSize, bindings, context, dynamicInfo, stream);
                    break;
                }
                default:
                    break;
            }
            if(ret != DG_OK) {
                LOG(ERROR) << "Inference failed: " << ret;
                return ret;
            }
            /**
             * Copy memory from output device to host
             */
            ret = layerMap->mapOutputToHost(stream);
            if(ret != DG_OK) {
                LOG(ERROR) << "Copy output failed";
                return ret;
            }

            return ret;
        }
        protected:
            template <typename TempEngine>
            TempEngine *createSubCombinedEngine(std::vector<MemBlock> &blocks,TempEngine *engine){
                if(!engine){
                    LOG(ERROR) << "new engine " ;
                    engine = new TempEngine();
                }
                auto ret = engine->create(blocks, config_);

                if(ret == DG_OK) {
                    // engine->setInputFmt(model_inputs_);          
                    engine->setInputFmt(input_fmts_);
                    LOGFULL << "Create engine OK";
                    return engine;
                }

                LOG(ERROR) << "Create engine failed: " << config_->model_name_;
                delete engine;
                return nullptr;
            }

            VegaEngine *createCombinedEngine(std::vector<MemBlock> &blocks) {
                if (config_->model_graph_.has_externalmodel_) {
                    engine_type_ = ENGINE_TYPE::EXTRA;
                    auto engine  = new ExternalAclEngine();

                    auto graph = &config_->model_graph_;

                    for (auto i = 0u; i < graph->external_model_name_.size(); i++) {
                        auto ext_model_name = graph->external_model_name_[i];
                        auto existCfg = ModelConfigMap::share(ext_model_name);
                        std::shared_ptr<ModelConfig> ext_config;
                        if (existCfg) {
                            ext_config = std::dynamic_pointer_cast<ModelConfig>(existCfg);
                            if (!ext_config) {
                                LOG(ERROR) << "Model config in pool with a different type: " << ext_model_name;
                            }
                        }

                        if (!ext_config) {
                            std::string find_path = config_->config_path_.substr(0, config_->config_path_.rfind(config_->model_name_));
                            std::vector<std::string> find_paths;
                            DgError ret = findDir(find_path, ext_model_name, find_paths);
                            if (ret != DG_OK) {
                                LOG(ERROR) << "No folder beginning with " << ext_model_name << " was found in the " << find_path
                                           << " path.";
                                delete engine;
                                return nullptr;
                            }
                            if (find_paths.size() > 1) {
                                LOG(ERROR) << " Only one folder starting with " << ext_model_name << " is supported, now there are "
                                           << find_paths.size();
                                delete engine;
                                return nullptr;
                            }
                            std::string ext_model_path = find_paths[0];
                            auto cfgFile               = ext_model_path + "/config.json";
                            ext_config                 = std::make_shared<ModelConfig>();
                            if (ext_config->load(cfgFile) != DG_OK) {
                                LOG(ERROR) << "Fail to load config file :" << cfgFile;
                                CHECK(false);
                            }

                            if (ext_config->model_name_ != ext_model_name) {
                                LOG(ERROR) << "model name in config (" << ext_model_name << ") not match expected " << ext_model_name;
                            }

                            /**
                             * Put config into a common map as weakptr of this
                             * to support retrieving model info by client
                             */
                            ModelConfigMap::put(ext_config);
                        }
                        auto ext_model = vegaGetModel(ext_config);  // create model
                        engine->extern_models_.insert(
                          std::pair<std::string, std::shared_ptr<VegaModel>>(ext_model_name, ext_model));
                    }
                    engine = createSubCombinedEngine<ExternalAclEngine>(blocks, engine);
                    return (VegaEngine *)engine;
                } else {
                    engine_type_ = ENGINE_TYPE::COMBINE;
                    auto engine  = new CombinedAclEngine();
                    auto retEngine = createSubCombinedEngine<CombinedAclEngine>(blocks, engine);
                    return retEngine;
                }
                return nullptr;
            }

            VegaEngine *createEngine(std::vector<MemBlock> &blocks) override {
                for (auto i = 0u; i< model_inputs_.size(); i++ ){
                    MatrixType modelInput = input_fmts_[i];
                    LOGFULL << "Model Input Fmt " << i << " : " << matrix_type_str(modelInput);
                }
                if (blocks.size() > 1 ) {
                    return createCombinedEngine(blocks);
                } else {
                    auto engine = new AclEngine(config_->model_name_);
                    engine->setInputFmt(input_fmts_);
                    auto ret = engine->create(blocks[0], config_);
                    if (ret == DG_OK) {
                        LOGFULL << "Create engine OK";
                        return engine;
                    }
                    LOG(ERROR) << "Create engine failed: " << config_->model_name_;
                    delete engine;
                    return nullptr;
                }
            }
            std::shared_ptr<LayerMap> createLayerMap(std::shared_ptr<ModelConfig> config,
                                                     std::shared_ptr<LayerGroup> group) override {
                std::shared_ptr<LayerMap> lm;
                switch (engine_type_) {
                case ENGINE_TYPE::BASE:
                    lm = std::make_shared<AclLayerMap<AclEngine>>(group, config, engine_);
                    break;
                case ENGINE_TYPE::COMBINE:
                    lm = std::make_shared<AclLayerMap<CombinedAclEngine>>(group, config, engine_);
                    break;
                case ENGINE_TYPE::EXTRA:
                    lm = std::make_shared<AclLayerMap<ExternalAclEngine>>(group, config, engine_);
                    break;
                default:
                    break;
                }
                return lm;
            }

        };
    }
} // namespace vega
#endif //VEGA_ACL_MODEL_H_

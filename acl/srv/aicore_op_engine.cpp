#include "aicore_op_engine.h"
namespace vega {
namespace acl {
DgError AicoreOp::LoadModel(std::string model_path) {
    std::string file_path_model_aipp=model_path + "/aipp.om";
    aclError ret = aclmdlLoadFromFile(file_path_model_aipp.c_str(),&aipp_modelId_);
    if (ret != ACL_ERROR_NONE) {
        LOG(ERROR)<<"Load model from file "<<file_path_model_aipp<<" failed, for " << ret <<".";
        return DG_ERR_HW_FAILURE;
    }
    auto modelDesc = aclmdlCreateDesc();
    if (modelDesc == nullptr) {
        LOG(ERROR) << "create model description failed";
        return DG_ERR_ENGINE_CREATE_FAIL;
    }
    ret = aclmdlGetDesc(modelDesc, aipp_modelId_);
    if (ret != ACL_ERROR_NONE) {
        LOG(ERROR) << "get model description failed";
        return DG_ERR_ENGINE_CREATE_FAIL;
    }
    aclmdlGetInputIndexByName(modelDesc,ACL_DYNAMIC_AIPP_NAME,&aipp_index_);
    std::string file_path_resize_model = model_path + "/ResizeBilinearV2_2560_2560.om";
    FILE *fp = fopen(file_path_resize_model.c_str(), "rb");
    if (!fp) {
        LOG(ERROR) << "AicoreOpExecute fail to open " << file_path_resize_model;
        return DG_ERR_INIT_FAIL;
    }
    fseek(fp, 0, SEEK_END);
    long size = ftell(fp);
    std::shared_ptr<unsigned char> bufPtr(new unsigned char[size],std::default_delete<unsigned char[]>());
    fseek(fp, 0, SEEK_SET);
    long readSize = fread(bufPtr.get(), 1, size, fp);
    if (readSize != size) {
        LOG(ERROR) << "AicoreOpExecute read size is " << readSize << ",size of file is " << size;
        fclose(fp);
        return DG_ERR_INIT_FAIL;
    }
    fclose(fp);
    ret = aclopLoad(bufPtr.get(), size);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Fail to aclopLoad " << file_path_resize_model << "; Error code: " << ret;
        return DG_ERR_INIT_FAIL;
    } else {
        LOG(INFO) << "aclopLoad " << file_path_resize_model << " successfully";
    }

    LOG(INFO)<<"aclopLoad "<<file_path_resize_model<<" ret="<<ret;
    return DG_OK;
}
OperatorDesc AicoreOp::CreateOpResizeBilinearV2Desc(cv::Size inputSize, cv::Size outputSize) {
    std::vector<int64_t> input_shape{ 1, 3, inputSize.height,inputSize.width};
    std::vector<int64_t> sizes_shape{ 2 };
    std::vector<int64_t> output_shape{ 1, 3,outputSize.height,outputSize.width};
    OperatorDesc opDesc(opResizeBilinearV2_);
    opDesc.AddInputTensorDesc(ACL_FLOAT, input_shape.size(), input_shape.data(), ACL_FORMAT_NCHW);
    opDesc.AddInputTensorDesc(ACL_INT32, sizes_shape.size(), sizes_shape.data(), ACL_FORMAT_ND);
    opDesc.AddOutputTensorDesc(ACL_FLOAT, output_shape.size(), output_shape.data(), ACL_FORMAT_NCHW);
    auto opAttr = opDesc.opAttr;
    aclopSetAttrBool(opAttr, "align_corners", false);
    aclopSetAttrBool(opAttr, "half_pixel_centers", false);
    //int64_t shape_range[4][2] = {{1, 1}, {3,3}, {1, 1080}, {1, 1920}};
    //aclSetTensorShapeRange(opDesc.outputDesc[0], 4, shape_range);
    return opDesc;
}
OperatorDesc AicoreOp::CreateOpResizeDesc(cv::Size inputSize, cv::Size outputSize) {
    std::vector<int64_t> input_shape{ 1, inputSize.height, inputSize.width,3};
    std::vector<int64_t> roi_shape{};
    std::vector<int64_t> scales_shape{};
    std::vector<int64_t> sizes_shape{ 4 };
    std::vector<int64_t> output_shape{ 1, outputSize.height, outputSize.width ,3};
    int64_t shape_range[4][2] = {{1, 1}, {1, 2560}, {1, 2560}, {3,3}};
    OperatorDesc opDesc(opResize_);
    opDesc.AddInputTensorDesc(ACL_FLOAT, input_shape.size(), input_shape.data(), ACL_FORMAT_NHWC);
    opDesc.AddInputTensorDesc(ACL_FLOAT, roi_shape.size(), roi_shape.data(), ACL_FORMAT_ND);
    opDesc.AddInputTensorDesc(ACL_FLOAT, scales_shape.size(), scales_shape.data(), ACL_FORMAT_ND);
    opDesc.AddInputTensorDesc(ACL_INT64, sizes_shape.size(), sizes_shape.data(), ACL_FORMAT_ND);
    opDesc.AddOutputTensorDesc(ACL_FLOAT, output_shape.size(), output_shape.data(), ACL_FORMAT_NHWC);
    aclSetTensorShapeRange(opDesc.outputDesc[0], 4, shape_range);
    auto opAttr = opDesc.opAttr;
    aclopSetAttrString(opAttr, "coordinate_transformation_mode", "align_corners");
    aclopSetAttrFloat(opAttr, "cubic_coeff_a", -0.75);
    aclopSetAttrInt(opAttr, "exclude_outside", 0);
    aclopSetAttrFloat(opAttr, "extrapolation_value", 0.0);
    aclopSetAttrString(opAttr, "mode", "linear");
    aclopSetAttrString(opAttr, "nearest_mode", "round_prefer_floor");
    return opDesc;
}

void AicoreOp::FreeAclDataBuf(std::vector<aclDataBuffer *> vBuffer0,std::vector<aclDataBuffer *> vBuffer1){
    for(auto i = 0u;i<vBuffer0.size();i++)
        aclDestroyDataBuffer(vBuffer0[i]);
    for(auto i = 0u;i<vBuffer1.size();i++)
        aclDestroyDataBuffer(vBuffer1[i]);

}

DgError AicoreOp::aiCpuPast(AclImageSP input,AclImageSP &output,aclrtStream stream){
    auto aclOpProcess=std::make_shared<AclOpProcess>();
    float mean[3]={0,0,0};
    float scale=1.0;
    MatrixSP inputM = input;
    MatrixSP outputM = output;
    auto ret=aclOpProcess->cropResize(inputM,outputM,mean,scale,stream);
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to aclOpProcess cropResize ";
        return DG_ERR_HW_FAILURE;
    }
    return DG_OK;
}

DgError AicoreOp::aippCrop(AclImageSP input, AclMemDvppSP &memBgrOut,ProcessMatrixParam param,aclrtStream stream){
       std::shared_ptr<aclmdlAIPP> aippPtr=std::shared_ptr<aclmdlAIPP>(aclmdlCreateAIPP(1),[=](aclmdlAIPP *aipp){
        aclmdlDestroyAIPP(aipp);
    });
    float scale=1.0/param.scale();
    cv::Rect roi=input->roi();
    int32_t cropStartPosW=(roi.x+1)/2*2;
    int32_t cropStartPosH=(roi.y+1)/2*2;
    int32_t cropSizeW=roi.width; 
    int32_t cropSizeH=roi.height;
    auto ret=SetAippInfo(aippPtr.get(),input->stride().width, input->stride().height,cropStartPosW,cropStartPosH,cropSizeW,cropSizeH,0,0,0,0,param.mean()[0],param.mean()[1],
    param.mean()[2],scale);
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to SetAippInfo!";
        return DG_ERR_HW_FAILURE;
    } 
    memBgrOut = std::make_shared<AclMemDvpp>();
    unsigned int memBgrSize=cropSizeW*cropSizeH*3*sizeof(float);
    ret=memBgrOut->create(memBgrSize, sizeof(float));
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to creat dvpp AclDvppMem,size "<<memBgrSize;
        return DG_ERR_HW_FAILURE;
    }
    ret=aippInfer(aippPtr.get(),input->MemSize(), input->data(),memBgrSize,memBgrOut->blob(),stream);
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to aippInfer";
        return DG_ERR_HW_FAILURE;
    }
    return DG_OK;
}
DgError AicoreOp::cropResize(MatrixSP &input, MatrixSP &output, ProcessMatrixParam &param, aclrtStream stream) {
    AclImageSP output_acl = std::dynamic_pointer_cast<AclImage>(output);
    if (!output_acl) {
        LOG(INFO) << "output is'nt AclImageSP!";
        return DG_ERR_INVALID_IMAGE;
    }
    AclImageSP input_acl = std::dynamic_pointer_cast<AclImage>(input);
    if (!input_acl) {
        LOG(INFO) << "input_acl is'nt AclImageSP!";
        return DG_ERR_INVALID_IMAGE;
    }
    if (input_acl->type() != MatrixType::NV12 || output_acl->type() != MatrixType::BGRPlanar) {
        LOG(INFO) << "input must be nv12 and out must be BGRPlanar; input " << input_acl->typestr()
                  << " output " << output_acl->typestr();
        return DG_ERR_INVALID_IMAGE;
    }
    if((uint32_t)input->stride().width>max_width_||(u_int32_t)input->stride().height>max_height_){
        LOG(ERROR)<<"input stride "<<input->stride()<<",but max height "<<max_height_
        <<",max width"<<max_width_;
        return DG_ERR_INVALID_IMAGE;
    }

    AclMemDvppSP memBgrOut;
    auto ret=aippCrop(input_acl, memBgrOut,param,stream);
    if(ret!=DG_OK){
        LOG(ERROR)<<"aippCrop is failed.";
        return DG_ERR_HW_FAILURE;
    }

    cv::Size corpSize(input->roif().width,input->roif().height);
    cv::Size pastSize(output->roif().width,output->roif().height);
    AclImageSP pastMat;
    AclMemDvppSP memPast = std::make_shared<AclMemDvpp>();
    //unsigned int memPastSize=max_width_*max_height_*3*sizeof(float);
    unsigned int memPastSize=pastSize.width*pastSize.height*3*sizeof(float);
    ret=memPast->create(memPastSize, sizeof(float));
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to create AclMemDvpp! size "<<memPastSize;
        return DG_ERR_HW_FAILURE;
    }
    pastMat=std::make_shared<AclImage>();
    ret = pastMat->create(MatrixType::BGRPlanar, pastSize,memPast,1,pastSize.width,pastSize.height,VegaDataType::FLOAT);
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to create AclImage! size "<<pastSize;
        return DG_ERR_HW_FAILURE;
    }
    ret=ResizeInfer(corpSize,pastSize,memBgrOut->blob(),memBgrOut->size(),memPast->blob(),memPastSize,stream);
    if(ret!=DG_OK){
        LOG(ERROR)<<"Fail to ResizeInfer ";
        return DG_ERR_HW_FAILURE;
    }
    aiCpuPast(pastMat,output_acl,stream);
    return DG_OK;
}
DgError AicoreOp::SetAippInfo(aclmdlAIPP *mdlAipp,int32_t srcImageSizeW, int32_t srcImageSizeH,int32_t cropStartPosW,
    int32_t cropStartPosH,int32_t cropSizeW, int32_t cropSizeH,int32_t paddingSizeTop, int32_t paddingSizeBottom,
    int32_t paddingSizeLeft, int32_t paddingSizeRight,int16_t dtcPixelMeanChn0,int16_t dtcPixelMeanChn1,
    int16_t dtcPixelMeanChn2,float scale){
    int8_t cscSwitch=true;
    int16_t cscMatrixR0C0=256;
    int16_t cscMatrixR0C1=454;
    int16_t cscMatrixR0C2=0;
    int16_t cscMatrixR1C0=256;
    int16_t cscMatrixR1C1=-88;
    int16_t cscMatrixR1C2=-183;
    int16_t cscMatrixR2C0=256;
    int16_t cscMatrixR2C1=0;
    int16_t cscMatrixR2C2=359;
    uint8_t cscOutputBiasR0=0;
    uint8_t cscOutputBiasR1=0;
    uint8_t cscOutputBiasR2=0;
    uint8_t cscInputBiasR0=0;
    uint8_t cscInputBiasR1=128;
    uint8_t cscInputBiasR2=128;
    aclAippInputFormat inputFormat=ACL_YUV420SP_U8;
    int8_t rbuvSwapSwitch=false;
    aclError ret=aclmdlSetAIPPInputFormat(mdlAipp, inputFormat);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPInputFormat! ret "<<ret;
        return DG_ERR_HW_FAILURE;
    }
    ret=aclmdlSetAIPPCscParams(mdlAipp, cscSwitch,cscMatrixR0C0,
    cscMatrixR0C1, cscMatrixR0C2,cscMatrixR1C0, cscMatrixR1C1, cscMatrixR1C2,
    cscMatrixR2C0, cscMatrixR2C1, cscMatrixR2C2,cscOutputBiasR0, cscOutputBiasR1,cscOutputBiasR2,
    cscInputBiasR0, cscInputBiasR1, cscInputBiasR2);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPCscParams! ret= "<<ret;
        return DG_ERR_HW_FAILURE;
    }
    ret=aclmdlSetAIPPRbuvSwapSwitch(mdlAipp, rbuvSwapSwitch);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPRbuvSwapSwitch!";
        return DG_ERR_HW_FAILURE;
    }
    ret=aclmdlSetAIPPSrcImageSize(mdlAipp, srcImageSizeW, srcImageSizeH);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPSrcImageSize!";
        return DG_ERR_HW_FAILURE;
    }
    int8_t cropSwitch=0;
    if(cropSizeW>0)
        cropSwitch=1;
    ret=aclmdlSetAIPPCropParams(mdlAipp, cropSwitch,cropStartPosW,cropStartPosH,cropSizeW,cropSizeH,0);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPCropParams!";
        return DG_ERR_HW_FAILURE;
    }
    int8_t paddingSwitch=0;
    ret=aclmdlSetAIPPPaddingParams(mdlAipp, paddingSwitch,paddingSizeTop, paddingSizeBottom,paddingSizeLeft,
    paddingSizeRight,0);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPPaddingParams!";
        return DG_ERR_HW_FAILURE;
    }
    ret=aclmdlSetAIPPDtcPixelMean(mdlAipp,dtcPixelMeanChn0,dtcPixelMeanChn1,dtcPixelMeanChn2,0,0);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPDtcPixelMean!";
        return DG_ERR_HW_FAILURE;
    }
    float dtcPixelVarReciChn0=scale;
    float dtcPixelVarReciChn1=scale;
    float dtcPixelVarReciChn2=scale;
    ret=aclmdlSetAIPPPixelVarReci(mdlAipp,dtcPixelVarReciChn0,dtcPixelVarReciChn1,dtcPixelVarReciChn2,1.0,0);
    if(ret!=ACL_SUCCESS){
        LOG(ERROR) << "Fail to aclmdlSetAIPPPixelVarReci!";
        return DG_ERR_HW_FAILURE;
    }
    return DG_OK;
}
DgError AicoreOp::ResizeInfer(cv::Size inputSize,
                               cv::Size outputSize,
                               void *inputBuf,
                               int inputBufSize,
                               void *outputBuf,
                               int32_t outputBufSize,
                               aclrtStream stream) {
    OperatorDesc opDesc = CreateOpResizeBilinearV2Desc(inputSize, outputSize);

    size_t numInputs = opDesc.inputDesc.size();
    size_t numOutputs = opDesc.outputDesc.size();
    std::vector<aclDataBuffer *> inputBuffers;
    std::vector<aclDataBuffer *> outputBuffers;
    std::shared_ptr<std::vector<aclDataBuffer *>> autoFree = std::shared_ptr<std::vector<aclDataBuffer *>>(
      &inputBuffers, [=](void *) { FreeAclDataBuf(inputBuffers, outputBuffers); });
    inputBuffers.emplace_back(aclCreateDataBuffer(inputBuf, inputBufSize));//x
    AclMemDvppSP mem = std::make_shared<AclMemDvpp>();
    int32_t sizesBuf[2];
    sizesBuf[0] = outputSize.height;
    sizesBuf[1] = outputSize.width;
    mem->create(sizeof(sizesBuf), sizeof(float));
    auto r = aclrtMemcpy(mem->blob(), sizeof(sizesBuf), sizesBuf, sizeof(sizesBuf), ACL_MEMCPY_HOST_TO_DEVICE);
    if (r != ACL_SUCCESS) {
        LOG(ERROR) << "Fail to aclrtMemcpy!";
        return DG_ERR_HW_FAILURE;
    }
    inputBuffers.emplace_back(aclCreateDataBuffer(mem->blob(), sizeof(sizesBuf)));//size
    outputBuffers.emplace_back(aclCreateDataBuffer(outputBuf, outputBufSize));
    auto ret = aclopExecuteV2(opResizeBilinearV2_.c_str(),
                              numInputs,
                              opDesc.inputDesc.data(),
                              inputBuffers.data(),
                              numOutputs,
                              opDesc.outputDesc.data(),
                              outputBuffers.data(),
                              opDesc.opAttr,
                              stream);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << "Fail to aclopExecuteV2 " << opResizeBilinearV2_ << "; error code is " << ret;
        return DG_ERR_HW_FAILURE;
    }
    ret = aclrtSynchronizeStream(stream);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR) << " Failed to aclrtSynchronizeStream ,ret " << ret;
        return DG_ERR_HW_FAILURE;
    }
    return DG_OK;
}
aclmdlDataset *AicoreOp::CreateAndFillDataset(void *bufs, size_t sizes) {
    aclError ret = ACL_ERROR_NONE;
    aclmdlDataset *dataset = aclmdlCreateDataset();
    if (dataset == nullptr) {
        LOG(ERROR) << "ACL_ModelInputCreate failed.";
        return nullptr;
    }
    aclDataBuffer *data = aclCreateDataBuffer(bufs, sizes);
    if (data == nullptr) {
        DestroyDataset(dataset);
        LOG(ERROR) << "aclCreateDataBuffer failed.";
        return nullptr;
    }
    ret = aclmdlAddDatasetBuffer(dataset, data);
    if (ret != ACL_ERROR_NONE) {
        aclDestroyDataBuffer(data);
        DestroyDataset(dataset);
        LOG(ERROR) << "ACL_ModelInputDataAdd failed, ret= " << ret;
        return nullptr;
    }
    return dataset;
}
void AicoreOp::DestroyDataset(aclmdlDataset *dataset) {
    if (dataset != nullptr) {
        for (size_t i = 0; i < aclmdlGetDatasetNumBuffers(dataset); i++) {
            aclDataBuffer *dataBuffer = aclmdlGetDatasetBuffer(dataset, i);
            if (dataBuffer != nullptr) {
                aclDestroyDataBuffer(dataBuffer);
                dataBuffer = nullptr;
            }
        }
        aclmdlDestroyDataset(dataset);
        dataset = nullptr;
    }
}

DgError AicoreOp::aippInfer(aclmdlAIPP *aippParmsSet,uint32_t input_size, void *input_data,uint32_t output_size, void *output_data,
    aclrtStream stream){
    std::shared_ptr<aclmdlDataset> input = std::shared_ptr<aclmdlDataset>(
    CreateAndFillDataset(input_data, input_size),[=](aclmdlDataset *data){});

    aclDataBuffer* inputData = aclCreateDataBuffer(input_data, input_size);
    auto ret = aclmdlAddDatasetBuffer(input.get(), inputData);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR)<<" Failed to aclmdlAddDatasetBuffer, ret "<<ret;
        return DG_ERR_HW_FAILURE;
    }


    std::shared_ptr<aclmdlDataset> output = std::shared_ptr<aclmdlDataset>(
    CreateAndFillDataset(output_data, output_size),[=](aclmdlDataset *data){});

    ret = aclmdlSetInputAIPP(aipp_modelId_,input.get(), aipp_index_, aippParmsSet);
    if (ret != ACL_SUCCESS) {
        LOG(FATAL)<<" Failed to aclmdlSetInputAIPP, ret "<<ret;
        return DG_ERR_HW_FAILURE;
    }
 
    ret = aclmdlExecuteAsync(aipp_modelId_, input.get(), output.get(),stream);
    if (ret != ACL_SUCCESS) {
        LOG(ERROR)<<" Failed to aclmdlExecuteAsync, ret "<<ret;
        return DG_ERR_HW_FAILURE;
    }
    ret = aclrtSynchronizeStream(stream);
    if (ret != ACL_SUCCESS){
        LOG(ERROR) << " Failed to aclrtSynchronizeStream ,ret "<<ret;
        return DG_ERR_HW_FAILURE;
    }
    for (auto i = 0u; i < aclmdlGetDatasetNumBuffers(input.get()); ++i) {
        aclDataBuffer *dataBuffer = aclmdlGetDatasetBuffer(input.get(), i);
        auto ret = aclDestroyDataBuffer(dataBuffer);
        if (ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "destroy datebuffer input " << i << " failed,  errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
    }
    ret = aclmdlDestroyDataset(input.get());
    if (ret != ACL_ERROR_NONE) {
        LOG(ERROR) << "destroy dateset input failed,  errorCode is  " << static_cast<int32_t>(ret);
        return DG_ERR_HW_FAILURE;
    }

    // destory output databuffer and dataset
    for (auto i = 0u; i < aclmdlGetDatasetNumBuffers(output.get()); ++i) {
        aclDataBuffer *dataBuffer = aclmdlGetDatasetBuffer(output.get(), i);
        ret = aclDestroyDataBuffer(dataBuffer);
        if (ret != ACL_ERROR_NONE) {
            LOG(ERROR) << "destroy datebuffer output " << i << " failed,  errorCode is  " << static_cast<int32_t>(ret);
            return DG_ERR_HW_FAILURE;
        }
    }
    ret = aclmdlDestroyDataset(output.get());
    if (ret != ACL_ERROR_NONE) {
        LOG(ERROR) << "destroy dateset output failed,  errorCode is  " << static_cast<int32_t>(ret);
        return DG_ERR_HW_FAILURE;
    }
    return DG_OK;
}

}  // namespace acl
}  // namespace vega
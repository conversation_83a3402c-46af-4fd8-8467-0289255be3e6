/*
 * Copyright(C) 2022. Deepglint Technologies Co.,Ltd. All rights reserved.
 *  by <PERSON><PERSON><PERSON><PERSON><PERSON>@deepglint.com
 */
#ifndef VEGA_ACL_OP_ENGINE_H_
#define VEGA_ACL_OP_ENGINE_H_
#include <cstdio>
#include <vector>
#include "acl/acl.h"
#include "acl/srv/acl_op_execute.h"
#include "vega_sdk_config.h"
#include "utils/mem_host_device_pair.h"
namespace vega {
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        class AclOpProcess{
        public:
            typedef enum{
                warpa_ffine=0,
                warpa_perspective=1
            }TransType;
        private:
            typedef struct {
                unsigned long long addr;
                unsigned int w;
                unsigned int h;
                unsigned int type;
            } VegaOpticalFlowImg;
            typedef struct VegaOpticalFlowParam  // 光流需要的参数
            {
                double pyr_scale;   // 图像尺度变化比率
                int levels;         // 图像金字塔层数
                int winsize;        // 平滑窗口尺寸
                int iterations;     // 迭代次数
                int poly_n;         // 像素邻域大小，用于做多项式拟合
                double poly_sigma;  // 像素邻域标准差，用于做多项式拟合
                int flags;          // 其他参数控制标志
            } VegaOpticalFlowParam;
            typedef struct {
            public:
                VegaOpticalFlowImg prev;  // CV_8UC3
                VegaOpticalFlowImg next;
                VegaOpticalFlowImg flow;  // CV_32FC2
                VegaOpticalFlowParam param;
                static void cvMat2VegaOpticalFlowImg(VegaOpticalFlowImg &img, cv::Mat &mat) {
                    img.addr = reinterpret_cast<unsigned long long>(mat.data);
                    img.w    = mat.cols;
                    img.h    = mat.rows;
                    img.type = mat.type();
                }
            } VegaOpticalFlowInput;
            typedef struct {
            public:
                VegaOpticalFlowImg prev;  // CV_8UC1
                VegaOpticalFlowImg next;
                VegaOpticalFlowImg flow;  // CV_8UC1
                static void cvMat2VegaOpticalFlowImg(VegaOpticalFlowImg &img, cv::Mat &mat) {
                    img.addr = reinterpret_cast<unsigned long long>(mat.data);
                    img.w    = mat.cols;
                    img.h    = mat.rows;
                    img.type = mat.type();
                }
            } VegaAbsDiffInput;

            typedef struct {
                unsigned long long addr;
                unsigned  int w;
                unsigned  int h;
                unsigned  int s_w;
                unsigned  int s_h;
                unsigned  int roi_x;
                unsigned  int roi_y;
                unsigned  int roi_w;
                unsigned  int roi_h;
                unsigned  int type;
            }VegaOpMatrix;

            typedef struct {
                VegaOpMatrix in;
                VegaOpMatrix out;
                float mean[3];
                float scale;
            }VegaCropResizeParam;     

            typedef struct {
                VegaOpMatrix in;
                VegaOpMatrix out;
                unsigned  int trans_m_size_w;
                unsigned  int trans_m_size_h;
                unsigned  int type;
                float trans_m[9];
            }VegaTransParam;

            void matrixToOpParam( AclImageSP input_acl,VegaOpMatrix &in){
                in.addr=(unsigned long long)input_acl->data();
                in.s_w=input_acl->stride().width;
                in.s_h=input_acl->stride().height;
                in.w=input_acl->size().width;
                in.h=input_acl->size().height;
                in.roi_x=input_acl->roi().x;
                in.roi_y=input_acl->roi().y;
                in.roi_w=input_acl->roi().width;
                in.roi_h=input_acl->roi().height;
                in.type=(unsigned int)input_acl->type();
            }
            // void m=ToOpParamForOpticalFlow(AclImageSP input_acl, VegaOpticalFlowImg &in) {
            //     in.addr  = (unsigned long long)input_acl->data();
            //     in.w=input_acl->size().width;
            //     in.h=input_acl->size().height;
            //     in.type = input_acl->mat().type(); // opencv type
            // }

        private :  
        DgError executeOp(std::vector<int64_t> param_shape , void *param_data,std::string op_type,bool first_run){
            auto aclOpExecute=AclOpExecute::pop();
            auto ret=aclOpExecute->executeOp(param_shape,param_data,op_type,first_run);//first compile the aclop
            AclOpExecute::push(aclOpExecute);
             return ret;
        }
        public:
            AclOpProcess() {
                sdk_config::Platform platform=vegaGetPlatform();
                int engine_num = sdk_config::get_cfg<int>("aicpu_engine_num",3,platform);
                poolCropResizeParam_= std::make_shared<MemHostDevicePool<unsigned char>>(engine_num, sizeof(VegaCropResizeParam)
                                        +sizeof(unsigned long long),true,true);
                poolTransParam_= std::make_shared<MemHostDevicePool<unsigned char>>(engine_num, sizeof(VegaTransParam)
                                        +sizeof(unsigned long long),true,true);
                poolOpticalFlowParam_= std::make_shared<MemHostDevicePool<unsigned char>>(
                  engine_num, sizeof(VegaOpticalFlowInput) + sizeof(unsigned long long), true, true);
                poolAbsDiffParam_= std::make_shared<MemHostDevicePool<unsigned char>>(
                  engine_num, sizeof(VegaAbsDiffInput) + sizeof(unsigned long long), true, true);                
                transOp="VegaTransform";
                cropResizeOp="VegaCropResize";
                opticalFlowOp="VegaOpticalFlow";
                absDiffOp = "VegaAbsDiff";
            }
            ~AclOpProcess() {
            }
            DgError aclopCompile(){
                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};
                MemHostDevicePool<unsigned char>::MemPairSP inParam=poolCropResizeParam_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(inParam->host,[&](void *data){
                    poolCropResizeParam_->push(inParam);
                });
                memset(inParam->host,0,inParam->size);
                if(DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }

                auto ret=executeOp(param_shape , inParam->device->blob(),cropResizeOp,true);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to executeOp "<<cropResizeOp;
                }

                MemHostDevicePool<unsigned char>::MemPairSP inParam2=poolTransParam_->pop();
                if(!inParam2){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable2=std::shared_ptr<DG_U8>(inParam2->host,[&](void *data){
                    poolTransParam_->push(inParam2);
                });
                memset(inParam2->host,0,inParam2->size);
                 if(DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam2)){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                ret=executeOp(param_shape , inParam2->device->blob(),transOp,true);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to executeOp "<<transOp;
                }
                return ret;
            }
            DgError cropResize( MatrixSP &input,MatrixSP &output,float *mean, float scale, aclrtStream stream){
                AclImageSP output_acl=std::dynamic_pointer_cast<AclImage>(output);
                if(!output_acl){
                    LOG(INFO)<<"output is'nt AclImageSP!";
                   return DG_ERR_INVALID_IMAGE;
                }
                AclImageSP input_acl=std::dynamic_pointer_cast<AclImage>(input);
                if(!input_acl){
                    LOG(INFO)<<"input_acl is'nt AclImageSP!";
                    return DG_ERR_INVALID_IMAGE;
                }
                if (input_acl->type() == MatrixType::BGRPlanar && output_acl->type() == MatrixType::BGRPlanar) {
                    if (input->roi().width != output->roi().width || input->roi().height != output->roi().height) {
                        LOG(INFO) << "only crop and past in BGRPlanar";
                        return DG_ERR_INVALID_IMAGE;
                    }
                } else {
                    if ((input_acl->type() != MatrixType::BGRPacked && input_acl->type() != MatrixType::NV12)
                        || output_acl->type() != MatrixType::BGRPacked) {
                        LOGFULL << "input must be bgrpacked or nv12 and out must be bgrpacked; input "
                                  << input_acl->typestr() << " output " << output_acl->typestr();
                        return DG_ERR_INVALID_IMAGE;
                    }
                    if (output_acl->property().dataType() != VegaDataType::CHAR) {
                        LOG(ERROR) << "output data type must be char!";
                        return DG_ERR_INVALID_IMAGE;
                    }
                }
                
                MemHostDevicePool<unsigned char>::MemPairSP inParam=poolCropResizeParam_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(inParam->host,[&](void *data){
                    poolCropResizeParam_->push(inParam);
                });
                memset(inParam->host,0,inParam->size);
                DG_U8 *ptr=inParam->host;
                unsigned long long *param_addr=(unsigned long long *)ptr;
                *param_addr=(unsigned long long)inParam->device->blob()+sizeof(unsigned long long);
                ptr+=sizeof(unsigned long long);
                VegaCropResizeParam  *param=(VegaCropResizeParam *)ptr;
                matrixToOpParam(input_acl,param->in);
                matrixToOpParam(output_acl,param->out);
                param->mean[0]=mean[0];
                param->mean[1]=mean[1];
                param->mean[2]=mean[2];
                param->scale=scale;
                if(DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};
                auto ret=executeOp(param_shape , inParam->device->blob(),cropResizeOp,false);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Fail to executeOp "<<cropResizeOp;
                }
                return ret;
            }
            DgError transformOP(MatrixSP input,MatrixSP output, cv::Mat m,aclrtStream stream,TransType transType) {
                AclImageSP output_acl=std::dynamic_pointer_cast<AclImage>(output);
                if(!output_acl){
                    LOG(ERROR)<<"output is'nt AclImageSP!";
                   return DG_ERR_INVALID_IMAGE;
                }
                AclImageSP input_acl=std::dynamic_pointer_cast<AclImage>(input);
                if(!input_acl){
                    LOG(ERROR)<<"input_acl is'nt AclImageSP!";
                    return DG_ERR_INVALID_IMAGE;
                }
                if((input_acl->type()!=MatrixType::BGRPacked && input_acl->type()!=MatrixType::NV12)
                ||output_acl->type()!=MatrixType::BGRPacked){
                    LOG(ERROR)<<"input must be bgrpacked or nv12,output must be bgrpacked,but input "<<input_acl->typestr()
                    << " output "<<output_acl->typestr();
                    return DG_ERR_INVALID_IMAGE;
                }
                if(output_acl->property().dataType() != VegaDataType::CHAR){
                    LOG(ERROR)<<"output data type must be char!";
                    return DG_ERR_INVALID_IMAGE;
                }
                if(m.type()!=CV_32FC1){
                   LOG(ERROR)<<"trans mat must be CV_32FC1,but  "<<m.type();
                    return DG_ERR_INVALID_IMAGE;
                }

                MemHostDevicePool<unsigned char>::MemPairSP inParam=poolTransParam_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(inParam->host,[&](void *data){
                    poolTransParam_->push(inParam);
                });
                memset(inParam->host,0,inParam->size);
                DG_U8 *ptr=inParam->host;
                unsigned long long *param_addr=(unsigned long long *)ptr;
                *param_addr=(unsigned long long)inParam->device->blob()+sizeof(unsigned long long);
                ptr+=sizeof(unsigned long long);
                VegaTransParam  *param=(VegaTransParam *)ptr;
                matrixToOpParam(input_acl,param->in);
                matrixToOpParam(output_acl,param->out);
                param->trans_m_size_w=m.cols;
                param->trans_m_size_h=m.rows;
                param->type=(unsigned int )transType;
                int mSize=m.size().area();
                if(transType == warpa_ffine){
                    if(mSize!=6){
                        LOG(ERROR)<<"warpa_ffine mat size must be 6,but  "<<mSize;
                        return DG_ERR_INVALID_PARAM;
                    }
                }else if(transType == warpa_perspective){
                    if(mSize!=9){
                        LOG(ERROR)<<"warpa_perspective mat size must be 9,but  "<<mSize;
                        return DG_ERR_INVALID_PARAM;
                    }
                }else{
                    LOG(ERROR)<<"Not support this transform type "<<transType;
                    return DG_ERR_INVALID_PARAM;
                }
                memcpy(param->trans_m,m.data,mSize*sizeof(float));

                if(DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)){
                    LOG(ERROR)<<"Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                std::vector<int64_t>   param_shape={1,sizeof(unsigned long long)};

                auto ret=executeOp(param_shape , inParam->device->blob(),transOp,false);
                if(DG_OK != ret){
                    LOG(ERROR)<<"Transf M: "<<param->trans_m[0]<<" "<<param->trans_m[1]<<" "<<param->trans_m[2]<<" "<<param->trans_m[3]<<" "<<param->trans_m[4]
                    <<" "<<param->trans_m[5]<<" "<<param->trans_m[6]<<" "<<param->trans_m[7]<<" "<<param->trans_m[8]
                    <<" trans_m_size_w "<<param->trans_m_size_w<<" trans_m_size_h "<<param->trans_m_size_h<<" type "<<param->type;
                    LOG(ERROR)<<"Transf in: w :"<<param->in.w<<" h "<<param->in.h<<" sw "<<param->in.s_w<<" sh "<<param->in.s_h<<" roix "<<param->in.roi_x
                    <<" roiy "<<param->in.roi_y<<" roiw "<<param->in.roi_w<<" roih "<<param->in.roi_h<<" tpye "<<param->in.type;
                    LOG(ERROR)<<"Transf out: w :"<<param->out.w<<" h "<<param->out.h<<" sw "<<param->out.s_w<<" sh "<<param->out.s_h<<" roix "<<param->out.roi_x
                    <<" roiy "<<param->out.roi_y<<" roiw "<<param->out.roi_w<<" roih "<<param->out.roi_h<<" tpye "<<param->out.type;
                    LOG(FATAL)<<"Fail to executeOp "<<transOp<<" input "<<input_acl->typestr()<< " output "<<output_acl->typestr();
                }
                return ret;
            }
            DgError opticalFlow(MatrixSP prev,MatrixSP next,AclMemDvppSP flow,OpticalFlowInput inputParam, aclrtStream stream) {
                // 1. check if the input and output is on the device
                AclImageSP prevAcl = std::dynamic_pointer_cast<AclImage>(prev);
                if (!prevAcl) {
                    LOG(INFO) << "prev is'nt AclImageSP!";
                    return DG_ERR_INVALID_IMAGE;
                }
                AclImageSP nextAcl = std::dynamic_pointer_cast<AclImage>(next);
                if (!nextAcl) {
                    LOG(INFO) << "next is'nt AclImageSP!";
                    return DG_ERR_INVALID_IMAGE;
                }
                // 2. set om's input parameter
                MemHostDevicePool<unsigned char>::MemPairSP inParam=poolOpticalFlowParam_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(inParam->host,[&](void *data){
                    poolOpticalFlowParam_->push(inParam);
                });
                memset(inParam->host,0,inParam->size);
                DG_U8 *ptr=inParam->host;
                unsigned long long *param_addr=(unsigned long long *)ptr;
                *param_addr=(unsigned long long)inParam->device->blob()+sizeof(unsigned long long);
                ptr+=sizeof(unsigned long long);
                VegaOpticalFlowInput  *param=(VegaOpticalFlowInput *)ptr;
                param->param.pyr_scale     = inputParam.pyr_scale;
                param->param.levels = inputParam.levels;
                param->param.winsize = inputParam.winsize;
                param->param.iterations = inputParam.iterations;
                param->param.poly_n = inputParam.poly_n;
                param->param.poly_sigma = inputParam.poly_sigma;
                param->param.flags = inputParam.flags;

                auto width = prev->stride().width;
                auto height = prev->stride().height;
                // prev
                param->prev.addr = reinterpret_cast<unsigned long long>(prev->data());
                param->prev.w = width;
                param->prev.h = height;
                param->prev.type = CV_MAKETYPE(CV_8U, 1); // CV_8UC1
                // next
                param->next.addr = reinterpret_cast<unsigned long long>(next->data());
                param->next.w    = width;
                param->next.h    = height;
                param->next.type = CV_MAKETYPE(CV_8U, 1);
                // flow
                param->flow.addr = reinterpret_cast<unsigned long long>(flow->blob());
                param->flow.w = width;
                param->flow.h = height;
                param->flow.type = CV_MAKETYPE(CV_32F, 2); // CV_32FC2

                // 3. execute
                if (DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)) {
                    LOG(ERROR) << "Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                std::vector<int64_t> param_shape = { 1, sizeof(unsigned long long) };
                auto ret                         = executeOp(param_shape, inParam->device->blob(), opticalFlowOp, false);
                if (DG_OK != ret) {
                    LOG(ERROR) << "Fail to executeOp " << opticalFlowOp;
                }
                return DG_OK;
            }

            DgError absDiff(MatrixSP prev,MatrixSP next,AclMemDvppSP flow, aclrtStream stream) {
                // 1. check if the input and output is on the device
                AclImageSP prevAcl = std::dynamic_pointer_cast<AclImage>(prev);
                if (!prevAcl) {
                    LOG(INFO) << "prev is'nt AclImageSP!";
                    return DG_ERR_INVALID_IMAGE;
                }
                AclImageSP nextAcl = std::dynamic_pointer_cast<AclImage>(next);
                if (!nextAcl) {
                    LOG(INFO) << "next is'nt AclImageSP!";
                    return DG_ERR_INVALID_IMAGE;
                }
                // 2. set om's input parameter
                MemHostDevicePool<unsigned char>::MemPairSP inParam=poolAbsDiffParam_->pop();
                if(!inParam){
                    LOG(FATAL) << "Fail to pop MemHostDevicePool";
                    return DG_ERR_HW_FAILURE;
                }
                std::shared_ptr<DG_U8> autoFreeVariable=std::shared_ptr<DG_U8>(inParam->host,[&](void *data){
                    poolAbsDiffParam_->push(inParam);
                });
                memset(inParam->host,0,inParam->size);
                DG_U8 *ptr=inParam->host;
                unsigned long long *param_addr=(unsigned long long *)ptr;
                *param_addr=(unsigned long long)inParam->device->blob()+sizeof(unsigned long long);
                ptr+=sizeof(unsigned long long);
                auto width = prev->stride().width;
                auto height = prev->stride().height;
                VegaAbsDiffInput  *param=(VegaAbsDiffInput *)ptr;
                // prev
                param->prev.addr = reinterpret_cast<unsigned long long>(prev->data());
                param->prev.w = width;
                param->prev.h = height;
                param->prev.type = CV_MAKETYPE(CV_8U, 1); // CV_8UC1
                // next
                param->next.addr = reinterpret_cast<unsigned long long>(next->data());
                param->next.w    = width;
                param->next.h    = height;
                param->next.type = CV_MAKETYPE(CV_8U, 1);
                // flow
                param->flow.addr = reinterpret_cast<unsigned long long>(flow->blob());
                param->flow.w = width;
                param->flow.h = height;
                param->flow.type = CV_MAKETYPE(CV_8U, 1); // CV_8UC1

                // 3. execute
                if (DG_OK != MemHostDevicePool<unsigned char>::SynHostToDevice(inParam)) {
                    LOG(ERROR) << "Fail to SynHostToDevice!";
                    return DG_ERR_HW_FAILURE;
                }
                std::vector<int64_t> param_shape = { 1, sizeof(unsigned long long) };
                auto ret                         = executeOp(param_shape, inParam->device->blob(), absDiffOp, false);
                if (DG_OK != ret) {
                    LOG(ERROR) << "Fail to executeOp " << absDiffOp;
                }
                return DG_OK;
            }

            private:
            std::shared_ptr<MemHostDevicePool<unsigned char>> poolCropResizeParam_;
            std::shared_ptr<MemHostDevicePool<unsigned char>> poolTransParam_;
            std::shared_ptr<MemHostDevicePool<unsigned char>> poolOpticalFlowParam_;
            std::shared_ptr<MemHostDevicePool<unsigned char>> poolAbsDiffParam_;
            std::string transOp;
            std::string cropResizeOp;
            std::string opticalFlowOp;
            std::string absDiffOp;
            int buffer_pool_size_=12;
        };

        class AclOpEngine : public ImageEngine {
        public:
            AclOpEngine():ImageEngine(0, "AclOp") {
                aclOpProcess_=std::make_shared<AclOpProcess>();
                aclOpProcess_->aclopCompile();
            }
            ~AclOpEngine(){
            }
            DgError process(VegaMatrixCmd cmd,
                            std::vector<bool> &marks,
                            MatrixSPV &inputs,
                            MatrixSPV &outputs,
                            VegaStream stream,
                            ProcessMatrixParam &param) override {
                if(cmd!=VegaMatrixCmd::CROP_RESIZE && cmd!=VegaMatrixCmd::WARP_AFFINE && cmd!=VegaMatrixCmd::WARP_PERSPECTIVE){
                    return DG_ERR_NOT_SUPPORTED;
                }
                 DgError ret = DG_OK;
                int cnt = 0;
                for(auto i = 0u; i < marks.size(); i++) {
                    if(!marks[i]) cnt++;
                }
                inc(cnt);
                for(auto i=0u; i < inputs.size();i++){
                    DgError err;
                    if(marks[i]){
                        continue;
                    }
                    switch(cmd) {
                        case VegaMatrixCmd::CROP_RESIZE: {
                            err = aclOpProcess_->cropResize(inputs[i], outputs[i],param.meanptr(), param.scale(),aclrtStream(stream));
                            break;
                        }
                        case VegaMatrixCmd::WARP_AFFINE: {
                            cv::Mat warpMat;
                            if(param.M().size() > i) {
                                warpMat = param.M(i);
                                err = aclOpProcess_->transformOP(inputs[i], outputs[i], warpMat,aclrtStream(stream),AclOpProcess::warpa_ffine);
                            }else{
                                LOG(ERROR) << "warpAffine without a warp matrix";
                                err = DG_ERR_INVALID_PARAM;
                            }
                            break;
                        }
                        case VegaMatrixCmd::WARP_PERSPECTIVE: {
                            if(param.M().size() > i) {
                                err = aclOpProcess_->transformOP(inputs[i], outputs[i], param.M(i), aclrtStream(stream),AclOpProcess::warpa_perspective);
                            } else {
                                LOG(ERROR) << "WarpPerspective without a warp matrix";
                                err = DG_ERR_INVALID_PARAM;
                            }
                            break;
                        }
                        default:
                            err = DG_ERR_NOT_SUPPORTED;
                            break;
                    }
                    cnt--;
                    dec();

                    if(err == DG_OK) {
                        marks[i] = true;
                        // todo: copy host to device
                    } else if(ret == DG_OK) {
                        ret = err;
                    }
                }
                dec(cnt);
                return ret;
            }
            std::shared_ptr<AclOpProcess> aclOpProcess_;

        };


    }
}
#endif

#include <sys/time.h>
#include <sys/prctl.h>
#include "image_device_encoder.h"

namespace vega {
    namespace acl {

        static auto g_picDescDeleter = [](acldvppPicDesc * const p) { acldvppDestroyPicDesc(p); };
        static auto g_roiCfgDeleter = [](acldvppRoiConfig * const p) { acldvppDestroyRoiConfig(p); };
        static auto g_jpegeCfgDeleter = [](acldvppJpegeConfig * const p) { acldvppDestroyJpegeConfig(p); };
        static auto g_channelDeleter = [](acldvppChannelDesc * const p) { \
                                        acldvppDestroyChannel(p);
                                        acldvppDestroyChannelDesc(p);
                                    };
        AclImageEncoder::AclImageEncoder() {
            // Create channel desc and chn
            for(int i=0;i<8;i++){
                std::shared_ptr<JPG_ENC_CHANNEL> jpgEncChannel=std::shared_ptr<JPG_ENC_CHANNEL>(new JPG_ENC_CHANNEL,
                [=](JPG_ENC_CHANNEL *soure){
                    if(soure->dvppDesc){
                        g_channelDeleter(soure->dvppDesc);
                    }
                    if(soure->jpgDesc){
                        g_channelDeleter(soure->jpgDesc);
                    }
                    if(soure->stream){
                        aclrtDestroyStream(soure->stream);
                    }
                    delete soure;
                });
                jpgEncChannel->dvppDesc=nullptr;
                jpgEncChannel->jpgDesc=nullptr;
                jpgEncChannel->stream=nullptr;
                jpgEncChannlQ_.push(jpgEncChannel);
                jpgEncChannel->dvppDesc= acldvppCreateChannelDesc();
                if (jpgEncChannel->dvppDesc== nullptr) {
                    LOG(ERROR) << "Failed to create channel desc";
                    continue;
                }
#if CANN50
                acldvppSetChannelDescMode(jpgEncChannel->dvppDesc,DVPP_CHNMODE_VPC);
#endif
                aclError ret = acldvppCreateChannel(jpgEncChannel->dvppDesc);
                if (ret != ACL_SUCCESS) {
                    LOG(ERROR) << "Failed to create dvpp channel, ret = " << ret;
                    acldvppDestroyChannelDesc(jpgEncChannel->dvppDesc);
                    jpgEncChannel->dvppDesc=nullptr;
                    continue;
                }
                jpgEncChannel->jpgDesc= acldvppCreateChannelDesc();
                if(jpgEncChannel->jpgDesc == nullptr) {
                    LOG(ERROR) << "Failed to create channel desc";
                    continue;
                }
#if CANN50
                acldvppSetChannelDescMode(jpgEncChannel->jpgDesc,DVPP_CHNMODE_JPEGE);
#endif
                ret = acldvppCreateChannel(jpgEncChannel->jpgDesc);
                if (ret != ACL_SUCCESS) {
                    LOG(ERROR) << "Failed to create dvpp channel, ret = " << ret;
                    acldvppDestroyChannelDesc(jpgEncChannel->jpgDesc);
                    jpgEncChannel->jpgDesc=nullptr;
                }
                // Create stream
                ret = aclrtCreateStream(&jpgEncChannel->stream);
                if (ret != ACL_SUCCESS) {
                    LOG(ERROR) << "Failed to create stream, ret = " << ret;
                    jpgEncChannel->stream=nullptr;
                }
            }
        }
        AclImageEncoder::~AclImageEncoder() {
            }
        DgError AclImageEncoder::JpegEncode(MatrixSP &input, MatrixSP &output, int quality) {
            if (nullptr == input) {
                LOG(ERROR) << "Invalid input matrix";
                return DG_ERR_INVALID_PARAM;
            }
            auto  jpgencChannl=jpgEncChannlQ_.pop();
            std::shared_ptr<aclrtStream> streamPtr=std::shared_ptr<aclrtStream>(&jpgencChannl->stream,[=](void *p){
                jpgEncChannlQ_.push(jpgencChannl);
            });
            if(!jpgencChannl->dvppDesc){
                LOG(ERROR)<<"dvppChannelDesc is null!";
                return DG_ERR_INIT_FAIL;
            }
            if(!jpgencChannl->jpgDesc){
                LOG(ERROR)<<"jpgChannelDesc_ is null!";
                return DG_ERR_INIT_FAIL;
            }
            if(!jpgencChannl->stream){
                LOG(ERROR)<<"aclStream is null!";
                return DG_ERR_INIT_FAIL;
            }

            if (input->type() != MatrixType::NV12) {
                LOG(ERROR) << "Invalid input frame type:" << input->typestr() << ", only support nv12 !!!";
                return DG_ERR_INVALID_PARAM;
            }



            DgError dgErr = CombineJpegeProcess(jpgencChannl->dvppDesc,jpgencChannl->jpgDesc,
                                                jpgencChannl->stream, input, output, quality);
            if(dgErr!=DG_OK){
                LOG(ERROR)<<"Fail to CombineJpegeProcess";
            }
            return dgErr;
        }

        void AclImageEncoder::SetDvppPicDesc(const DvppDataInfo &info, acldvppPicDesc *picDesc) {
            (void)acldvppSetPicDescData(picDesc, info.data);
            (void)acldvppSetPicDescSize(picDesc, info.dataSize);
            (void)acldvppSetPicDescFormat(picDesc, info.format);
            (void)acldvppSetPicDescWidth(picDesc, info.width);
            (void)acldvppSetPicDescHeight(picDesc, info.height);
            (void)acldvppSetPicDescWidthStride(picDesc, info.strideW);
            (void)acldvppSetPicDescHeightStride(picDesc, info.strideH);
        }

        DgError AclImageEncoder::CombineJpegeProcess(acldvppChannelDesc *dvppDesc,
                                                    acldvppChannelDesc *jpgDesc, \
                                                    const aclrtStream &aclStream, \
                                                     const MatrixSP &input, \
                                                     MatrixSP &output, int quality) {
            /*
             * JPEG Encode分辨率: 32*32 ~ 8192*8192
             * 图片格式: YUV420SP(NV12, NV21)
             * widthStride: align(16)
             * heightStride: 等于图片高，或图片高度向上对齐到16数值
             */
            std::shared_ptr<AclMemDvpp> aclMem = std::dynamic_pointer_cast<AclMemDvpp>(input->memory());
            if (aclMem == nullptr) {
                LOG(ERROR) << "input aclMem is nullptr";
                return DG_ERR_NOT_EXIST;
            }

            // stroe the input frame
            DvppDataInfo inputData;
            inputData.width    = (input->size().width+1)/2*2;
            inputData.height   = (input->size().height+1)/2*2;
            inputData.strideW  = input->stride().width;
            inputData.strideH  = input->stride().height;
            inputData.data     = (uint8_t*)aclMem->blob();
            inputData.dataSize = aclMem->size();
            inputData.format   = format_;

            // has roi, need crop for image
            auto cropDataSP = std::make_shared<AclMemDvpp>();
            if (input->hasRoi()) {
                cv::Rect cropRect = input->roi();
                DvppDataInfo outData;
                if (CropImage(inputData, outData, cropDataSP,cropRect, aclStream, dvppDesc) < 0) {
                    LOG(ERROR) << "Failed to crop image";
                    return DG_ERR_VENC_FAIL;
                }

                inputData = outData;
            }

            // create jpeg encode config
            auto jpegeCfg = acldvppCreateJpegeConfig();
            if (jpegeCfg == nullptr) {
                LOG(ERROR) << "Failed to create jpeg config";
                return DG_ERR_VENC_FAIL;
            }
            std::shared_ptr<acldvppJpegeConfig> jpegeCfgSP(jpegeCfg, g_jpegeCfgDeleter);
            auto ret = acldvppSetJpegeConfigLevel(jpegeCfg, quality);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to set encode level, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }

            // create dvpp input pic desc
            acldvppPicDesc *inputDesc = acldvppCreatePicDesc();
            if (inputDesc == nullptr) {
                LOG(ERROR) << "Failed to create pic desc";
                return DG_ERR_VENC_FAIL;
            }
            std::shared_ptr<acldvppPicDesc> inputDescSP(inputDesc, g_picDescDeleter);

            SetDvppPicDesc(inputData, inputDesc);

            // get the buffer size of encode output
            uint32_t outputSize = 0;
            ret = acldvppJpegPredictEncSize(inputDesc, jpegeCfg, &outputSize);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to predict encode size of jpeg image, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }
            auto aclImage=std::make_shared<AclImage>();
            auto erro =aclImage->create(MatrixType::JPEG, cv::Size(outputSize,1));
            if (erro != DG_OK) {
                LOG(ERROR) << "Create jpeg image  mem failed";
                return DG_ERR_VENC_FAIL;
            }

            ret = acldvppJpegEncodeAsync(jpgDesc, inputDesc, aclImage->memory()->blob(), &outputSize, jpegeCfg, aclStream);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to encode image, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }
            ret = aclrtSynchronizeStream(aclStream);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to aclrtSynchronizeStream, ret = " << ret<<",input  inputData.width "<<inputData.width<<" ,inputData.height "<<inputData.height;
                return DG_ERR_VENC_FAIL;
            }
            auto hostImage=std::make_shared<Matrix>();
            erro =hostImage->create(MatrixType::JPEG, cv::Size(outputSize,1));
            if (erro != DG_OK) {
                LOG(ERROR) << "Create jpeg image  mem failed";
                return DG_ERR_VENC_FAIL;
            }
            auto devMem=std::dynamic_pointer_cast<AclMemDvpp>(aclImage->memory());
            erro=devMem->cpToHost(hostImage->memory(),hostImage->memory()->size());
            if(erro != DG_OK){
                LOG(ERROR) << "Fail to cpToHost!";
                return DG_ERR_VENC_FAIL;
            }
		   output=hostImage;	
            output->setStreamId(input->streamId());

            return DG_OK;
        }

        int AclImageEncoder::VpcCrop(const DvppDataInfo &inData, DvppDataInfo &outData,cv::Rect &rect, \
                                     const aclrtStream &aclStream, acldvppChannelDesc *dvppChnDesc) {
            acldvppPicDesc *inDesc = acldvppCreatePicDesc();
            if (inDesc == nullptr) {
                LOG(ERROR) << "Failed to create dvpp input Desc";
                return -1;
            }
            std::shared_ptr<acldvppPicDesc> inDescPtr(inDesc, g_picDescDeleter);

            acldvppPicDesc *outDesc = acldvppCreatePicDesc();
            if (outDesc == nullptr) {
                LOG(ERROR) << "Failed to create dvpp output Desc";
                return -1;
            }
            std::shared_ptr<acldvppPicDesc> outDescSP(outDesc, g_picDescDeleter);

            // set dvpp pic desc info of input image
            SetDvppPicDesc(inData, inDesc);

            // set dvpp pic desc info of output image
            SetDvppPicDesc(outData, outDesc);

            // 宽 = 右偏移 - 左偏移 + 1;
            // 高 = 下偏移 - 上偏移 + 1;
            // 抠图区域分辨率: 10*6 ~ 4096*4096
            uint32_t left   = rect.x;  // 必为偶数
            uint32_t top    = rect.y;  // 必为偶数
            uint32_t right  = rect.x + rect.width - 1;   // 必为奇数
            uint32_t bottom = rect.y + rect.height - 1;  // 必为奇数

            auto cropRioCfg = acldvppCreateRoiConfig(left, right, top, bottom);
            if (cropRioCfg == nullptr) {
                LOG(ERROR) << "create dvpp vpc roi config failed.";
                return -1;
            }
            std::shared_ptr<acldvppRoiConfig> cropRoiCfgSP(cropRioCfg, g_roiCfgDeleter);

            auto ret = acldvppVpcCropAsync(dvppChnDesc, inDesc, outDesc, cropRioCfg, aclStream);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to crop, ret = " << ret;
                return -1;
            }

            ret = aclrtSynchronizeStream(aclStream);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "roi [left, top, right, bottom] -> [" \
                           << left << ", "<< top << ", " << right << ", " << bottom << "]";
                LOG(ERROR) << "Failed to synchronize stream, ret = " << ret;
                return -1;
            }
            return 0;
        }

        int AclImageEncoder::CropImage(const DvppDataInfo &inData, DvppDataInfo &outData, AclMemDvppSP &cropDataSP,cv::Rect &rect, \
                                       const aclrtStream &aclStream, acldvppChannelDesc *dvppChnDesc) {
            int picWidth  = inData.width;
            int picHeight = inData.height;

            if (rect.width < 32 || rect.width > 4096 ||
                rect.height < 32 || rect.height > 4096) {
                LOG(ERROR) << "input roi " << rect.width << "x" << rect.height \
                           << ", crop image size is from 32x32 to 4096x4096";
                return -1;
            }

            if (rect.x < 0 ) rect.x = 0;
            if (rect.y < 0 ) rect.y = 0;
            if (rect.x >= picWidth || rect.y >= picHeight) {
                LOG(ERROR) << "input roi x:" << rect.x << ", y:" << rect.y \
                            << ", w:" << rect.width << ", h:" << rect.height << ", is error";
                return -1;
            }

            rect.x = rect.x & 0xFFFFFFFE;
            rect.y = rect.y & 0xFFFFFFFE;
            rect.width  = DVPP_ALIGN_UP(rect.width, 16);
            rect.height = DVPP_ALIGN_UP(rect.height, 16);

            if ((rect.x + rect.width) > picWidth) {
                rect.width = DVPP_ALIGN_UP((picWidth - rect.x), 16);
                if (rect.width < 32) {
                    rect.width = 32;
                }
                rect.x = picWidth - rect.width;
            }

            if ((rect.y + rect.height) > picHeight) {
                rect.height = DVPP_ALIGN_UP((picHeight - rect.y), 16);
                if (rect.height < 32) {
                    rect.height = 32;
                }
                rect.y = picHeight - rect.height;
            }

            outData.width    = rect.width;
            outData.height   = rect.height;
            outData.strideW  = rect.width;
            outData.strideH  = rect.height;
            outData.format   = format_;
            outData.dataSize = outData.strideW * outData.strideH * YUV_BGR_SIZE_CONVERT_3 / YUV_BGR_SIZE_CONVERT_2;
            auto ret=cropDataSP->create(outData.dataSize);
            if (ret != DG_OK) {
                LOG(ERROR) << "Failed to create AclMemDvpp,size="<<outData.dataSize;
                return -1;
            }
            outData.data=cropDataSP->blob();
            if (VpcCrop(inData, outData, rect, aclStream, dvppChnDesc) < 0) {
                outData.data = nullptr;
                LOG(ERROR) << "Failed to VpcCrop!";
                return -1;
            }

            return 0;
        }
    }
}

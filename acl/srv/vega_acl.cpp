//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 03/18/21.
//

#include "vega_arch.h"
#include "common/Platform/platform_processor.h"
#include "image_engine.h"
#include "processor/image_decoder.h"
#include "acl_model.h"
#include "utils/acl_image.h"
#include "utils/acl_memory.h"
#include "processor/image_encoder.h"
#include "processor/video_decoder.h"
#include "processor/video_encoder.h"
#include "common/Platform/processor/video_decoder_mgr.h"
#include "processor/aicpu/acl_optical_flow_processor.h"
#include "processor/aicpu/acl_abs_diff_processor.h"

namespace vega {
    MatrixSP MatrixHostToDevice(MatrixSP src){
        return PlatformProcess<acl::AclImage,acl::AclMemDvpp>::MatrixHostToDevice(src);
    }
    std::shared_ptr<VegaModel> vegaCreateModel(std::shared_ptr<ModelConfig> config) {
        return vegaCreateModel<acl::AclModel>(config);
    }
    MatrixSP createTaskMatrix(SdkTaskBase *task, RawBuffer &rb, DgError &error) {
        return PlatformProcess<acl::AclImage,acl::AclMemDvpp>::createTaskMatrix(task,rb,error);;
    }

    void callInferProcessorHooker(RpcContext *ctx) {
        PlatformProcess<acl::AclImage,acl::AclMemDvpp>::callInferProcessorHooker(ctx);
    }

    MatrixSP preparePlateRecitfyImage(MatrixType type, const cv::Size &size) {
        return PlatformProcess<acl::AclImage,acl::AclMemDvpp>::preparePlateRecitfyImage(type,size);
    }

    std::vector<MatrixSP> prepareFaceAlignTransformMatrix(const cv::Size &size, int batchSize) {
#if CANN50
        static int use_aicpu_op_engine =-1; 
        if(use_aicpu_op_engine==-1)
            use_aicpu_op_engine=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
        if(use_aicpu_op_engine)
            return PlatformProcess<acl::AclImage,acl::AclMemDvpp>::prepareFaceAlignTransformMatrix(size,batchSize,cv::Size(ACL_IMAGE_ALIGN_W,ACL_IMAGE_ALIGN_H));

#endif
         return PlatformProcess<vega::Matrix,vega::MemBase>::prepareFaceAlignTransformMatrix(size,batchSize);//host memory because use opencv in host
    }

    std::vector<MatrixSP> preparePlateRecogGraphCtx(const cv::Size &rectifySize, int batchSize) {
#if CANN50
        static int use_aicpu_op_engine =-1; 
        if(use_aicpu_op_engine==-1)
            use_aicpu_op_engine=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
        if(use_aicpu_op_engine)
            return PlatformProcess<acl::AclImage,acl::AclMemDvpp>::preparePlateRecogGraphCtx(rectifySize,batchSize);//host memory because use opencv in host
#endif
        return PlatformProcess<vega::Matrix,vega::MemBase>::preparePlateRecogGraphCtx(rectifySize,batchSize);//host memory because use opencv in host
    }

    MatrixSP prepareMatrix(MatrixType type, cv::Size &size, cv::Size stride){
        return PlatformProcess<acl::AclImage,acl::AclMemDvpp>::prepareMatrix(type, size, stride);
    }


    std::shared_ptr<RpcProcessor> createExtraInterface(const ::vega::rpc::InterfaceCtrlRequest &request) {
        std::shared_ptr<RpcProcessor> processor;
        auto &model = request.model_name();
        if (model == Model::decode_video) {
            auto harware = std::make_shared<acl::VideoDecoder>();
            processor = std::make_shared<vega::VideoDecoderMgr>();
            auto mgr=std::dynamic_pointer_cast<vega::VideoDecoderMgr>(processor);
            mgr->push(harware);
        } else if (model == Model::encode_video) {
            processor = std::make_shared<acl::VideoEncoder>();
        } else if (model == Model::decode_frame) {
            processor = std::make_shared<acl::Decoder>();
        } else if (model == Model::fetch_frame) {
            processor = std::make_shared<acl::FetchFrame>();
        } /*else if (model == Model::ai_image) {
            LOG(ERROR) << "not support Model::ai_image";
        }*/else if(model == Model::optical_flow){
#if CANN50
        int use_aicpu_op_engine=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
        if(use_aicpu_op_engine){
            processor = std::make_shared<acl::AclOpticalFlowProcessor>();
        }
#endif
        } else if (model == Model::abs_diff) {
#if CANN50
            int use_aicpu_op_engine = sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
            if (use_aicpu_op_engine) {
                processor = std::make_shared<acl::AclAbsDiffProcessor>();
            }
#endif
        }
        return processor;
    }
}

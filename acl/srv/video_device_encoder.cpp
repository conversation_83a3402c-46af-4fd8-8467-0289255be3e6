#include <sys/time.h>
#include <sys/prctl.h>
#include "video_device_encoder.h"

namespace vega {
    namespace acl {

        static auto g_resizeCfgDeleter = [](acldvppResizeConfig * const p) { acldvppDestroyResizeConfig(p); };
        static auto g_picDescDeleter = [](acldvppPicDesc * const p) { acldvppDestroyPicDesc(p); };

        DgError AclVencProc::Init(const aclrtStream &stream) {
            aclStream_ = stream;
            return DG_OK;
        }

        DgError AclVencProc::CreateVenc(MatrixType from, MatrixType to, const VencBuffer::VencParam &param, VencBuffer::VencCallback cb) {
            if (from != MatrixType::NV12) {
                LOG(ERROR) << "Invalid input frame type:" << (int)from << ", only support nv12";
                return DG_ERR_INVALID_PARAM;
            }
            picFormat_ = PIXEL_FORMAT_YUV_SEMIPLANAR_420;

            switch (to) {
                case MatrixType::H264:
                    enType_ = H264_MAIN_LEVEL;
                    break;
                case MatrixType::H265:
                    enType_ = H265_MAIN_LEVEL;
                    break;
                default :
                    LOG(ERROR) << "Invalid venc stream Format type:" << (int)to << ", only support h264 | h265";
                    return DG_ERR_INVALID_PARAM;
            }

            vencParam_ = param;
            vencCallback_ = cb;
            encSize = vencParam_.size;

            if ((vencParam_.resizeRatio != 1.0f) && !CheckResizeRatio(vencParam_.resizeRatio)) {
                int w = (int)vencParam_.size.width * vencParam_.resizeRatio;
                int h = (int)vencParam_.size.height * vencParam_.resizeRatio;
                encSize.width = DVPP_ALIGN_UP(w, 16);
                encSize.height = DVPP_ALIGN_UP(h, 16);
                LOG(ERROR) << "video " << vencParam_.size.width << "x" << vencParam_.size.height \
                           << " resize to " << encSize.width << "x" << encSize.height \
                           << ", ratio: " << vencParam_.resizeRatio;
            }

            bQuit_ = false;
            // create acl report threadId
            if (pthread_create(&threadId_, nullptr, AclReportThread_, this) != 0) {
                LOG(ERROR) << "Failed to create ACL report thread";
                return DG_ERR_INIT_FAIL;
            }

            auto ret = InitResource((uint64_t)threadId_, picFormat_, enType_);
            if (ret != DG_OK) {
                DestroyVenc();
                return DG_ERR_INIT_FAIL;
            }

            return DG_OK;
        }

        DgError AclVencProc::DestroyVenc(void) {
            bQuit_ = true;
            if(threadId_!=0)
                pthread_join(threadId_, NULL);
            threadId_=0;

            FreeResource();
            return DG_OK;
        }

        DgError AclVencProc::FreeResource(void) {
            if (vencChannelDesc_ != nullptr) {
                (void)aclvencDestroyChannel(vencChannelDesc_);
                (void)aclvencDestroyChannelDesc(vencChannelDesc_);
                vencChannelDesc_ = nullptr;
            }

            if (inputPicputDesc_ != nullptr) {
                (void)acldvppDestroyPicDesc(inputPicputDesc_);
                inputPicputDesc_ = nullptr;
            }

            if (vencFrameConfig_ != nullptr) {
                (void)aclvencDestroyFrameConfig(vencFrameConfig_);
                vencFrameConfig_ = nullptr;
            }

            if (dvppChannelDesc_ != nullptr) {
                (void)acldvppDestroyChannel(dvppChannelDesc_);
                (void)acldvppDestroyChannelDesc(dvppChannelDesc_);
                dvppChannelDesc_ = nullptr;
            }
            return DG_OK;
        }

        void *AclVencProc::AclReportThread_(void *arg) {
            prctl(PR_SET_NAME, "AclVEncReportThread");
            auto aclVdec = static_cast<AclVencProc*>(arg);
            if (aclVdec != nullptr) {
                return aclVdec->AclReportThread();
            }
            return nullptr;
        }

        void *AclVencProc::AclReportThread(void) {
            vegaSetCurrentContext();
            while (!bQuit_) {
                (void)aclrtProcessReport(30);  // 30ms
            }
            return nullptr;
        }

        DgError AclVencProc::InitResource(uint64_t threadId, acldvppPixelFormat format, acldvppStreamFormat enType) {
            vencChannelDesc_ = aclvencCreateChannelDesc();
            if (vencChannelDesc_ == nullptr) {
                LOG(ERROR) << "Fail to create venc channel desc";
                return DG_ERR_INIT_FAIL;
            }

            // set process callback thread
            auto ret = aclvencSetChannelDescThreadId(vencChannelDesc_, threadId);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set threadId, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            // set callback func
            ret = aclvencSetChannelDescCallback(vencChannelDesc_, vencCallback);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc Callback, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            // set output stream type
            ret = aclvencSetChannelDescEnType(vencChannelDesc_, enType);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc EnType, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            // set input picture type
            ret = aclvencSetChannelDescPicFormat(vencChannelDesc_, format);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc PicFormat, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            // set input picture width
            ret = aclvencSetChannelDescPicWidth(vencChannelDesc_, encSize.width);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc PicWidth, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            // set input picture height
            ret = aclvencSetChannelDescPicHeight(vencChannelDesc_, encSize.height);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc PicWidth, ret = "<< ret;
                return DG_ERR_INIT_FAIL;
            }

            // set key frame interval
            ret = aclvencSetChannelDescKeyFrameInterval(vencChannelDesc_, vencParam_.keyFrameInterval);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc FrameInterval, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            // set key frame interval
            // ret = aclvencSetChannelDescMaxBitRate(vencChannelDesc_, vencParam_.bps);
            // if (ret != ACL_ERROR_NONE) {
            //     LOG(ERROR) << "Fail to set venc MaxBitRate, errorCode = " << ret;
            //     return DG_ERR_INIT_FAIL;
            // }

            // create vdec channel
            ret = aclvencCreateChannel(vencChannelDesc_);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to create venc channel, ret = " << ret;
                return DG_ERR_INIT_FAIL;
            }

            vencFrameConfig_ = aclvencCreateFrameConfig();
            if (vencFrameConfig_ == nullptr) {
                LOG(ERROR) << "Fail to create frame config";
                return DG_ERR_INIT_FAIL;
            }

            inputPicputDesc_ = acldvppCreatePicDesc();
            if (inputPicputDesc_ == nullptr) {
                LOG(ERROR) << "Fail to create output pic desc";
                return DG_ERR_INIT_FAIL;
            }

            dvppChannelDesc_ = acldvppCreateChannelDesc();
            if (dvppChannelDesc_ == nullptr) {
                LOG(ERROR) << "Failed to create channel desc";
                return DG_ERR_INIT_FAIL;
            }
#if CANN50
            acldvppSetChannelDescMode(dvppChannelDesc_,DVPP_CHNMODE_VPC);
#endif
            ret = acldvppCreateChannel(dvppChannelDesc_);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to create dvpp channel, ret = " << ret;
                (void)acldvppDestroyChannelDesc(dvppChannelDesc_);
                dvppChannelDesc_ = nullptr;
                return DG_ERR_INIT_FAIL;
            }

            LOG(INFO) << "Acl venc init resource success";
            return DG_OK;
        }

        DgError AclVencProc::VencSendEosFrame(void) {
            aclError ret = aclvencSetFrameConfigEos(vencFrameConfig_, 1);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set eos, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }

            ret = aclvencSendFrame(vencChannelDesc_, nullptr, nullptr, vencFrameConfig_, nullptr);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to send eos frame, errorCode = " << ret;
                return DG_ERR_VENC_FAIL;
            }
            return DG_OK;
        }

        DgError AclVencProc::EncodeVideo(MatrixSP input, int kIntv, bool forceIFrame, float resizeRatio, bool videoEos) {
            if (videoEos) {
                auto ret=VencSendEosFrame();
                return ret;
            }

            if (input == nullptr) {
                LOG(ERROR) << "Invalid input matrix";
                return DG_ERR_INVALID_PARAM;
            }

            if (input->type() != MatrixType::NV12) {
                LOG(ERROR) << "Invalid input frame type, Only surport nv12";
                return DG_ERR_INVALID_PARAM;
            }

            if ((resizeRatio != vencParam_.resizeRatio) && !CheckResizeRatio(resizeRatio)) {
                // send eos to stop current venc
                VencSendEosFrame();
                FreeResource();

                // re-init ven configuration
                int w = (int)vencParam_.size.width * vencParam_.resizeRatio;
                int h = (int)vencParam_.size.height * vencParam_.resizeRatio;
                encSize.width = DVPP_ALIGN_UP(w, 16);
                encSize.height = DVPP_ALIGN_UP(h, 16);
                vencParam_.resizeRatio = resizeRatio;
                if (InitResource((uint64_t)threadId_, picFormat_, enType_) != DG_OK) {
                    return DG_ERR_INIT_FAIL;
                }
            }

            std::shared_ptr<AclMemDvpp> aclMem = std::dynamic_pointer_cast<AclMemDvpp>(input->memory());
            if (aclMem == nullptr) {
                LOG(ERROR) << "Input aclMem is nullptr";
                return DG_ERR_NOT_EXIST;
            }

            void *inBufferDev = (void*)aclMem->blob();
            uint32_t inBufferSize = aclMem->size();
            auto resizeDataSP = std::make_shared<AclMemDvpp>();
            // first resize the input image
            if (vencParam_.resizeRatio != 1.0f && !CheckResizeRatio(resizeRatio)) {
                DvppDataInfo inDataInfo;
                inDataInfo.width    = input->size().width;
                inDataInfo.height   = input->size().height;
                inDataInfo.strideW  = input->stride().width;
                inDataInfo.strideH  = input->stride().height;
                inDataInfo.format   = picFormat_;
                inDataInfo.dataSize = inBufferSize;
                inDataInfo.data     = (uint8_t*)inBufferDev;

                DvppDataInfo outDataInfo;
                outDataInfo.width    = encSize.width;
                outDataInfo.height   = encSize.height;
                outDataInfo.format   = picFormat_;
                outDataInfo.strideW  = DVPP_ALIGN_UP(encSize.width, VPC_STRIDE_WIDTH);
                outDataInfo.strideH  = DVPP_ALIGN_UP(encSize.height, VPC_STRIDE_HEIGHT);
                outDataInfo.dataSize = outDataInfo.strideW * outDataInfo.strideH * YUV_BGR_SIZE_CONVERT_3 / YUV_BGR_SIZE_CONVERT_2;

                // Malloc buffer for output of resize module
                auto err = resizeDataSP->create(outDataInfo.dataSize);
                if (err!= DG_OK) {
                    LOG(ERROR) << "Failed to malloc " << outDataInfo.dataSize << " bytes on dvpp for resize" ;
                    return DG_ERR_VENC_FAIL;
                }
                outDataInfo.data=resizeDataSP->blob();

                if (VpcResize(inDataInfo, outDataInfo) < 0) {
                    return DG_ERR_VENC_FAIL;
                }

                inBufferDev = (void*)outDataInfo.data;
                inBufferSize = outDataInfo.dataSize;
            }

            aclError ret = acldvppSetPicDescData(inputPicputDesc_, inBufferDev);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set PicDescData, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }
            ret = acldvppSetPicDescSize(inputPicputDesc_, inBufferSize);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set PicDescSize, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }

            ret = aclvencSetFrameConfigEos(vencFrameConfig_, 0);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set eos, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }

            ret = aclvencSetFrameConfigForceIFrame(vencFrameConfig_, forceIFrame ? 1 : 0);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to set venc ForceIFrame, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }

            ret = aclvencSendFrame(vencChannelDesc_, inputPicputDesc_, nullptr, vencFrameConfig_, (void*)this);
            if (ret != ACL_ERROR_NONE) {
                LOG(ERROR) << "Fail to aclvencSendFrame, ret = " << ret;
                return DG_ERR_VENC_FAIL;
            }

            return DG_OK;
        }

        void AclVencProc::vencCallback(acldvppPicDesc *input, acldvppStreamDesc *outputStreamDesc, void *userdata) {
            if (outputStreamDesc == nullptr) {
                LOG(ERROR) << "Output is null";
                return;
            }

            // check whether encode success
            uint32_t retCode = acldvppGetStreamDescRetCode(outputStreamDesc);
            if (retCode != 0) {
                LOG(ERROR) << "Venc encode frame failed, retCode = "  << retCode;
                return;
            }

            auto parent = (AclVencProc*)userdata;
            if (parent != nullptr) {
                void *outputDev = acldvppGetStreamDescData(outputStreamDesc);
                uint32_t dataSize = acldvppGetStreamDescSize(outputStreamDesc);
                parent->EncodeDone(outputDev, dataSize);
            }  
        }

        void AclVencProc::EncodeDone(const void *dataDev, uint32_t dataSize) {
            if (dataDev == nullptr || dataSize < 4) {
                vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                return;
            }

            MatrixType type = MatrixType::Undefined;
            switch (enType_) {
            case H264_MAIN_LEVEL: {
                type = MatrixType::H264;
            } break;
            case H265_MAIN_LEVEL: {
                type = MatrixType::H265;
            } break;
            default:
                LOG(ERROR) << "Invalid stream type, only support h264 | h265";
                vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                return;
            }

            MatrixAlign align;
            align.mode_ = MatrixAlignMode::MEMORY;
            align.mem_ = 1;
            align.stride_ = {1, 1};

            cv::Size size(dataSize, 1);

            MatrixProperty prop(VegaDataType::CHAR);
            if (DG_OK != prop.create(type, size, align)) {
                LOG(ERROR) << "MatrixProperty create failed";
                vencCallback_(nullptr, DG_ERR_VENC_FAIL);
            } else {
                auto dst = std::make_shared<Matrix>();
                if (DG_OK != dst->create(prop, align.mem_)) {
                    LOG(ERROR) << "Matrix create failed";
                    vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                    return;
                }

                // copy output to host memory
                auto dataHost = dst->memory()->blob();
                auto aclRet = aclrtMemcpy(dataHost, dataSize, dataDev, dataSize, ACL_MEMCPY_DEVICE_TO_HOST);
                if (aclRet != ACL_SUCCESS) {
                    LOG(ERROR) << "acl memcpy data to host failed, dataSize:" << dataSize << ", ret = " << aclRet;
                    vencCallback_(nullptr, DG_ERR_VENC_FAIL);
                } else {
                    vencCallback_(dst, DG_OK);
                }
            }
        }

        int AclVencProc::CheckResizeRatio(const float &resizeRatio) {
            if (resizeRatio < MIN_RESIZE_SCALE || resizeRatio > MAX_RESIZE_SCALE) {
                LOG(ERROR) << "Resize scale should be in range [1/32, 16], which is " << resizeRatio;
                return -1;
            }
            return 0;
        }

        void AclVencProc::SetDvppPicDesc(const DvppDataInfo &info, acldvppPicDesc *picDesc) {
            (void)acldvppSetPicDescData(picDesc, info.data);
            (void)acldvppSetPicDescSize(picDesc, info.dataSize);
            (void)acldvppSetPicDescFormat(picDesc, info.format);
            (void)acldvppSetPicDescWidth(picDesc, info.width);
            (void)acldvppSetPicDescHeight(picDesc, info.height);
            (void)acldvppSetPicDescWidthStride(picDesc, info.strideW);
            (void)acldvppSetPicDescHeightStride(picDesc, info.strideH);
        }

        int AclVencProc::VpcResize(const DvppDataInfo &input, DvppDataInfo &output) {
            acldvppPicDesc *inDesc = acldvppCreatePicDesc();
            if (inDesc == nullptr) {
                LOG(ERROR) << "Failed to create dvpp input Desc";
                return -1;
            }
            std::shared_ptr<acldvppPicDesc> inDescSP(inDesc, g_picDescDeleter);

            acldvppPicDesc *outDesc = acldvppCreatePicDesc();
            if (outDesc == nullptr) {
                LOG(ERROR) << "Failed to create dvpp output Desc";
                return -1;
            }
            std::shared_ptr<acldvppPicDesc> outDescSP(outDesc, g_picDescDeleter);

            acldvppResizeConfig *resizeCfg = acldvppCreateResizeConfig();
            if (resizeCfg == nullptr) {
                LOG(ERROR) << "Failed to create dvpp resize config";
                return -1;
            }
            std::shared_ptr<acldvppResizeConfig> resizeCfgSP(resizeCfg, g_resizeCfgDeleter);

            // Set dvpp pic desc info of input image
            SetDvppPicDesc(input, inDesc);

            // Set dvpp pic desc info of output image
            SetDvppPicDesc(output, outDesc);
            acldvppSetResizeConfigInterpolation(resizeCfg,0);
            auto ret = acldvppVpcResizeAsync(dvppChannelDesc_, inDesc, outDesc, resizeCfg, aclStream_);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to resize asynchronously, ret = " << ret << ".";
                return -1;
            }
          
            ret = aclrtSynchronizeStream(aclStream_);
            if (ret != ACL_SUCCESS) {
                LOG(ERROR) << "Failed to synchronize stream, ret = " << ret << "."<<" input w "<<input.width
                <<", h "<<input.height<<",sw "<<input.strideW<<",sh "<<input.strideH<<
                ";out w "<<output.width<<",h "<<output.height<<",sw "<<output.strideW<<",sh "<<output.strideH;
                return -1;
            }
            return 0;
        }

    }
}

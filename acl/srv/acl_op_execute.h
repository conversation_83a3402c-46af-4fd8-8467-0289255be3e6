/* Last modified on 2000-01-01 00:00:00 */
/*
 * Copyright(C) 2022. Deepglint Technologies Co.,Ltd. All rights reserved.
 *  by <PERSON><PERSON><PERSON><PERSON><PERSON>@deepglint.com
 */
#ifndef VEGA_ACL_OP_EXECUTE_H_
#define VEGA_ACL_OP_EXECUTE_H_
#include <cstdio>
#include <vector>
#include "vega_sdk_config.h"
#include "acl/acl.h"
#include "utils/acl_memory.h"
#include "station/block_queue.h"
#include <unistd.h>
#include <dlfcn.h>
#include <fstream>
#include <iostream>
namespace vega {
    extern sdk_config::Platform vegaGetPlatform();
    namespace acl {
        static void empty_dladdr(){

        }
        class AclOpExecute{
        public:
            DgError executeOp(std::vector<int64_t> param_shape , void *param_data,std::string op_type,bool first_run){
                auto it=model_name_id_.find(op_type);
                if(it!=model_name_id_.end()){
                    if(first_run){
                        return DG_OK;
                    }else{
                        return ModelInference(it->second, sizeof(unsigned long long), param_data,stream_,op_type);
                    }
                }
                LOG(ERROR)<<"Cannot find "<<op_type<<" ai_cpu_op model!";
                return DG_ERR_HW_FAILURE;
            }
            void printAicpuInfo(std::string op_type)
            {
                auto host_data = std::make_shared<MemBase>();
                if (DG_OK == host_data->create(output_size_)){
                    if (ACL_SUCCESS == aclrtMemcpy(host_data->blob(), output_size_, output_data_->blob(),
                                                    output_size_, ACL_MEMCPY_DEVICE_TO_HOST)){
                        std::string version((char *)host_data->blob());
                        LOG(ERROR) << op_type << " aicpu op info:" << version;
                        // Expected version
                        std::string CropResizeVersion("VegaCropResize version 1.3");
                        std::string ssdPosVersion("vega ssd post version 1.3.2");
                        std::string opticalFlowVersion("VegaOpticalFlow version 1.0");
                        std::string transformVersion("vega Transform version 1.0.1");
                        std::string absDiffVersion("VegaAbsDiff version 1.0");
                        if(!version.empty()){
                            // incompatible
                            if(version.compare(CropResizeVersion)!=0&&version.compare(ssdPosVersion)!=0&&version.compare(opticalFlowVersion)!=0&&version.compare(transformVersion)!=0&&version.compare(absDiffVersion)!=0){
                                LOG(FATAL)<<"aicpu op version has to be updated." << " Found version: " << version;
                            }
                        }
                    }
                }
            }
            DgError loadModel(std::string modelPath,aclrtStream stream,uint32_t &modelId,std::string op_type)
            {
                aclError ret=ACL_ERROR_NONE; 
                if (is_cann70_mode_) {
                    std::ifstream file(modelPath, std::ios::binary | std::ios::ate);
                    if(!file.is_open()){
                        LOG(ERROR)<<"can not open "<<modelPath;
                        return DG_ERR_HW_FAILURE;
                    }
                    file.seekg(0, file.end);
                    uint32_t fileSize = file.tellg();
                    std::shared_ptr<char> buffer=std::shared_ptr<char>(new char[fileSize], [=](char *data){delete [] data;});    
                    file.seekg(0, file.beg);
                    
                    file.read(buffer.get(), fileSize);
                    if (file.gcount() != fileSize) {
                        LOG(ERROR) << "Failed to read entire file of "<<modelPath;
                        file.close();
                        return DG_ERR_HW_FAILURE;
                    }
                    file.close();
                    ret = aclopLoad(buffer.get(), fileSize);
                }else {
                    ret = aclmdlLoadFromFile(modelPath.c_str(),&modelId);
                }
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR)<<"Load model from file "<<modelPath<<" failed, for " << ret <<".";
                    return DG_ERR_HW_FAILURE;
                }
                auto intput_data = std::make_shared<AclMemDvpp>();
                if(DG_OK!=intput_data->create(sizeof(unsigned long long))){
                    LOG(ERROR)<<"fail to create  AclMemDvpp";
                    return DG_ERR_HW_FAILURE;
                }
                ret=aclrtMemsetAsync(intput_data->blob(), intput_data->size(), 0, intput_data->size(),stream);
                ret=aclrtMemsetAsync(output_data_->blob(), output_data_->size(), 0, output_data_->size(),stream);
                if(ret!=ACL_SUCCESS){
                    LOG(ERROR) <<"Fail to aclrtMemsetAsync";
                    return DG_ERR_HW_FAILURE;
                }

                ModelInference(modelId, intput_data->size(),intput_data->blob(),stream,op_type);
                printAicpuInfo(modelPath);
                return DG_OK;
            }
            aclmdlDataset *CreateAndFillDataset(void * bufs, size_t sizes)
            {
                aclError ret = ACL_ERROR_NONE;
                aclmdlDataset *dataset = aclmdlCreateDataset();
                if (dataset == nullptr)
                {
                    LOG(ERROR)<<"ACL_ModelInputCreate failed.";
                    return nullptr;
                }
                aclDataBuffer *data = aclCreateDataBuffer(bufs, sizes);
                if (data == nullptr)
                {
                    DestroyDataset(dataset);
                    LOG(ERROR)<<"aclCreateDataBuffer failed.";
                    return nullptr;
                }
                ret = aclmdlAddDatasetBuffer(dataset, data);
                if (ret != ACL_ERROR_NONE)
                {
                    DestroyDataset(dataset);
                    LOG(ERROR)<< "ACL_ModelInputDataAdd failed, ret= "<< ret ;
                    return nullptr;
                }
                return dataset;
            }
            
            void DestroyDataset(aclmdlDataset *dataset)
            {
                if (dataset != nullptr) {
                    for (size_t i = 0; i < aclmdlGetDatasetNumBuffers(dataset); i++) {
                        aclDataBuffer* dataBuffer = aclmdlGetDatasetBuffer(dataset, i);
                        if (dataBuffer != nullptr) {
                            aclDestroyDataBuffer(dataBuffer);
                            dataBuffer = nullptr;
                        }
                    }
                    aclmdlDestroyDataset(dataset);
                    dataset = nullptr;
                }
            }
            DgError ModelInference(uint32_t modelId, uint32_t input_size, void *input_data,aclrtStream stream,std::string op_type){
                aclError ret;
                if(is_cann70_mode_){
                    std::shared_ptr<aclDataBuffer> inputBufs= std::shared_ptr<aclDataBuffer>(aclCreateDataBuffer(input_data, input_size), [=](aclDataBuffer *data) {
                            if (data != nullptr)
                                aclDestroyDataBuffer(data);
                        });
                    std::shared_ptr<aclDataBuffer> outputBufs= std::shared_ptr<aclDataBuffer>(aclCreateDataBuffer(output_data_->blob(), output_size_), [=](aclDataBuffer *data) {
                            if (data != nullptr)
                                aclDestroyDataBuffer(data);
                        }); 
                    aclDataBuffer *input[1]{inputBufs.get()};
                    aclDataBuffer *output[1]{outputBufs.get()};
                    ret=aclopExecuteV2(op_type.c_str(),
                        1,descInputs_,
                        input,
                        1,
                        descOutputs_,
                        output,
                        opAttr_,
                        stream);
                    if (ret != ACL_SUCCESS) {
                        LOG(ERROR) << " Failed to "<<op_type<<" aclopExecuteV2, ret " << ret;
                        consecutive_fail_times_++;
                        if (consecutive_fail_times_ > 10) {
                            LOG(FATAL) << "There may be a problem with aicpu and it must be restarted";
                        }
                        return DG_ERR_HW_FAILURE;
                    }
                    ret = aclrtSynchronizeStream(stream);
                } else {
                    std::shared_ptr<aclmdlDataset> input
                      = std::shared_ptr<aclmdlDataset>(CreateAndFillDataset(input_data, input_size), [=](aclmdlDataset *data) {
                            if (data != nullptr)
                                DestroyDataset(data);
                        });
                    std::shared_ptr<aclmdlDataset> output
                      = std::shared_ptr<aclmdlDataset>(CreateAndFillDataset(output_data_->blob(), output_size_), [=](aclmdlDataset *data) {
                            if (data != nullptr)
                                DestroyDataset(data);
                        });
                    ret = aclmdlExecuteAsync(modelId, input.get(), output.get(), stream);
                    if (ret != ACL_SUCCESS) {
                        LOG(ERROR) << " Failed to ai_cpu_op aclmdlExecuteAsync, ret " << ret;
                        consecutive_fail_times_++;
                        if (consecutive_fail_times_ > 10) {
                            LOG(FATAL) << "There may be a problem with aicpu and it must be restarted";
                        }
                        return DG_ERR_HW_FAILURE;
                    }
                    ret = aclrtSynchronizeStream(stream);
                }
                if (ret != ACL_SUCCESS){
                    LOG(ERROR) << " Failed to ai_cpu_op aclrtSynchronizeStream ,ret "<<ret;
                    consecutive_fail_times_++;
                    if(consecutive_fail_times_>10){
                        LOG(FATAL)<<"There may be a problem with aicpu and it must be restarted";
                    }
                    return DG_ERR_HW_FAILURE;
                }
                consecutive_fail_times_=0;
                return DG_OK;
            }

            AclOpExecute() {
                is_cann70_mode_=true;
                aclDataType dataType = ACL_UINT8;
                aclFormat format = ACL_FORMAT_ND;
                int numDims =2;
                int64_t dims[2]; 
                dims[0]=1;
                dims[1]=8;
                descInputs_[0]=aclCreateTensorDesc(dataType,numDims,dims,format);
                output_size_=256;
                dims[1]=output_size_;
                descOutputs_[0]=aclCreateTensorDesc(dataType,numDims,dims,format);
                opAttr_ = aclopCreateAttr();
                auto r_acl = aclrtCreateStream(&stream_);
                if(r_acl != ACL_ERROR_NONE) {
                    LOG(FATAL) << "Fail to create stream, errorCode is  " << static_cast<int32_t>(r_acl);
                }
                output_data_ = std::make_shared<AclMemDvpp>();
                if(DG_OK!=output_data_->create(output_size_)){
                    LOG(ERROR)<<"fail to create  AclMemDvpp";
                    output_size_=0;
                }
                Dl_info dl_info;
                dladdr((void *)empty_dladdr,&dl_info);
                std::string libvegaPath=(std::string)dl_info.dli_fname;
                std::size_t post=libvegaPath.find_last_of("/")+1;
                std::string libPath=libvegaPath.substr(0,post);
                std::map<std::string, uint32_t> model_name_id{{"VegaSSDPost",0},{"VegaKeyPoint",0},
                                                                {"VegaCropResize",0},{"VegaTransform",0},{"VegaOpticalFlow",0},{"VegaAbsDiff",0}};
                 
                if(libPath.empty()){
                    LOG(FATAL)<<"aicpu_model_path is empty!";
                }else{
                    std::map<std::string,uint32_t>::iterator it;
                    std::string soc_name="A310";
                    std::string acl_soc_name=aclrtGetSocName();
                    if(acl_soc_name.compare("Ascend710")==0 || acl_soc_name.compare("Ascend310P3")==0|| acl_soc_name.compare("Ascend310P1")==0){
                        soc_name="A710";
                    }
                    for(it=model_name_id.begin();it!=model_name_id.end();++it){
                        auto ret=loadModel(libPath+"/aicpu_om/"+soc_name+"_"+it->first+".om",stream_,it->second,it->first);
                        if(ret != DG_OK)
                            LOG(FATAL)<<"Fail to loadModel "<<libPath+"/aicpu_om/"+soc_name+"_"+it->first+".om";
                    }
                    model_name_id_=model_name_id;
                }
            }
            ~AclOpExecute() {
                std::map<std::string,uint32_t>::iterator it;
                for(it=model_name_id_.begin(); it!=model_name_id_.end(); ++it){
                    if(it->second != 0){
                        aclmdlUnload(it->second);
                    }
                }
                aclrtDestroyStream(stream_);
                aclDestroyTensorDesc(descInputs_[0]);
                aclDestroyTensorDesc(descOutputs_[0]);
                aclopDestroyAttr(opAttr_);
            }
            static void aicpu_engine_init()
            {  
                sdk_config::Platform platform=vegaGetPlatform();
                int engine_num = sdk_config::get_cfg<int>("aicpu_engine_num",3,platform);
                LOG(ERROR)<<"aicpu_engine_num is "<<engine_num;
                AclOpExecute::QueueInit(engine_num);
            }
            static BlockQueue<std::shared_ptr<AclOpExecute>> AclOpExecuteQ_;
            static std::shared_ptr<AclOpExecute> pop() {
                static std::atomic<int> wait_num;
                wait_num++;
                auto num=wait_num.load();
                if(num>7){
                    if(num>13){
                        LOG(WARNING)<<" waiting for aicpu_process number is "<<num;
                    }else {
                        LOG_EVERY_N(WARNING,50)<<" waiting for aicpu_process number is "<<num;
                    }
                }
                auto op=AclOpExecuteQ_.pop();
                wait_num--;
                return op;
            }
            static void push(std::shared_ptr<AclOpExecute> exec){
                AclOpExecuteQ_.push(exec);
            }
            static void QueueInit(unsigned int count=1){
                 for(unsigned int i =0; i<count; i++){
                    auto exec = std::make_shared<AclOpExecute>();
                    AclOpExecuteQ_.push(exec); 
                 }
            }
            static void clear(){
                while(!AclOpExecuteQ_.empty()){
                    auto op=AclOpExecuteQ_.popWait();
                }
            }
            private : 
            int output_size_=256;
            std::map<std::string,uint32_t> model_name_id_;
            aclrtStream stream_;
            int consecutive_fail_times_=0;
            bool is_cann70_mode_=true;
            aclTensorDesc *descInputs_[1];
            aclTensorDesc *descOutputs_[1];
            aclopAttr *opAttr_;;
            AclMemDvppSP output_data_ = std::make_shared<AclMemDvpp>();
        };
    }
}
#endif

#ifndef VEGA_DVPP_ENGINE_H_
#define VEGA_DVPP_ENGINE_H_

#include "image_engine.h"
#include "utils/acl_image.h"
#include "utils/acl_memory.h"
#include "acl/ops/acl_dvpp.h"
#include "dvpp_common.h"
#include "include/station/block_queue.h"
#include "vega_sdk_config.h"
#if CANN50
#include "acl_op_engine.h"
#endif 
#define DVPP_MIN_W 10 
#define DVPP_MIN_H 6
#define DVPP_MAX_W 8192
#define DVPP_MAX_H 8192
namespace vega {
    namespace acl {
        class DvppProcess{
            typedef struct cropResizeParam
            {
                std::shared_ptr<acldvppPicDesc> vpcInputDesc;
                std::shared_ptr<acldvppPicDesc> vpcOutputDesc;
                std::shared_ptr<acldvppRoiConfig> cropArea;
                std::shared_ptr<acldvppRoiConfig> pasteArea;
                AclImageSP input;
                AclImageSP output;
                bool outputChange; 
                cv::Rect iRoi;
                cv::Rect oRoi;
            }CropResizeParam;

            typedef enum{
                A310,
                A710
            }SocName;  

        public:
            DvppProcess() {
                bInit=false;
                if(DG_OK!=vegaCreateStream(&dvppStream)){
                    LOG(ERROR)<<"Fail to dvpp vegaCreateStream!";
                }
                soc_name_=A310;
                use_aicpu_op_engine_=0;
#if CANN50
                use_aicpu_op_engine_=sdk_config::get_cfg<int>("use_aicpu_op_engine", 0, sdk_config::acl);
                std::string acl_soc_name=aclrtGetSocName();
                if(acl_soc_name.compare("Ascend310")==0){
                    soc_name_=A310;
                }else if(acl_soc_name.compare("Ascend710")==0 || acl_soc_name.compare("Ascend310P3")==0
                || acl_soc_name.compare("Ascend310P1")==0){
                    soc_name_=A710;
                }
#endif
            };
            ~DvppProcess() {
                vegaDestroyStream(dvppStream);
            };
#if CANN50
            DgError cropResize( MatrixSPV &vInput,MatrixSPV &vOutput){
                if(vInput.size()<=1){
                    return DG_ERR_NOT_SUPPORTED;
                }
                // for(unsigned int i=0;i<vInput.size();i++){
                //     if((vInput[i]->soureType()!=SoureType::video)){
                //         return DG_ERR_NOT_SUPPORTED;
                //     }
                // }
                auto srcBatchPicDescs= std::shared_ptr<acldvppBatchPicDesc>(acldvppCreateBatchPicDesc(vInput.size()),[&](void* p){
                        acldvppDestroyBatchPicDesc((acldvppBatchPicDesc *)p);
                    });
                auto dstBatchPicDescs= std::shared_ptr<acldvppBatchPicDesc>(acldvppCreateBatchPicDesc(vOutput.size()),[&](void* p){
                        acldvppDestroyBatchPicDesc((acldvppBatchPicDesc *)p);
                    });
                std::vector<CropResizeParam> vParam;
                vParam.resize(vInput.size());
                std::vector<acldvppRoiConfig *> cropAreas;
                std::vector<acldvppRoiConfig *> pasteAreas;
                cropAreas.resize(vInput.size());
                pasteAreas.resize(vInput.size());
                for(u_int i=0;i<vInput.size();i++){
                    acldvppPicDesc *inDesc=acldvppGetPicDesc(srcBatchPicDescs.get(), i);
                    acldvppPicDesc *outDesc=acldvppGetPicDesc(dstBatchPicDescs.get(), i);
                    auto ret=prepare(vInput[i],vOutput[i],vParam[i],inDesc,outDesc);
                    if(DG_OK != ret){
                        if(DG_ERR_NOT_SUPPORTED != ret)
                            LOG(ERROR)<<"Fail to prepare cropResize";
                        return ret;
                    }
                    cropAreas[i]=vParam[i].cropArea.get();
                    pasteAreas[i]=vParam[i].pasteArea.get();
                }
                std::vector<uint32_t> roiNums;
                roiNums.resize(vInput.size(),1);
                aclError ret=acldvppVpcBatchCropResizePasteAsync(dvppChannelDesc_.get(), srcBatchPicDescs.get(),
                    roiNums.data(),vInput.size(),dstBatchPicDescs.get(),cropAreas.data(),pasteAreas.data(),
                    resizeCfg_.get(),dvppStream);
                if(ret != ACL_SUCCESS){
                    LOG(ERROR)<<"Fail to acldvppVpcBatchCropResizePasteAsync,erro "<<ret;
                }else{
                    ret = aclrtSynchronizeStream(dvppStream);
                    if (ret != ACL_SUCCESS) {
                        LOG(ERROR)<< "aclrtSynchronizeStream failed, errorCode " <<  static_cast<int32_t>(ret);
                    }
                }
                if(ret != ACL_SUCCESS){
                    return DG_ERR_HW_FAILURE;
                }
                for(u_int i=0;i<vOutput.size();i++){
                    if(DG_OK != outputPro(vOutput[i], vParam[i])){
                        LOG(ERROR)<<"Index "<<i<<"; fail to outputPro!";
                        return DG_ERR_HW_FAILURE;
                    }
                }
                return DG_OK;
            }
#endif
            DgError cropResize( MatrixSP &input,MatrixSP &output,ProcessMatrixParam &info){
                CropResizeParam param;
                auto ret=prepare(input,output,param);
                if(DG_OK != ret){
                    if(DG_ERR_NOT_SUPPORTED != ret)
                        LOG(ERROR)<<"Fail to prepare cropResize";
                    return ret;
                }
                ret=cropAndPaste(param.vpcInputDesc.get(),param.vpcOutputDesc.get(),param.cropArea.get(),param.pasteArea.get(),dvppStream);
                if(ret!=DG_OK){
                    LOG(ERROR) <<info.Info()<<"; Dvpp fail to cropResize ! "<<"input size "<<input->size()<<" stride "<<input->stride()<<" aligned in roi "<< param.iRoi
                    <<",src in roi "<<input->roi()<<", aligned out roi: " << param.oRoi<<", src out roi "<< output->roi()<<", output size " <<output->size()<<" stride "<<output->stride();
                    return DG_ERR_ABORTED;
                }
                return outputPro(output, param);
            }
            DgError outputPro(MatrixSP &output, CropResizeParam param)
            {

#if CANN50 
                if(output->type()==MatrixType::BGRPacked && soc_name_!=A710){
                    float mean[]={0,0,0};
                    auto op_in=std::dynamic_pointer_cast<Matrix>(param.output);
                    auto ret=aclOpProcess_.cropResize(op_in,output,mean, 1,(aclrtStream)dvppStream);
                    if(ret != DG_OK){
                        LOG(ERROR)<<"Fail to AclOpProcess.cropResize";
                        return DG_ERR_ABORTED;
                    }
                    param.outputChange=false;
                }
#endif
                if(param.outputChange){
                    auto acl_output=std::dynamic_pointer_cast<AclImage>(output);
                    auto ret=acl_output->copy(param.output);
                    if(ret!=DG_OK){
                        LOG(ERROR) << " Faile to copy !";
                        return DG_ERR_ABORTED;
                    }
                }
                output->setRoi(param.oRoi);
                return DG_OK;
            }
            DgError prepare( MatrixSP &input,MatrixSP &output,CropResizeParam &param,acldvppPicDesc *inDesc=nullptr,acldvppPicDesc *OutDesc=nullptr){
                if(!bInit){
                    initResource();
                    if(!bInit){
                        LOG(ERROR) <<"DvppProcess hasn't initalized!";
                        return DG_ERR_INIT_FAIL;
                    }
                }
                if(input->type()!=MatrixType::BGRPacked && input->type()!=MatrixType::NV12){
                    //LOG(INFO)<<"dvpp cropResize don't support input type of"<<matrix_type_str(input->type());
                    return DG_ERR_NOT_SUPPORTED;
                }
                if(output->type()!=MatrixType::BGRPacked && output->type()!=MatrixType::NV12){
                    //LOG(INFO)<<"dvpp cropResize don't support output type of "<<matrix_type_str(output->type());
                    return DG_ERR_NOT_SUPPORTED;
                }
                if(output->type()==MatrixType::BGRPacked)
                    if(!use_aicpu_op_engine_&& soc_name_!=A710)
                        return DG_ERR_NOT_SUPPORTED;

                auto acl_output=std::dynamic_pointer_cast<AclImage>(output);
                if(!acl_output){
                    //LOG(INFO)<<"output is not aclImage";
                    return DG_ERR_NOT_SUPPORTED;
                }
                if(input->pixel().width<DVPP_MIN_W||input->pixel().height<DVPP_MIN_H
                    ||input->pixel().width>DVPP_MAX_W||input->pixel().height>DVPP_MAX_H)
                {
                    LOG(ERROR)<<"input stride " << input->pixel()<<" ,beyond the capability of DVPP!";
                    return DG_ERR_INVALID_PARAM;
                }
                bool change;
                auto ret=CreatFitMatrix(input,param.input,change,input->type());
                if(ret!=DG_OK){
                    LOG(ERROR)<<"Fail to creatFitMatrix";
                    return ret;
                }
                if(soc_name_==A710){
                    //ACL710 only support YUV or bgr output
                    ret=CreatFitMatrix(output,param.output,param.outputChange,output->type(),false); 
                }else{
                    ret=CreatFitMatrix(output,param.output,param.outputChange,MatrixType::NV12,false); //ACL310 only support YUV output
                }
                if(ret!=DG_OK){
                    LOG(ERROR)<<"Fail to creatFitMatrix";
                    return ret;
                }
                if(!inDesc){
                    param.vpcInputDesc= std::shared_ptr<acldvppPicDesc>(setPicDesc(param.input),[&](void* p){
                        destroyResizeResource((acldvppPicDesc *)p);
                    });
                }else {
                    param.vpcInputDesc= std::shared_ptr<acldvppPicDesc>(setPicDesc(param.input,inDesc),[&](void* p){
                    });
                }
                if(param.vpcInputDesc.get() == nullptr){
                    LOG(ERROR) << " create vpcInputDesc failed ";
                    return DG_ERR_INVALID_PARAM;
                }

                if(!OutDesc){
                    param.vpcOutputDesc= std::shared_ptr<acldvppPicDesc>(setPicDesc(param.output),[&](void* p){
                        destroyResizeResource((acldvppPicDesc *)p);
                    });
                }else{
                    param.vpcOutputDesc= std::shared_ptr<acldvppPicDesc>(setPicDesc(param.output,OutDesc),[&](void* p){
                    });
                }
                if(param.vpcOutputDesc.get() == nullptr){
                    LOG(ERROR) << " create vpcOutputDesc failed ";
                    return DG_ERR_INVALID_PARAM;
                }
                param.cropArea = std::shared_ptr<acldvppRoiConfig>(createCropArea(param.input,param.iRoi),[&](void *p){
                    destroyRoiConfig((acldvppRoiConfig *)p);
                });
                if(param.cropArea.get() == nullptr){
                    LOG(ERROR) << " Faile to createCropArea !";
                    return DG_ERR_INVALID_PARAM;
                }
                param.pasteArea = std::shared_ptr<acldvppRoiConfig>(createPasteArea(param.output,param.oRoi),[&](void *p){
                    destroyRoiConfig((acldvppRoiConfig *)p);
                });
                if(param.pasteArea.get() == nullptr){
                    LOGFULL << " Faile to createPasteArea !";
                    return DG_ERR_NOT_SUPPORTED;
                }
                if(param.oRoi.area()!=param.output->size().area()){
                    if(output->type()==MatrixType::NV12){
		                //auto acl_erro=aclrtMemsetAsync(param.output->memory()->blob(), param.output->MemSize()/3*2, 0, param.output->MemSize()/3*2,dvppStream);
		                auto acl_erro=aclrtMemset(param.output->memory()->blob(), param.output->MemSize()/3*2, 0, param.output->MemSize()/3*2);
			            if(acl_erro!=ACL_SUCCESS){
			                LOG(ERROR) <<"Fail to aclrtMemsetAsync mem_size "<<param.output->MemSize()<<", erro code "<<acl_erro;
			            }
		                //acl_erro=aclrtMemsetAsync(param.output->memory()->blob()+param.output->MemSize()/3*2, param.output->MemSize()/3, 128, param.output->MemSize()/3,dvppStream);
		                acl_erro=aclrtMemset(param.output->memory()->blob()+param.output->MemSize()/3*2, param.output->MemSize()/3, 128, param.output->MemSize()/3);
			            if(acl_erro!=ACL_SUCCESS){
			                LOG(ERROR) <<"Fail to aclrtMemsetAsync mem_size "<<param.output->MemSize()<<", erro code "<<acl_erro;
			            }
		            }else {
		                //auto acl_erro=aclrtMemsetAsync(param.output->memory()->blob(), param.output->MemSize(), 0, param.output->MemSize(),dvppStream);
		                auto acl_erro=aclrtMemset(param.output->memory()->blob(), param.output->MemSize(), 0, param.output->MemSize());
			            if(acl_erro!=ACL_SUCCESS){
		                    LOG(ERROR) <<"Fail to aclrtMemsetAsync mem_size "<<param.output->MemSize()<<", erro code "<<acl_erro;
			            }
                    } 
                }
                return DG_OK;
            }


            /// @brief 
            /// @return 
            DgError initResource() {
                vegaSetCurrentContext();
                dvppChannelDesc_ = std::shared_ptr<acldvppChannelDesc>(acldvppCreateChannelDesc(),[&](acldvppChannelDesc *data){
                    if(data!= nullptr) {
                        if(bInit){
                            aclError ret = acldvppDestroyChannel(data);
                            if (ret != ACL_ERROR_NONE) {
                                LOG(ERROR) << "acldvppDestroyChannel failed, errorCode = " <<  static_cast<int32_t>(ret);
                            }
                        }
                        (void) acldvppDestroyChannelDesc(data);
                    }
                }
                );
                if (!dvppChannelDesc_) {
                    LOG(ERROR) << "acldvppCreateChannelDesc failed";
                    return DG_ERR_HW_FAILURE;
                }
#if CANN50
                acldvppSetChannelDescMode(dvppChannelDesc_.get(),DVPP_CHNMODE_VPC);
#endif
                aclError ret = acldvppCreateChannel(dvppChannelDesc_.get());
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR)<<"acldvppCreateChannel failed, errorCode  "<<  static_cast<int32_t>(ret);
                    return DG_ERR_HW_FAILURE;
                }
                resizeCfg_ = std::shared_ptr<acldvppResizeConfig>(acldvppCreateResizeConfig(),[&](acldvppResizeConfig *data){
                    if(data)
                        acldvppDestroyResizeConfig(data);
                });
                if (!resizeCfg_) {
                    LOG(ERROR) << "Failed to create dvpp resize config";
                    return DG_ERR_HW_FAILURE;
                }
                /*
                0：默认值。在昇腾310 AI处理器上，在昇腾910 AI处理器上，0表示华为自研的高阶滤波算法。在昇腾710 AI处理器上，设置为0时，系统内部也会自动采用1。
                1：业界通用的Bilinear算法（与OpenCV算法的计算过程类似）
                2：业界通用的Nearest neighbor 算法（与OpenCV算法的计算过程类似）
                3：业界通用的Bilinear算法（与Tensorflow算法的计算过程类似）。昇腾710 AI处理器不支持该值。
                4：业界通用的Nearest neighbor算法（与Tensorflow算法的计算过程类似）。昇腾710 AI处理器不支持该值。
                */
                interpolation_set_ =(int)sdk_config::get_cfg<int>("acl_resize_interpolation", 0, sdk_config::acl);
                acldvppSetResizeConfigInterpolation(resizeCfg_.get(), interpolation_set_);//defaul:Bilinear
                interpolation_=interpolation_set_;
                bInit=true;
                return DG_OK;
            }
        private:
            /**
             * set dvpp input data
             * @return
             */
            acldvppPicDesc *setPicDesc(MatrixSP input,acldvppPicDesc *vpcDesc=nullptr){
                if(!vpcDesc){
                    vpcDesc = acldvppCreatePicDesc();
                    LOGFULL << "Matrix size " << input->size() << " stride " << input->stride();
                    if (!vpcDesc){
                        LOG(ERROR) << "acldvppCreatePicDesc failed";
                        return nullptr;
                    }
                }
                uint32_t outBufferSize = input->stride().area() ;
                if(input->type()==MatrixType ::BGRPacked) {
                    (void) acldvppSetPicDescFormat(vpcDesc, PIXEL_FORMAT_BGR_888);
                } else if(input->type()==MatrixType :: NV12){
                    (void)acldvppSetPicDescFormat(vpcDesc, PIXEL_FORMAT_YUV_SEMIPLANAR_420);
                    outBufferSize = input->stride().area()*3/2;
                } else{
                    destroyResizeResource(vpcDesc);
                    return nullptr;
                }
                int w=input->size().width;
                int sw=input->stride().width;
                if(w%2 != 0){
                    if(sw>w)
                        w++; 
                    else
                        w--;
                }
                (void)acldvppSetPicDescData(vpcDesc, input->data());
                (void)acldvppSetPicDescWidth(vpcDesc, w);
                (void)acldvppSetPicDescHeight(vpcDesc, input->stride().height);
                (void)acldvppSetPicDescWidthStride(vpcDesc, sw);
                (void)acldvppSetPicDescHeightStride(vpcDesc, input->stride().height);
                (void)acldvppSetPicDescSize(vpcDesc, outBufferSize);
                return vpcDesc;

            }
            cv::Rect fixRoi(MatrixSP &matrix,bool alignedBy2=true) {

                auto roi = matrix->roi();
                auto unit = UNIT(matrix->type());
                auto pixel = matrix->stride();
                pixel.width /= unit;
                if(roi.width<DVPP_MIN_W ){
                    int dif=DVPP_MIN_W-roi.width;
                    roi.width=DVPP_MIN_W ;
                    roi.x-=dif/2;
                }
               if(roi.height<DVPP_MIN_H ){
                    int dif=DVPP_MIN_H-roi.height;
                    roi.height=DVPP_MIN_H ;
                    roi.y-=dif/2;
                }
                if(alignedBy2){
                    if(roi.x % 2 != 0) { roi.x --; }
                    if(roi.y % 2 != 0) { roi.y --; }
                }
                if(roi.x<0){
                   roi.x=0; 
                }
                if(roi.y<0){
                   roi.y=0; 
                }

                if(alignedBy2){
                    if(roi.width % 2 != 0) {
                        roi.width++;
                    }
                    if(roi.height % 2 != 0) {
                        roi.height++;
                    }
                }
                if(roi.width > pixel.width-roi.x )
                    roi.width =pixel.width-roi.x;
                if(roi.height > pixel.height-roi.y) 
                    roi.width =pixel.height-roi.y;
                return roi;
            }

            acldvppRoiConfig* createCropArea(MatrixSP input,cv::Rect &iRoi){
                if(input->type()==MatrixType::BGRPacked && soc_name_ == A710){
                    iRoi = fixRoi(input,false);
                }else{
                    iRoi = fixRoi(input);
                }
                LOGFULL  << "IRoi: " << iRoi;
                if(iRoi.width<MinWidth_||iRoi.height<MinHeight_){
                    LOG(INFO)<<"roi limit is "<<MinWidth_<<"*"<<MinHeight_<<" ; roi "<<iRoi;
                }
                acldvppRoiConfig *cropArea = acldvppCreateRoiConfig(iRoi.x, iRoi.br().x - 1, iRoi.y , iRoi.br().y - 1);
                if(cropArea == nullptr){
                    LOG(ERROR)<<"acldvppCreateRoiConfig cropArea failed";
                }
                return cropArea;
            }

            acldvppRoiConfig *createPasteArea(MatrixSP output,cv::Rect &oRoi){
                if(output->type()==MatrixType::BGRPacked){
                    oRoi = fixRoi(output,false);
                }else{
                    oRoi = fixRoi(output);
                    if(oRoi.x % 16 != 0) {
                        int x=oRoi.x;
                        oRoi.x = oRoi.x/16 * 16;
                        float ratio=(float)(x-oRoi.x)/oRoi.width;   
                        if(ratio>0.05){
                            LOGFULL  << "ORoi: " << oRoi<<",discontact ratio "<<ratio;
                            return nullptr;
                        }
                    }
                    if(oRoi.width % 16 != 0) {
                        int w=oRoi.width;
                        oRoi.width = oRoi.width/16 * 16;
                        float ratio=(float)(w-oRoi.width)/w;
                        if(ratio>0.05){
                            LOGFULL  << "ORoi: " << oRoi<<",discontact ratio "<<ratio;
                            return nullptr;
                        }
                    }
                }
                oRoi = cv::Rect(oRoi.tl(), oRoi.size());
                LOGFULL  << "ORoi: " << oRoi;
                if(oRoi.width<MinWidth_||oRoi.height<MinHeight_){
                    LOG(ERROR)<<"roi limit is "<<MinWidth_<<"*"<<MinHeight_<<" ; roi "<<oRoi;
                    return nullptr;
                }
                acldvppRoiConfig *pasteArea = acldvppCreateRoiConfig(oRoi.x, oRoi.br().x - 1, oRoi.y , oRoi.br().y - 1);
                if(pasteArea == nullptr){
                    LOG(ERROR)<<"acldvppCreateRoiConfig pasteArea failed";
                }
                return pasteArea;
            }

            DgError cropAndPaste(acldvppPicDesc *vpcInputDesc,acldvppPicDesc *vpcOutputDesc,acldvppRoiConfig *cropArea,
                    acldvppRoiConfig *pasteArea,aclrtStream stream){
#if CANN50
                aclError ret = acldvppVpcCropResizePasteAsync(dvppChannelDesc_.get(), vpcInputDesc,
                                                  vpcOutputDesc, cropArea, pasteArea ,resizeCfg_.get(),stream);
#else
                aclError ret = acldvppVpcCropAndPasteAsync(dvppChannelDesc_.get(), vpcInputDesc,
                                                  vpcOutputDesc, cropArea, pasteArea ,stream);
#endif
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR)<< "acldvppVpcCropResizePasteAsync failed, errorCode " <<  static_cast<int32_t>(ret);
                    return DG_ERR_HW_FAILURE;
                }
                ret = aclrtSynchronizeStream(stream);
                if (ret != ACL_ERROR_NONE) {
                    LOG(ERROR)<< "aclrtSynchronizeStream failed, errorCode " <<  static_cast<int32_t>(ret);
                    return DG_ERR_HW_FAILURE;
                }
                return DG_OK;

            }

            void destroyResizeResource(acldvppPicDesc *vpcDesc){
                if (vpcDesc != nullptr) {
                    acldvppDestroyPicDesc(vpcDesc);
                }
            }

            void destroyRoiConfig(acldvppRoiConfig *roi){
                if(roi != nullptr){
                    (void)acldvppDestroyRoiConfig(roi);
                }
            };

            DgError CreatFitMatrix(MatrixSP input,AclImageSP &output,bool &change,MatrixType matrixType ,bool copy=true){
                output=std::dynamic_pointer_cast<AclImage>(input);
                change=false;
                if(!output||input->stride().width%ACL_IMAGE_ALIGN_W!=0||input->stride().height%ACL_IMAGE_ALIGN_H!=0
                ||matrixType!=input->type())
                {
                    change=true;
                    auto output_acl=std::make_shared<AclImage>();
                    int element_size=1;
                    cv::Size size=input->stride();
                    if(input->type()==MatrixType::BGRPacked){
                        element_size=3;
                        size.width/=3;
                    }
                    RawStream data = RawStream(input->data(), [=](void *data){});
                    bool isHostMem=false;
                    if(!output){
                        isHostMem=true;
                    }
                   if(matrixType!=input->type()){
                        copy=false;   
                   }
                   if(copy) {
                        auto ret = output_acl->create(input->type(), size, element_size, data, isHostMem);
                        if (ret != DG_OK) {
                            LOG(ERROR) << "Fail to create acl Matrix!";
                            return DG_ERR_ABORTED;
                        }
                    }else{
                        auto ret = output_acl->create(matrixType, VegaDataType::CHAR,size);
                        if (ret != DG_OK) {
                            LOG(ERROR) << "Fail to create acl Matrix!";
                            return DG_ERR_ABORTED;
                        }
                    }
                    output_acl->setRoi(input->roif(),true);
                    output_acl->setFrameId(input->frameId());
                    output_acl->setStreamId(input->streamId());
                    output=output_acl;
                }
                return DG_OK;
            }
        private:
            std::shared_ptr<acldvppChannelDesc> dvppChannelDesc_;
            std::shared_ptr<acldvppResizeConfig> resizeCfg_;
            bool bInit=false;
            const int MinWidth_=DVPP_MIN_W;
            const int MinHeight_=DVPP_MIN_H;
            VegaStream dvppStream;
            int use_aicpu_op_engine_=0;
            uint32_t interpolation_=0;
            uint32_t interpolation_set_=0;
#if CANN50 
            AclOpProcess aclOpProcess_;
#endif
            SocName soc_name_;
        };

        class DvppEngine : public ImageEngine {
        public:
            BlockQueue<std::shared_ptr<DvppProcess>> dvppProQ_;
            DvppEngine() : ImageEngine(10, "DVPP") {
                for(int i=0;i<10;i++) {
                    std::shared_ptr<DvppProcess> dvppProcess = std::make_shared<DvppProcess>();
                    dvppProQ_.push(dvppProcess);
                }
            }
            DgError process(VegaMatrixCmd cmd,
                            std::vector<bool> &marks,
                            MatrixSPV &inputs,
                            MatrixSPV &outputs,
                            VegaStream stream,
                            ProcessMatrixParam &param) override {
                if(cmd != VegaMatrixCmd::CROP_RESIZE){
                    return DG_ERR_NOT_SUPPORTED;
                }
                std::shared_ptr<DvppProcess> dvppProcess = dvppProQ_.pop();
                DgError ret = DG_OK;
                int cnt = 0;
                for(auto i = 0u; i < marks.size(); i++) {
                    if(!marks[i]) cnt++;
                }
                inc(cnt);
#if CANN50
                if((unsigned int)cnt==marks.size()){
                    ret=dvppProcess->cropResize(inputs,outputs);
                    if(DG_OK==ret){
                        for(u_int i = 0u; i < marks.size(); i++) marks[i] = true;
                        dvppProQ_.push(dvppProcess);
                        dec(cnt);
                        return DG_OK;
                    }else {
                        if(DG_ERR_NOT_SUPPORTED != ret)
                            LOG(ERROR)<<"Fail to dvpp batch cropResize";
                    }
                }
#endif
                ret = DG_OK;
                for(auto i=0u; i < inputs.size();i++){
                    DgError err;
                    if(marks[i]){
                        continue;
                    }
                    err = dvppProcess->cropResize(inputs[i], outputs[i],param);
                    cnt--;
                    dec();
                    if(err == DG_OK) {
                        marks[i] = true;
                    } else if(ret == DG_OK) {
                        ret = err;
                    }
                }
                dec(cnt);
                dvppProQ_.push(dvppProcess);
                return ret;
            }

        };


    }
}
#endif

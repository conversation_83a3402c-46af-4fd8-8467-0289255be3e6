#ifndef VEGA_ACL_VIDEO_DEVICE_ENCODER_H
#define VEGA_ACL_VIDEO_DEVICE_ENCODER_H

#include <iostream>
#include <memory>
#include <mutex>
#include <queue>
#include <atomic>
#include "error.h"
#include "vega_matrix.h"
#include "glog/logging.h"
#include "dg_base_types.h"
#include "utils/acl_memory.h"
#include "utils/acl_image.h"
#include "dvpp_common.h"
#include "common/Platform/processor/video_encoder_buffer.h"

namespace vega {
    namespace acl {

        class AclVencProc : public AclVencProcBase{
        public:
            AclVencProc() {
                threadId_=0;
            }
            ~AclVencProc() {
                DestroyVenc();
            }
            AclVencProc &operator=(const AclVencProc &) = delete;

            DgError Init(const aclrtStream &stream);

            DgError CreateVenc(MatrixType from, MatrixType to, const VencBuffer::VencParam &param, VencBuffer::VencCallback cb);
            DgError DestroyVenc(void);
            DgError EncodeVideo(MatrixSP input, int kIntv, bool forceIFrame, float resizeRatio, bool videoEos);

        protected:
            static void *AclReportThread_(void *arg);
            void *AclReportThread(void);
            static void vencCallback(acldvppPicDesc *input, acldvppStreamDesc *outputStreamDesc, void *userdata);

            DgError InitResource(uint64_t threadId, acldvppPixelFormat format, acldvppStreamFormat enType);
            DgError VencSendEosFrame(void);
            DgError FreeResource(void);

            void EncodeDone(const void *dataDev, uint32_t dataSize);
            void SetDvppPicDesc(const DvppDataInfo &info, acldvppPicDesc *picDesc);
            int CheckResizeRatio(const float &resizeRatio);
            int VpcResize(const DvppDataInfo &input, DvppDataInfo &output);

        private:
            bool bQuit_ = true;
            pthread_t threadId_ = 0;
	    VencBuffer::VencParam vencParam_;
            VencBuffer::VencCallback vencCallback_ = nullptr;
            cv::Size encSize = {0, 0};

            aclrtStream aclStream_ = nullptr;            
            acldvppPicDesc *inputPicputDesc_ = nullptr;
            aclvencChannelDesc *vencChannelDesc_ = nullptr;
            aclvencFrameConfig *vencFrameConfig_ = nullptr;
            acldvppPixelFormat picFormat_ = PIXEL_FORMAT_YUV_SEMIPLANAR_420;
            acldvppStreamFormat enType_ = H264_MAIN_LEVEL;
            acldvppChannelDesc *dvppChannelDesc_ = nullptr;
        };
    }
}


#endif
